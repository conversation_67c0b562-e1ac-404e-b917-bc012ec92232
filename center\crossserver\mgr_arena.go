package crossserver

import (
	"fmt"
	json "github.com/bytedance/sonic"
	"log"
	"master/center/conf"
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
	"math"
	"net/http"
	"sort"
	"sync"
)

const (
	TABLE_CROSS_SERVER_ARENA             = "tbl_crossserverarena"
	CROSS_SERVER_ARENA_SCORE_START       = 1000
	CROSS_SERVER_ARENA_TEAM_MAX          = 3
	CROSS_SERVER_ARENA_ENEMY_CHOOSE_LIST = 10  //对手备选数量，提高效率
	CROSS_SERVER_ARENA_RANK_MAX          = 100 //
)

const (
	RETCODE_DATA_CROSS_OK          = 0 //
	RETCODE_DATA_CROSS_PARAM_ERROR = 1 //参数错误
)

type lstCrossServerArenaRank []*CrossServerArenaUser

func (s lstCrossServerArenaRank) Len() int      { return len(s) }
func (s lstCrossServerArenaRank) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s lstCrossServerArenaRank) Less(i, j int) bool {
	if s[i].IsSign == s[j].IsSign {
		if s[i].Score == s[j].Score {
			return s[i].Uid < s[j].Uid
		} else {
			return s[i].Score >= s[j].Score
		}
	}
	return s[i].IsSign >= s[j].IsSign
}

type CrossServerArenaDB struct {
	Id         int    `json:"id"`
	Uid        int64  `json:"uid"`
	Periods    int    `json:"periods"`
	GroupId    int    `json:"groupid"`
	SvrId      int    `json:"svrid"`
	Info       string `json:"info"`
	FightInfos string `json:"fightinfos"`

	info *CrossServerArenaUser
	//fightInfos []*core.JS_FightInfo  //优化 这个字段干掉 节省内存空间
	db.DataUpdate
}

type CrossServerArenaUser struct {
	Uid        int64                          `json:"uid"`
	SvrId      int                            `json:"svrid"`
	SvrName    string                         `json:"svrname"`
	UName      string                         `json:"uname"`
	UnionName  string                         `json:"unionname"`
	Score      int                            `json:"score"`
	ScoreLv    int                            `json:"scorelv"`
	RankPos    int                            `json:"rankpos"`
	Level      int                            `json:"level"`
	Vip        int                            `json:"vip"`
	Icon       int                            `json:"icon"`
	Portrait   int                            `json:"portrait"`
	Title      int                            `json:"title"`
	Fight      int64                          `json:"fight"`
	Robot      int                            `json:"robot"`
	ArenaFight []*model.CrossServerArenaFight `json:"arenafight"` //战报集
	CantList   map[int64]int                  `json:"-"`
	IsSign     int                            `json:"issign"` //是否正式参赛
}

type CrossServerArenaMgr struct {
	Mu                   *sync.RWMutex
	CrossServerArenaInfo map[int]*CrossServerArenaInfo //key: group
	JJCRobotConfig       []*JJCRobotConfig
	RecordId             int64
}

type CrossServerArenaInfo struct {
	groupId  int
	periods  int
	rankInfo []*CrossServerArenaUser       //排行数据    key:rank
	db_list  map[int64]*CrossServerArenaDB //数据存储
	Locker   *sync.RWMutex
}
type MasterLog struct {
	Id    int    `json:"id"`
	LogId int    `json:"logid"`
	Info  string `json:"info"`
}
type RewardList struct {
	ServerId   int           `json:"serverid"`
	Period     int           `json:"period"`
	RewardInfo []*RewardInfo `json:"rewardinfo"`
}
type RewardInfo struct {
	Uid     int64 `json:"uid"`
	RankPos int   `json:"rankpos"`
}

type RewardServerInfo struct {
	ServerId int `json:"serverid"`
	RankPos  int `json:"rankpos"`
}

type PeakRewardInfo struct {
	PersonReward *RewardList
	ServerReward *RewardServerInfo
}

type JJCRobotConfig struct {
	Id         int     `json:"id"`
	Type       int     `json:"type"`
	Jjcscore   int     `json:"jjcscore"`
	Jjcclass   int     `json:"jjcclass"`
	Jjcdan     int     `json:"jjcdan"`
	Category   int     `json:"jjccategory"`
	Teamnum    int     `json:"teamnum"`
	Rank1      int     `json:"jjcrankmin" trim:"0"`
	Rank2      int     `json:"jjcrankmax" trim:"0"`
	Name       string  `json:"name"`
	Head       int     `json:"head"`
	MonsterId  int     `json:"Monsterid"`
	NpcLv      []int   `json:"herolv"`
	Level      int     `json:"robotlevel"`
	NpcQuality int     `json:"npcquality"`
	NpcStar    []int   `json:"robotstar"`
	Fight      []int64 `json:"showfight"`
	//MaxFight   int    `json:"showfight1"`
	Hero []int `json:"optionhero"`
	//Arms       []int     `json:"armtype"`
	Hydra      int       `json:"hydraoption"` //! 巨兽
	Rage       int       `json:"rageoption"`
	BaseTypes  []int     `json:"base_type"`
	BaseValues []float64 `json:"base_value"`
}

var crossServerArenaMgr *CrossServerArenaMgr = nil

// 跨服竞技场 // 苍穹战场 决战空岛 全力对决
func GetCrossServerArenaMgr() *CrossServerArenaMgr {
	if crossServerArenaMgr == nil {
		crossServerArenaMgr = new(CrossServerArenaMgr)
		crossServerArenaMgr.CrossServerArenaInfo = make(map[int]*CrossServerArenaInfo)
		crossServerArenaMgr.Mu = new(sync.RWMutex)
		crossServerArenaMgr.LoadCsv()
	}
	return crossServerArenaMgr
}

func GetScoreLv(score int) int {
	lv := 0
	if score <= 1000 {
		lv = 1
	} else if score >= 1001 && score <= 1199 {
		lv = 2
	} else if score >= 1200 && score <= 1399 {
		lv = 3
	} else if score >= 1400 && score <= 1599 {
		lv = 4
	} else if score >= 1600 && score <= 1799 {
		lv = 5
	} else if score >= 1800 && score <= 1999 {
		lv = 6
	} else if score >= 2000 && score <= 2399 {
		lv = 7
	} else if score >= 2400 && score <= 2799 {
		lv = 8
	} else if score >= 2800 && score <= 3199 {
		lv = 9
	} else {
		lv = 10
	}
	return lv
}

func WriterLog(logId int, info string) {
	dbData := new(MasterLog)
	dbData.LogId = logId
	dbData.Info = info
	db.InsertTable("tbl_masterlog", dbData, 0, false)
}

func CalWinScore(attackStartScore int, defenceStartScore int) int {
	Eaaa := (float64(defenceStartScore) - float64(attackStartScore)) / float64(400)
	Eaa := math.Pow(float64(10), Eaaa)
	Ea := float64(1) / (float64(1) + Eaa)
	attackScore := float64(50) * (float64(1) - Ea)
	return int(attackScore)
}

func CalLoseScore(attackStartScore int, defenceStartScore int) int {
	Ebbb := (float64(attackStartScore) - float64(defenceStartScore)) / float64(400)
	Ebb := math.Pow(float64(10), Ebbb)
	Eb := float64(1) / (float64(1) + Ebb)
	defenceScore := float64(50) * (-Eb)
	return int(defenceScore)
}

func ArenaCalScore(w http.ResponseWriter, r *http.Request) {
	attack := utils.HF_Atoi(r.FormValue("attack"))
	defence := utils.HF_Atoi(r.FormValue("defence"))
	w.Write([]byte(fmt.Sprintf("进攻方初始分：%d \n", attack)))
	w.Write([]byte(fmt.Sprintf("防守方初始分：%d \n", defence)))
	Eaaa := (float64(defence) - float64(attack)) / float64(400)
	Eaa := math.Pow(float64(10), Eaaa)
	Ea := float64(1) / (float64(1) + Eaa)
	w.Write([]byte(fmt.Sprintf("Ea：%f \n", Ea)))
	attackScore := float64(50) * (float64(1) - Ea)
	w.Write([]byte(fmt.Sprintf("attackScore：%f \n", attackScore)))

	Ebbb := (float64(attack) - float64(defence)) / float64(400)
	Ebb := math.Pow(float64(10), Ebbb)
	Eb := float64(1) / (float64(1) + Ebb)
	w.Write([]byte(fmt.Sprintf("Eb：%f \n", Eb)))
	defenceScore := float64(50) * (-Eb)
	w.Write([]byte(fmt.Sprintf("defenceScore：%f \n", defenceScore)))
	return
}

func GetRankInfoCrossServerArena(data *CrossServerArenaUser) *model.RankInfo {
	rel := new(model.RankInfo)
	if data == nil {
		return rel
	}
	rel.Uid = data.Uid
	rel.SvrId = data.SvrId
	rel.UName = data.UName
	rel.Level = data.Level
	rel.Vip = data.Vip
	rel.Icon = data.Icon
	rel.Portrait = data.Portrait
	rel.Title = data.Title
	rel.Fight = data.Fight
	rel.Num = int64(data.Score)
	rel.UnionName = data.UnionName
	if data.IsSign == model.LOGIC_TRUE {
		rel.Rank = data.RankPos
	}
	return rel
}

func (self *CrossServerArenaInfo) GetRobotUser(config *JJCRobotConfig) *CrossServerArenaUser {
	data := new(CrossServerArenaUser)
	data.Uid = int64(config.Jjcscore)
	return data
}

func (self *CrossServerArenaMgr) LoadCsv() {
	JJCRobotConfigTemp := make([]*JJCRobotConfig, 0)
	utils.GetCsvUtilMgr().LoadCsv("Jjc_Robot", &JJCRobotConfigTemp)
	for _, v := range JJCRobotConfigTemp {
		if v.Type != 5 {
			continue
		}
		self.JJCRobotConfig = append(self.JJCRobotConfig, v)
	}
	return
}

// Lz4...
func (self *CrossServerArenaDB) Encode() {
	//self.Info = utils.HF_JtoA(self.info)
	self.Info = utils.Lz4Encode(self.info)
	//self.FightInfos = utils.HF_JtoA(self.fightInfos)
	//self.FightInfos = utils.Lz4Encode(&self.fightInfos)
}

func (self *CrossServerArenaDB) Decode() {
	//json.Unmarshal([]byte(self.Info), &self.info)
	utils.Lz4Decode([]byte(self.Info), &self.info)
	//json.Unmarshal([]byte(self.FightInfos), &self.fightInfos)
	return
}

// 存储数据库
func (self *CrossServerArenaMgr) OnSave() {
	self.Mu.Lock()
	defer self.Mu.Unlock()
	for _, serverGroup := range self.CrossServerArenaInfo {
		serverGroup.Locker.RLock()
		for _, user := range serverGroup.db_list {
			user.info = serverGroup.rankInfo[user.info.RankPos-1]
			user.Encode()
			user.UpdateEx("periods", user.Periods)
		}
		serverGroup.Locker.RUnlock()
	}
}

func (self *CrossServerArenaMgr) GetAllData() {
	core.GetMasterApp().StartWait()
	self.Mu.Lock()
	defer self.Mu.Unlock()

	periods := GetServerGroupMgr().GetArenaPeriods()
	serverGroupMap := GetServerGroupMgr().GetServerGroupMap()

	startTime := model.TimeServer().UnixMilli()

	queryStr := fmt.Sprintf("select * from `%s` where periods = %d;", TABLE_CROSS_SERVER_ARENA, periods)
	var msg CrossServerArenaDB
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	dBEndTime := model.TimeServer().UnixMilli()

	resCorrect := make(map[int64]*CrossServerArenaDB)
	for i := 0; i < len(res); i++ {
		data := res[i].(*CrossServerArenaDB)
		//看看是否匹配分组
		groupId, groupOk := serverGroupMap[data.SvrId]
		if groupOk && groupId != data.GroupId {
			continue
		}
		data.Decode()
		if data.info == nil {
			continue
		}
		oldInfo, ok := resCorrect[data.Uid]
		if ok {
			if oldInfo.info.Score > data.info.Score {
				continue
			}
		}
		resCorrect[data.Uid] = data
	}

	for _, data := range resCorrect {
		_, ok := self.CrossServerArenaInfo[data.GroupId]
		if !ok {
			self.CrossServerArenaInfo[data.GroupId] = self.NewCrossServerArenaInfo(data.GroupId, periods)
		}
		if data.info == nil {
			data.info = new(CrossServerArenaUser)
			data.info.Uid = data.Uid
			data.info.SetScore(CROSS_SERVER_ARENA_SCORE_START)
			continue
		} else {
			data.info.ScoreLv = GetScoreLv(data.info.Score)
		}
		data.Init(TABLE_CROSS_SERVER_ARENA, data, false)
		self.CrossServerArenaInfo[data.GroupId].db_list[data.Uid] = data
	}
	dataEndTime := model.TimeServer().UnixMilli()
	//排序，生成初始信息
	for _, crossServerArenaInfo := range self.CrossServerArenaInfo {
		crossServerArenaInfo.InitRank()
	}
	sortEndTime := model.TimeServer().UnixMilli()
	core.GetMasterApp().StartDone()
	log.Println("顶上战争startTime:", startTime)
	log.Println("顶上战争dBEndTime:", dBEndTime)
	log.Println("顶上战争dataEndTime:", dataEndTime)
	log.Println("顶上战争sortEndTime:", sortEndTime)
	return
}
func (self *CrossServerArenaUser) SetScore(score int) {
	self.Score = CROSS_SERVER_ARENA_SCORE_START
	self.ScoreLv = GetScoreLv(CROSS_SERVER_ARENA_SCORE_START)
}
func (self *CrossServerArenaInfo) InitRank() {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	self.rankInfo = make([]*CrossServerArenaUser, 0)
	for _, v := range self.db_list {
		self.rankInfo = append(self.rankInfo, v.info)
	}
	sort.Sort(lstCrossServerArenaRank(self.rankInfo))
	for index, v := range self.rankInfo {
		v.RankPos = index + 1
	}
	return
}
func (self *CrossServerArenaMgr) NewCrossServerArenaInfo(groupId int, periods int) *CrossServerArenaInfo {
	data := new(CrossServerArenaInfo)
	data.groupId = groupId
	data.periods = periods
	data.rankInfo = make([]*CrossServerArenaUser, 0)
	data.db_list = make(map[int64]*CrossServerArenaDB)
	data.Locker = new(sync.RWMutex)
	return data
}
func (self *CrossServerArenaMgr) GetCrossServerArenaInfo(serverId int) *CrossServerArenaInfo {
	self.Mu.Lock()
	defer self.Mu.Unlock()
	//获得该服务器分组
	groupId := GetServerGroupMgr().GetGroupId(serverId)
	// 获得竞技场期数
	periods := GetServerGroupMgr().GetArenaPeriods()
	_, ok := self.CrossServerArenaInfo[groupId]
	if !ok {
		self.CrossServerArenaInfo[groupId] = self.NewCrossServerArenaInfo(groupId, periods)
	}
	return self.CrossServerArenaInfo[groupId]
}
func (self *CrossServerArenaMgr) GetCrossServerArenaInfoByGroupId(groupId int) *CrossServerArenaInfo {
	return self.CrossServerArenaInfo[groupId]
}
func (self *CrossServerArenaMgr) GetGroupConfig() string {
	//config := GetServerGroupMgr().GetArenaConfig()
	groupConfig := GetServerGroupMgr().GetGroupConfig()
	return utils.HF_JtoA(groupConfig)
}
func (self *CrossServerArenaMgr) GetConfig(serverId int) (string, string, string, string) {
	config := GetServerGroupMgr().GetArenaConfig()
	groupConfig := GetServerGroupMgr().GetGroupConfig()
	groups := GetServerGroupMgr().GetGroups(serverId)
	levelArenaConfig := GetServerGroupMgr().GetLevelArenaConfig()
	return utils.HF_JtoA(config), utils.HF_JtoA(groupConfig), utils.HF_JtoA(groups), utils.HF_JtoA(levelArenaConfig)
}
func (self *CrossServerArenaMgr) GetInfo(uid int64, serverId int, fightInfos []*model.JS_FightInfo) (int, string, string, string) {
	if len(fightInfos) < CROSS_SERVER_ARENA_TEAM_MAX {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", "", ""
	}
	crossServerArenaInfo := self.GetCrossServerArenaInfo(serverId)
	if crossServerArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", "", ""
	}
	info, enemyInfo, enemyFightInfo := crossServerArenaInfo.GetInfo(uid, fightInfos)
	infoStr := ""
	enemyInfoStr := ""
	enemyFightInfoStr := ""
	if info != nil {
		infoStr = utils.HF_JtoA(info)
	}
	if enemyInfo != nil {
		enemyInfoStr = utils.HF_JtoA(enemyInfo)
	}
	if enemyFightInfo != nil {
		enemyFightInfoStr = utils.HF_JtoA(enemyFightInfo)
	}
	return RETCODE_DATA_CROSS_OK, infoStr, enemyInfoStr, enemyFightInfoStr
}

func (self *CrossServerArenaInfo) GetInfoDB(uid int64, fightInfos []*model.JS_FightInfo) *CrossServerArenaDB {
	if len(fightInfos) == 0 {
		return nil
	}
	self.Locker.Lock()
	defer self.Locker.Unlock()
	infoDB, ok := self.db_list[uid]
	if !ok {
		infoDB = new(CrossServerArenaDB)
		infoDB.Uid = uid
		infoDB.Periods = self.periods
		infoDB.GroupId = self.groupId
		infoDB.SvrId = fightInfos[0].Server
		//infoDB.fightInfos = fightInfos
		infoDB.info = self.NewInfo(fightInfos)
		self.db_list[uid] = infoDB

		infoDB.Encode()
		id := db.InsertTable(TABLE_CROSS_SERVER_ARENA, infoDB, 0, false)
		infoDB.Id = int(id)
		infoDB.Init(TABLE_CROSS_SERVER_ARENA, infoDB, false)

		infoDB.info.RankPos = len(self.rankInfo) + 1
		self.rankInfo = append(self.rankInfo, infoDB.info)
		sort.Sort(lstCrossServerArenaRank(self.rankInfo))
		for index, v := range self.rankInfo {
			v.RankPos = index + 1
		}
	}
	infoDB.SetFightInfo(fightInfos)
	return infoDB
}
func (self *CrossServerArenaInfo) GetInfo(uid int64, fightInfos []*model.JS_FightInfo) (*CrossServerArenaUser, []*CrossServerArenaUser, map[int64]string) {
	infoDB := self.GetInfoDB(uid, fightInfos)
	if infoDB == nil {
		return nil, nil, nil
	}
	if infoDB.info != nil && len(fightInfos) > 0 {
		infoDB.info.Fight = 0
		infoDB.info.UName = fightInfos[0].Uname
		infoDB.info.Icon = fightInfos[0].Iconid
		infoDB.info.Portrait = fightInfos[0].Portrait
		infoDB.info.Title = fightInfos[0].Title
		infoDB.info.Level = fightInfos[0].Level
		infoDB.info.ArenaFight = CheckArenaFight(infoDB.info.ArenaFight)
		for _, v := range fightInfos {
			realValue := v.Deffight / 100
			infoDB.info.Fight += realValue * 100
		}
	}
	enemyInfo, enemyFightInfo := self.GetEnemy(uid)
	return infoDB.info, enemyInfo, enemyFightInfo
}

func (self *CrossServerArenaInfo) NewInfo(fightInfo []*model.JS_FightInfo) *CrossServerArenaUser {
	data := new(CrossServerArenaUser)
	data.Uid = fightInfo[0].Uid
	data.SvrId = fightInfo[0].Server
	data.UName = fightInfo[0].Uname
	data.UnionName = fightInfo[0].UnionName
	data.SetScore(CROSS_SERVER_ARENA_SCORE_START)
	data.Level = fightInfo[0].Level
	data.Vip = fightInfo[0].Vip
	data.Icon = fightInfo[0].Iconid
	data.Portrait = fightInfo[0].Portrait
	data.Title = fightInfo[0].Title
	for _, v := range fightInfo {
		realValue := v.Deffight / 100
		data.Fight += realValue * 100
	}
	return data
}

func (self *CrossServerArenaMgr) GetEnemy(uid int64, serverId int) (int, string, string) {
	crossServerArenaInfo := self.GetCrossServerArenaInfo(serverId)
	if crossServerArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	enemyInfo, enemyFightInfo := crossServerArenaInfo.GetEnemy(uid)
	return RETCODE_DATA_CROSS_OK, utils.HF_JtoA(enemyInfo), utils.HF_JtoA(enemyFightInfo)
}

func (self *CrossServerArenaInfo) GetEnemy(uid int64) ([]*CrossServerArenaUser, map[int64]string) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	relUser := make([]*CrossServerArenaUser, 0)
	relFight := make(map[int64]string, 0)
	info, ok := self.db_list[uid]
	if !ok {
		return nil, nil
	}
	if info.info == nil {
		return nil, nil
	}
	index := info.info.RankPos - 1
	if index < 0 || index >= len(self.rankInfo) {
		return nil, nil
	}
	//先把自己2个档位差的人全部取出来，作为备选列表
	score := self.rankInfo[index].Score
	scoreLv := self.rankInfo[index].ScoreLv
	scoreLvMap := make(map[int]map[int64]int)
	for i := index - 1; i >= 0; i-- {
		if self.rankInfo[i].IsSign == model.LOGIC_FALSE {
			continue
		}
		if self.rankInfo[i].Uid == 0 {
			continue
		}
		nowScoreLv := self.rankInfo[i].ScoreLv
		if nowScoreLv-scoreLv > 2 {
			break
		}
		_, ok := scoreLvMap[nowScoreLv]
		if !ok {
			scoreLvMap[nowScoreLv] = make(map[int64]int)
		}
		if len(scoreLvMap[nowScoreLv]) >= CROSS_SERVER_ARENA_ENEMY_CHOOSE_LIST {
			continue
		}
		scoreLvMap[nowScoreLv][self.rankInfo[i].Uid] = model.LOGIC_TRUE
	}
	for i := index + 1; i < len(self.rankInfo); i++ {
		if self.rankInfo[i].IsSign == model.LOGIC_FALSE {
			continue
		}
		if self.rankInfo[i].Uid == 0 {
			continue
		}
		nowScoreLv := self.rankInfo[i].ScoreLv
		if scoreLv-nowScoreLv > 2 {
			break
		}
		_, ok := scoreLvMap[nowScoreLv]
		if !ok {
			scoreLvMap[nowScoreLv] = make(map[int64]int)
		}
		if len(scoreLvMap[nowScoreLv]) >= CROSS_SERVER_ARENA_ENEMY_CHOOSE_LIST {
			continue
		}
		scoreLvMap[nowScoreLv][self.rankInfo[i].Uid] = model.LOGIC_TRUE
	}
	cantList := make(map[int64]int)
	for k := range self.rankInfo[index].CantList {
		cantList[k] = model.LOGIC_TRUE
	}

	firstEnemy, cantList := self.GetFirstEnemy(scoreLvMap, cantList, scoreLv, score)
	relUser = append(relUser, firstEnemy)
	secondEnemy, cantList := self.GetSecondEnemy(scoreLvMap, cantList, scoreLv, score)
	relUser = append(relUser, secondEnemy)
	thirdEnemy, _ := self.GetThirdEnemy(scoreLvMap, cantList, scoreLv, score)
	for k := range self.rankInfo[index].CantList {
		delete(cantList, k)
	}
	//暂时关闭过滤保证
	//self.rankInfo[index].CantList = cantList
	relUser = append(relUser, thirdEnemy)
	sort.Slice(relUser, func(i, j int) bool {
		return relUser[i].Score >= relUser[j].Score
	})
	for _, v := range relUser {
		if v.Uid == 0 {
			continue
		}
		enemyInfo, ok := self.db_list[v.Uid]
		if !ok {
			continue
		}
		relFight[v.Uid] = enemyInfo.FightInfos
	}
	//深拷返回
	deepRelUser := make([]*CrossServerArenaUser, 0)
	deepRelFight := make(map[int64]string, 0)
	utils.HF_DeepCopy(&deepRelUser, &relUser)
	utils.HF_DeepCopy(&deepRelFight, &relFight)
	return deepRelUser, deepRelFight
}

// 1. 高1档位
// 2. 高2个档位
// 3. 当前档位
// 4. 低1个档位
// 5. 低2个档位
// 6. 补机器人
func (self *CrossServerArenaInfo) GetFirstEnemy(scoreLvMap map[int]map[int64]int, cantList map[int64]int, scoreLv int, score int) (*CrossServerArenaUser, map[int64]int) {

	for k := range scoreLvMap[scoreLv+1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv+2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	//前面全部没有选中，开始补机器人
	robotInfo := new(CrossServerArenaUser)
	robotInfo.Score = score
	return robotInfo, cantList
}

// 1. 当前档位
// 2. 高一个档位
// 3. 低一个档位
// 4. 高两个档位
// 5. 低两个档位
// 6. 补机器人
func (self *CrossServerArenaInfo) GetSecondEnemy(scoreLvMap map[int]map[int64]int, cantList map[int64]int, scoreLv int, score int) (*CrossServerArenaUser, map[int64]int) {
	for k := range scoreLvMap[scoreLv] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv+1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv+2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	//前面全部没有选中，开始补机器人
	robotInfo := new(CrossServerArenaUser)
	robotInfo.Score = score
	return robotInfo, cantList
}

// 1. 低一个档位
// 2. 低两个档位
// 3. 当前档位
// 4. 补机器人
func (self *CrossServerArenaInfo) GetThirdEnemy(scoreLvMap map[int]map[int64]int, cantList map[int64]int, scoreLv int, score int) (*CrossServerArenaUser, map[int64]int) {
	for k := range scoreLvMap[scoreLv-1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	//前面全部没有选中，开始补机器人
	robotInfo := new(CrossServerArenaUser)
	robotInfo.Score = score
	return robotInfo, cantList
}

func (self *CrossServerArenaMgr) AttackEnd(uid int64, serverId int, attack []*model.JS_FightInfo,
	defend []*model.JS_FightInfo, battleInfo []*model.BattleInfo, result int, fightID [CROSS_SERVER_ARENA_TEAM_MAX]int64) (int, string, string, string) {
	crossServerArenaInfo := self.GetCrossServerArenaInfo(serverId)
	if crossServerArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", "", ""
	}
	info, enemyInfo, enemyFightInfo := crossServerArenaInfo.AttackEnd(uid, attack, defend, battleInfo, result, fightID)
	return RETCODE_DATA_CROSS_OK, utils.HF_JtoA(info), utils.HF_JtoA(enemyInfo), utils.HF_JtoA(enemyFightInfo)
}

func (self *CrossServerArenaInfo) AttackEnd(uid int64, attack []*model.JS_FightInfo, defend []*model.JS_FightInfo,
	battleInfo []*model.BattleInfo, result int, fightID [CROSS_SERVER_ARENA_TEAM_MAX]int64) (*CrossServerArenaUser, []*CrossServerArenaUser, map[int64]string) {

	self.CalAttackEnd(attack, defend, battleInfo, result, fightID)
	enemyInfo, enemyFightInfo := self.GetEnemy(uid)
	infoDB := self.GetInfoDB(uid, attack)
	if infoDB == nil {
		return nil, enemyInfo, enemyFightInfo
	}
	infoDB.SetFightInfo(attack)
	return infoDB.info, enemyInfo, enemyFightInfo
}

func (self *CrossServerArenaInfo) CalAttackEnd(attack []*model.JS_FightInfo, defend []*model.JS_FightInfo,
	battleInfo []*model.BattleInfo, result int, fightID [CROSS_SERVER_ARENA_TEAM_MAX]int64) {

	for _, v := range battleInfo {
		utils.LogDebug(fmt.Sprintf("CalAttackEnd,pre id:%d", v.Id))
	}

	self.Locker.Lock()
	defer self.Locker.Unlock()
	attackStartScore := 1000 //初始积分
	attackScore := attackStartScore
	defenceStartScore := 1000 //初始积分
	defenceScore := defenceStartScore
	attackPoint := 0  //变化的积分
	defencePoint := 0 //变化的积分
	attackUid := int64(0)
	if attack[0].Uid > 0 {
		attackUid = attack[0].Uid
		attackInfo, ok := self.db_list[attackUid]
		if ok && attackInfo.info != nil {
			attackStartScore = attackInfo.info.Score
			attackInfo.info.IsSign = model.LOGIC_TRUE
		}
	}
	//防守方需要排除机器人
	defendUid := int64(0)
	if defend[0].Uid > 0 {
		defendUid = defend[0].Uid
		defendInfo, ok := self.db_list[defendUid]
		if ok && defendInfo.info != nil {
			defenceStartScore = defendInfo.info.Score
		}
	}
	if result == model.ATTACK_WIN {
		attackPoint = CalWinScore(attackStartScore, defenceStartScore)
		defencePoint = CalLoseScore(attackStartScore, defenceStartScore)
		if attackUid > 0 {
			attackInfo, ok := self.db_list[attackUid]
			if !ok || attackInfo.info == nil {
				return
			}
			attackInfo.info.Score += attackPoint
			if attackInfo.info.Score < 0 {
				attackInfo.info.Score = 0
			}
			attackInfo.info.ScoreLv = GetScoreLv(attackInfo.info.Score)
			attackScore = attackInfo.info.Score
		}
		if defendUid > 0 {
			defendInfo, ok := self.db_list[defendUid]
			if ok && defendInfo.info != nil {
				defendInfo.info.Score += defencePoint
				if defendInfo.info.Score < 0 {
					defendInfo.info.Score = 0
				}
				defendInfo.info.ScoreLv = GetScoreLv(defendInfo.info.Score)
				defenceScore = defendInfo.info.Score
			}
		} else {
			defenceScore += defencePoint
		}
	} else {
		defencePoint = CalWinScore(attackStartScore, defenceStartScore)
		attackPoint = CalLoseScore(attackStartScore, defenceStartScore)
		if attackUid > 0 {
			attackInfo, ok := self.db_list[attackUid]
			if !ok || attackInfo.info == nil {
				return
			}
			attackInfo.info.Score += attackPoint
			if attackInfo.info.Score < 0 {
				attackInfo.info.Score = 0
			}
			attackInfo.info.ScoreLv = GetScoreLv(attackInfo.info.Score)
			attackScore = attackInfo.info.Score
		}
		if defendUid > 0 {
			defendInfo, ok := self.db_list[defendUid]
			if ok && defendInfo.info != nil {
				defendInfo.info.Score += defencePoint
				if defendInfo.info.Score < 0 {
					defendInfo.info.Score = 0
				}
				defendInfo.info.ScoreLv = GetScoreLv(defendInfo.info.Score)
				defenceScore = defendInfo.info.Score
			}
		} else {
			defenceScore += defencePoint
		}
	}
	sort.Sort(lstCrossServerArenaRank(self.rankInfo))
	for index, v := range self.rankInfo {
		v.RankPos = index + 1
	}
	//fightId := GetServerGroupMgr().GetRecordId()
	for index, v := range battleInfo {
		v.LevelID = 600003
		v.Id = fightID[index]
		if v.UserInfo[0] != nil {
			v.UserInfo[0].Score = attackScore
			v.UserInfo[0].Param1 = attackPoint
		}
		if v.UserInfo[1] != nil {
			v.UserInfo[1].Score = defenceScore
			v.UserInfo[1].Param1 = defencePoint
		}
		battleRecord := model.BattleRecord{}
		battleRecord.Id = v.Id
		battleRecord.LevelID = v.LevelID
		battleRecord.Result = result
		battleRecord.RandNum = v.Random
		battleRecord.FightInfo[0] = attack[index]
		battleRecord.FightInfo[1] = defend[index]
		lz4Str := utils.Lz4Encode(&battleRecord)
		db.HMSetRedisEx(REDIS_TABLE_NAME_BATTLERECORD, battleRecord.Id, lz4Str, utils.DAY_SECS*3)

		utils.LogDebug(fmt.Sprintf("CalAttackEnd,battleRecord id:%d", battleRecord.Id))
	}
	for _, v := range battleInfo {
		utils.LogDebug(fmt.Sprintf("CalAttackEnd,next  id:%d", v.Id))
	}
	if attackUid > 0 {
		attackInfo, ok := self.db_list[attackUid]
		if ok {
			//生成战报
			attackCrossServerArenaFight := self.NewCrossServerArenaFight(fightID[0], attack, defend, battleInfo, result, 0, attackPoint, attack[0])
			attackInfo.info.ArenaFight = append(attackInfo.info.ArenaFight, attackCrossServerArenaFight)
			attackInfo.info.ArenaFight = CheckArenaFight(attackInfo.info.ArenaFight)
		}
	}
	if defendUid > 0 {
		defendInfo, ok := self.db_list[defendUid]
		if ok {
			//生成战报
			defendCrossServerArenaFight := self.NewCrossServerArenaFight(fightID[0], attack, defend, battleInfo, result, 1, defencePoint, defend[0])
			defendInfo.info.ArenaFight = append(defendInfo.info.ArenaFight, defendCrossServerArenaFight)
			defendInfo.info.ArenaFight = CheckArenaFight(defendInfo.info.ArenaFight)
		}
	}

	return
}

func (self *CrossServerArenaInfo) NewCrossServerArenaFight(fightId int64, attack []*model.JS_FightInfo, defend []*model.JS_FightInfo,
	battleInfo []*model.BattleInfo, result int, side int, point int, fightInfo *model.JS_FightInfo) *model.CrossServerArenaFight {

	data := new(model.CrossServerArenaFight)
	data.FightId = fightId
	data.Side = side
	data.Result = result
	data.Point = point
	data.Uid = fightInfo.Uid
	data.IconId = fightInfo.Iconid
	data.Portrait = fightInfo.Portrait
	data.Title = fightInfo.Title
	data.Name = fightInfo.Uname
	data.Time = model.TimeServer().Unix()
	data.Level = fightInfo.Level
	if side == 0 {
		for _, v := range attack {
			data.Fight += v.Deffight
		}
	} else {
		for _, v := range defend {
			data.Fight += v.Deffight
		}
	}
	data.BattleInfo = battleInfo
	return data
}

func (self *CrossServerArenaMgr) GetRank(uid int64, serverId int, fightInfos []*model.JS_FightInfo) (int, string, string) {
	crossServerArenaInfo := self.GetCrossServerArenaInfo(serverId)
	if crossServerArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	selfInfo, rankInfo := crossServerArenaInfo.GetRank(uid, fightInfos, serverId)
	selfInfoStr := ""
	rankInfoStr := ""
	if selfInfo != nil {
		selfInfoStr = utils.HF_JtoA(selfInfo)
	}
	if rankInfo != nil {
		rankInfoStr = utils.HF_JtoA(rankInfo)
	}
	return RETCODE_DATA_CROSS_OK, selfInfoStr, rankInfoStr
}

func (self *CrossServerArenaInfo) GetRank(uid int64, fightInfos []*model.JS_FightInfo, serverId int) (*model.RankInfo, []*model.RankInfo) {
	selfInfo := self.GetInfoDB(uid, fightInfos)
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rankInfo := make([]*model.RankInfo, 0)
	for _, v := range self.rankInfo {
		if v.IsSign == model.LOGIC_FALSE {
			break
		}
		if len(rankInfo) >= CROSS_SERVER_ARENA_RANK_MAX {
			break
		}
		rank := GetRankInfoCrossServerArena(v)
		rankInfo = append(rankInfo, rank)
	}
	if selfInfo == nil {
		return nil, rankInfo
	}
	return GetRankInfoCrossServerArena(selfInfo.info), rankInfo
}

func (self *CrossServerArenaMgr) GetRecord(uid int64, serverId int, id int64) (int, string) {
	utils.LogDebug("CrossServerArenaMgr,GetRecord:", id)
	var battleRecord model.BattleRecord
	value, flag, err := db.HGetRedisEx(REDIS_TABLE_NAME_BATTLERECORD, id, fmt.Sprintf("%d", id))
	if err != nil || !flag {
		return 0, ""
	}
	utils.Lz4Decode([]byte(value), &battleRecord)
	//err1 := json.Unmarshal([]byte(value), &battleRecord)
	//if err1 != nil {
	//	return 0, ""
	//}
	if battleRecord.Id == 0 {
		return 0, ""
	}
	return 0, utils.HF_JtoA(battleRecord)
}

func (self *CrossServerArenaMgr) Look(uid int64, serverId int, targetUid int64) (int, string, string) {
	crossServerArenaInfo := self.GetCrossServerArenaInfo(serverId)
	if crossServerArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	userInfo, userFightInfo := crossServerArenaInfo.Look(targetUid)
	return RETCODE_DATA_CROSS_OK, utils.HF_JtoA(userInfo), utils.HF_JtoA(userFightInfo)
}

func (self *CrossServerArenaInfo) Look(uid int64) (*CrossServerArenaUser, string) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	info, ok := self.db_list[uid]
	if !ok {
		return nil, ""
	}
	return info.info, info.FightInfos
}

func (self *CrossServerArenaMgr) GetHallOfGlory(groupId int) *HallOfGlorys {
	info := self.GetCrossServerArenaInfoByGroupId(groupId)
	if info == nil {
		return nil
	}
	return info.GetHallOfGlorys()
}

func (self *CrossServerArenaInfo) GetHallOfGlorys() *HallOfGlorys {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rel := new(HallOfGlorys)
	for i := 0; i <= len(self.rankInfo); i++ {
		if i >= len(self.rankInfo) {
			break
		}
		data := self.MakeHallOfGloryInfo(self.rankInfo[i])
		rel.HallOfGlory = append(rel.HallOfGlory, data)
		if len(rel.HallOfGlory) >= 3 {
			break
		}
	}
	return rel
}
func (self *CrossServerArenaInfo) MakeHallOfGloryInfo(data *CrossServerArenaUser) *HallOfGloryInfo {
	hallOfGloryInfo := new(HallOfGloryInfo)
	if data == nil {
		return hallOfGloryInfo
	}
	hallOfGloryInfo.Uid = data.Uid
	hallOfGloryInfo.SvrId = data.SvrId
	hallOfGloryInfo.SvrName = data.SvrName
	hallOfGloryInfo.UName = data.UName
	hallOfGloryInfo.UnionName = data.UnionName
	hallOfGloryInfo.Score = data.Score
	hallOfGloryInfo.ScoreLv = data.ScoreLv
	hallOfGloryInfo.RankPos = data.RankPos
	hallOfGloryInfo.Level = data.Level
	hallOfGloryInfo.Vip = data.Vip
	hallOfGloryInfo.Icon = data.Icon
	hallOfGloryInfo.Portrait = data.Portrait
	hallOfGloryInfo.Title = data.Title
	hallOfGloryInfo.Fight = data.Fight
	return hallOfGloryInfo
}

func (self *CrossServerArenaMgr) ClearData() {
	self.CrossServerArenaInfo = make(map[int]*CrossServerArenaInfo, 0)
}

func (self *CrossServerArenaMgr) UpdateInfo(uid int64, serverId int, fightInfos []*model.JS_FightInfo) (int, string) {
	if len(fightInfos) < CROSS_SERVER_ARENA_TEAM_MAX {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	crossServerArenaInfo := self.GetCrossServerArenaInfo(serverId)
	if crossServerArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	info := crossServerArenaInfo.UpdateInfo(uid, fightInfos)
	infoStr := ""
	if info != nil {
		infoStr = utils.HF_JtoA(info)
	}
	return RETCODE_DATA_CROSS_OK, infoStr
}
func (self *CrossServerArenaInfo) UpdateInfo(uid int64, fightInfos []*model.JS_FightInfo) *CrossServerArenaUser {
	infoDB := self.GetInfoDB(uid, fightInfos)
	if infoDB == nil {
		return nil
	}
	infoDB.SetFightInfo(fightInfos)
	if infoDB.info != nil && len(fightInfos) > 0 {
		infoDB.info.Fight = 0
		infoDB.info.UName = fightInfos[0].Uname
		infoDB.info.Icon = fightInfos[0].Iconid
		infoDB.info.Portrait = fightInfos[0].Portrait
		infoDB.info.Title = fightInfos[0].Title
		infoDB.info.Level = fightInfos[0].Level
		infoDB.info.UnionName = fightInfos[0].UnionName
		infoDB.info.SvrId = fightInfos[0].Server

		for _, v := range fightInfos {
			//3队加起来因为看不见后两位的原因，导致数值偏大
			realValue := v.Deffight / 100
			infoDB.info.Fight += realValue * 100
		}
	}
	return infoDB.info
}

func (self *CrossServerArenaDB) SetFightInfo(fightInfos []*model.JS_FightInfo) {
	if len(fightInfos) != CROSS_SERVER_ARENA_TEAM_MAX {
		return
	}
	for _, v := range fightInfos {
		if v == nil {
			return
		}
		if len(v.Heroinfo) == 0 {
			return
		}
	}
	self.FightInfos = utils.Lz4Encode(fightInfos)
}
func (self *CrossServerArenaMgr) SendRankReward() {
	period := GetServerGroupMgr().GetArenaPeriods()

	self.Mu.Lock()
	defer self.Mu.Unlock()
	for _, v := range self.CrossServerArenaInfo {
		rewardList, HallOfGlorysInfo := v.GetRewardList(period)
		if HallOfGlorysInfo != nil {
			HallOfGlorysInfo.StartTime = GetServerGroupMgr().ServerGroupConfig.crossServerPlayConfig.ArenaConfig.StartTime
			HallOfGlorysInfo.EndTime = GetServerGroupMgr().ServerGroupConfig.crossServerPlayConfig.ArenaConfig.EndTime
		}
		for _, rewardInfo := range rewardList {
			//给荣耀殿堂赋值
			data, ok := GetServerGroupMgr().ServerGroup.Load(rewardInfo.ServerId)
			if ok {
				serverInfo := data.(*ServerGroupInfo)
				if serverInfo.activityInfo == nil {
					serverInfo.activityInfo = new(ActivityInfo)
				}
				serverInfo.activityInfo.HallOfGlorys = append(serverInfo.activityInfo.HallOfGlorys, HallOfGlorysInfo)
			}
			info := utils.HF_JtoA(rewardInfo)
			WriterLog(1, info)
			core.GetCenterApp().AddEvent(rewardInfo.ServerId, core.CROSS_SERVER_ARENA_RANK_REWARD, 0,
				0, 0, info)
			//给玩家打上排行标记
			for _, user := range rewardInfo.RewardInfo {
				GetOfflineInfoMgr().SetArenaRank(user.Uid, period, user.RankPos)
			}
		}
	}
}

func (self *CrossServerArenaInfo) GetRewardList(period int) (map[int]*RewardList, *HallOfGlorys) {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rewardMap := make(map[int]*RewardList)
	rel := new(HallOfGlorys)
	for index, v := range self.rankInfo {
		if v.IsSign == model.LOGIC_FALSE {
			break
		}
		//抓取荣耀殿堂数据
		if index < 3 {
			data := self.MakeHallOfGloryInfo(v)
			rel.HallOfGlory = append(rel.HallOfGlory, data)
		}
		//生成奖励名单
		info, ok := rewardMap[v.SvrId]
		if !ok {
			info = new(RewardList)
			info.ServerId = v.SvrId
			info.Period = period
			rewardMap[info.ServerId] = info
		}
		info.RewardInfo = append(info.RewardInfo, &RewardInfo{Uid: v.Uid, RankPos: v.RankPos})
	}
	return rewardMap, rel
}
func (self *CrossServerArenaMgr) DeletePlayerRecord(req *RPC_RankDoLikeReq) {
	crossServerArenaInfo := self.GetCrossServerArenaInfo(req.ServerId)
	if crossServerArenaInfo == nil {
		return
	}
	crossServerArenaInfo.DeletePlayerRecord(req.Uid)
}
func (self *CrossServerArenaInfo) DeletePlayerRecord(uid int64) {
	self.Locker.Lock()
	defer self.Locker.Unlock()

	user, ok := self.db_list[uid]
	if !ok {
		return
	}
	if user.info == nil {
		return
	}
	user.info.Score = 0
	sort.Sort(lstCrossServerArenaRank(self.rankInfo))
	for index, v := range self.rankInfo {
		v.RankPos = index + 1
	}
}

func (self *CrossServerArenaMgr) GameServerGetRankReward(serverId int, userList string) {

	targetList := make(map[int64]int)
	json.Unmarshal([]byte(userList), &targetList)
	if len(targetList) == 0 {
		return
	}
	targetPeriod := GetServerGroupMgr().GetArenaPeriods() - 1

	rewardInfo := new(RewardList)
	rewardInfo.ServerId = serverId
	rewardInfo.Period = targetPeriod
	rewardInfo.RewardInfo = make([]*RewardInfo, 0)
	for uid, period := range targetList {
		if period >= targetPeriod {
			continue
		}
		rankPos := GetOfflineInfoMgr().GetArenaRank(targetPeriod, uid)
		if rankPos == 0 {
			continue
		}
		rewardInfo.RewardInfo = append(rewardInfo.RewardInfo, &RewardInfo{Uid: uid, RankPos: rankPos})
	}
	core.GetCenterApp().AddEvent(rewardInfo.ServerId, core.CROSS_SERVER_ARENA_RANK_REWARD, 0,
		0, 0, utils.HF_JtoA(rewardInfo))
}

func CheckArenaFight(arenaFight []*model.CrossServerArenaFight) []*model.CrossServerArenaFight {
	nowTime := model.TimeServer().Unix()
	rel := make([]*model.CrossServerArenaFight, 0)
	for _, v := range arenaFight {
		if v.Time+3*utils.DAY_SECS < nowTime {
			continue
		}
		rel = append(rel, v)
	}
	if len(rel) > 10 {
		rel = rel[1:]
	}
	return rel
}

func LevelArenaCalScore(w http.ResponseWriter, r *http.Request) {
	attackStartScore := utils.HF_Atoi(r.FormValue("attack"))
	defenceStartScore := utils.HF_Atoi(r.FormValue("defence"))
	win := utils.HF_Atoi(r.FormValue("win"))
	scoreLv := utils.HF_Atoi(r.FormValue("dan"))
	w.Write([]byte(fmt.Sprintf("进攻方段位：%d \n", scoreLv)))
	w.Write([]byte(fmt.Sprintf("进攻方初始分：%d \n", attackStartScore)))
	w.Write([]byte(fmt.Sprintf("防守方初始分：%d \n", defenceStartScore)))
	config := conf.GetDanConfig(scoreLv)
	if config == nil {
		w.Write([]byte(fmt.Sprintf("段位配置不存在")))
		return
	}
	attackPoint := 0
	defencePoint := 0
	if win == 1 {
		w.Write([]byte(fmt.Sprintf("是否胜利：是 \n")))
		attackPoint = int(conf.CountAddPoint(true, config, int64(attackStartScore), int64(defenceStartScore)))
		defencePoint = int(conf.CountMinPoint(true, config, int64(attackStartScore), int64(defenceStartScore)))
	} else {
		w.Write([]byte(fmt.Sprintf("是否胜利：否")))
		attackPoint = int(conf.CountMinPoint(false, config, int64(attackStartScore), int64(defenceStartScore)))
		defencePoint = int(conf.CountAddPoint(false, config, int64(attackStartScore), int64(defenceStartScore)))
	}
	w.Write([]byte(fmt.Sprintf("进攻方分数变化：%d \n", attackPoint)))
	w.Write([]byte(fmt.Sprintf("防守方分数变化：%d \n", defencePoint)))
	return
}
