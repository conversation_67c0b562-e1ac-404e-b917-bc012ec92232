package crossserver

import (
	json "github.com/bytedance/sonic"
	"master/db"
	"master/model"
	"master/utils"
)

type RankInfoArrInfo struct {
	GroupId            int
	RankInfoArr        RankInfoArr
	RankInfoArrWorking RankInfoArr
	NeedSort           bool
}

type RankInfoArr []*model.RankInfo

func (s RankInfoArr) Len() int      { return len(s) }
func (s RankInfoArr) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s RankInfoArr) Less(i, j int) bool {
	//如果都打到噩梦模式则不判断关卡了
	if s[i].RankId == RANK_TYPE_NEW_BOSS {
		if s[i].LevelId < 800131 || s[j].LevelId < 800131 {
			if s[i].LevelId != s[j].LevelId {
				return s[i].LevelId > s[j].LevelId
			}
		}
	} else if s[i].RankId == RANK_TYPE_NEW_BOSS2 {
		if s[i].LevelId < 700198 || s[j].LevelId < 700198 {
			if s[i].LevelId != s[j].LevelId {
				return s[i].LevelId > s[j].LevelId
			}
		}
		if s[i].LeftTime == s[j].LeftTime {
			return s[i].Uid <= s[j].Uid
		}
		return s[i].LeftTime > s[j].LeftTime
	}

	if s[i].Num == s[j].Num {
		if s[i].Time == s[j].Time {
			return s[i].Uid <= s[j].Uid
		}
		return s[i].Time < s[j].Time
	}
	return s[i].Num > s[j].Num
}

type RankInfoDB struct {
	Id      int    `json:"id"`
	RankId  int    `json:"rankid"` //类型
	Period  int    `json:"period"` //期数
	RankPos int    `json:"rankpos"`
	Info    string `json:"info"`
	Uid     int64  `json:"uid"` //uid

	InfoData *model.RankInfo
	db.DataUpdate
}

func (self *RankInfoDB) Encode() {
	self.Info = utils.HF_JtoA(&self.InfoData)
	self.RankPos = self.InfoData.Rank
	self.Period = self.InfoData.Period
}

func (self *RankInfoDB) Decode() {
	json.Unmarshal([]byte(self.Info), &self.InfoData)
}
