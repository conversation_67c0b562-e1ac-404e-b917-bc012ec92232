14:34:33.405450 app_master.go:119: [debug ] 连接数据库...
14:34:33.408101 dbserver.go:60: [debug ] db connect! ds_master_001
14:34:33.409145 dbserver.go:60: [debug ] db connect! ds_master_log_001
14:34:33.463630 app_master.go:123: [debug ] 启动逻辑处理...
14:34:33.463630 app_master.go:134: [debug ] 注册并启动服务...
14:50:28.657484 app_master.go:119: [debug ] 连接数据库...
14:50:28.659566 dbserver.go:60: [debug ] db connect! ds_master_001
14:50:28.660081 dbserver.go:60: [debug ] db connect! ds_master_log_001
14:50:28.683274 app_master.go:123: [debug ] 启动逻辑处理...
14:50:28.683274 app_master.go:134: [debug ] 注册并启动服务...
14:50:28.684311 mgr_tower.go:150: [info  ] 迁移数据： san_towerbattleinfo 0
14:50:28.685441 mgr_tower.go:177: [info  ] san_towerbattleinfo 迁移数据OK
14:51:29.897815 mgr_gamedata.go:93: [debug ] SQL BASE EXEC: update `tbl_servergroup` set `activityinfo`='{"hallofglorys":[],"levelarenahallofglorys":null,"levelarenahallofglorysself":null}' where `serverid`=3 limit 1
14:51:29.899895 mgr_gamedata.go:93: [debug ] SQL BASE EXEC: update `tbl_servergroupconfig` set `starttime`=1722823200,`centretime`=1723410000,`endtime`=1724014800,`crossserverplayconfig`='{"ArenaConfig":{"StartTime":1722823200,"RewardTime":1723410000,"EndTime":1723410000,"Reward":0,"Periods":4},"LevelArenaConfig":{"StartTime":1722823200,"RewardTime":1723410000,"EndTime":1723410000,"Reward":0,"Periods":4}}' where `id`=1 limit 1
14:51:29.900404 mgr_gamedata.go:93: [debug ] SQL BASE EXEC: update `tbl_serverareaconfig` set `starttime`=1722823200,`endtime`=1724014800,`crossserverareaplayconfig`='{}' where `id`=1 limit 1
16:28:30.278678 app_master.go:158: [info  ] db close...
16:28:30.278678 mgr_database.go:67: [info  ] start close db....
16:28:30.278678 mgr_gamedata.go:107: [info  ] close db ok....
16:28:30.279193 app_master.go:162: [info  ] master close...
