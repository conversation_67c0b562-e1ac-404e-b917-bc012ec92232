package crossserver

import (
	"master/db"
	"master/utils"
)

type MinePointDB struct {
	Id          int64  `json:"id"`
	MineId      int64  `json:"mine_id"`
	OwnerId     int64  `json:"owner_id"`
	Shield      int64  `json:"shield"`
	OutputItem  int    `json:"output_item"`
	OutputRate  int64  `json:"output_rate"`
	Level       int    `json:"level"`
	LineIndex   int    `json:"line_index"`
	GroupId     int    `json:"group_id"`
	LastSaveTime int64 `json:"last_save_time"`
	OutputNum   int64  `json:"output_num"`
	MinePoint   *MinePoint
	db.DataUpdate
}

func (self *MinePointDB) Decode() {
	if self.MinePoint == nil {
		self.MinePoint = &MinePoint{
			MineId:     self.MineId,
			OwnerId:    self.OwnerId,
			Shield:     self.Shield,
			OutputItem: self.OutputItem,
			OutputRate: self.OutputRate,
			Level:      self.Level,
			LineIndex:  self.LineIndex,
			GroupId:    self.GroupId,
			OutputNum:  self.OutputNum,
		}
	}
}

type MinePositionDB struct {
	Id          int64  `json:"id"`
	PositionId  int64  `json:"position_id"`
	MineId      int64  `json:"mine_id"`
	PlayerId    int64  `json:"player_id"`
	EnterTime   int64  `json:"enter_time"`
	LeaveTime   int64  `json:"leave_time"`
	Status      string `json:"status"`
	GroupId     int    `json:"group_id"`
	OutputNum   string `json:"output_num"`
	MinePosition *MinePosition
	db.DataUpdate
}

func (self *MinePositionDB) Encode() {
	if self.MinePosition == nil {
		self.Status = ""
		return
	}
	self.Status = utils.Lz4Encode(self.MinePosition)
}

func (self *MinePositionDB) Decode() {
	if self.Status == "" {
		self.MinePosition = &MinePosition{}
		return
	}
	utils.Lz4Decode([]byte(self.Status), &self.MinePosition)
}

type MineTeamDB struct {
	Id          int64  `json:"id"`
	TeamId      int64  `json:"team_id"`
	PlayerId    int64  `json:"player_id"`
	MineId      int64  `json:"mine_id"`
	PositionId  int64  `json:"position_id"`
	GroupId     int    `json:"group_id"`
	Status      string `json:"status"`
	OutputNum   string `json:"output_num"`
	MineTeam    *MineTeam
	db.DataUpdate
}

func (self *MineTeamDB) Encode() {
	if self.MineTeam == nil {
		self.Status = ""
		return
	}
	self.Status = utils.Lz4Encode(self.MineTeam)
}

func (self *MineTeamDB) Decode() {
	if self.Status == "" {
		self.MineTeam = &MineTeam{}
		return
	}
	utils.Lz4Decode([]byte(self.Status), &self.MineTeam)
}
