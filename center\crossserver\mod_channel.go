package crossserver

import (
	"master/core"
	"master/model"
	"master/utils"
	"sync"
)

// ! 跨服聊天信息
const (
	CHAT_SERVER      = 1  // 跨服
	CHAT_PARTY       = 2  // 帮派(职位)
	CHAT_SYSTEM      = 5  //公告
	CHAT_WORLD       = 11 //世界
	CHAT_DEMONSLAYER = 12 //鬼灭世界

	MAX_MESSAGE_NUM = 1000 //! 暂时保留最长记录 --> 1000条,环形结构
)

type ModChannel struct {
	ChannelId   int           //! 渠道Id，世界频道=频道Id，公会频道=公会Id
	ChannelType int           //! 频道类型
	MsgIdLocker *sync.RWMutex //! 数据id
	PlayerList  *sync.Map     //! 角色列表   key:int64  value:*channelPlayer

	MaxMsgId int            //! 当前占用的最大消息Id
	MsgArr   []*ChatMessage //! 聊天消息队列-环形队列
}

// ! 渠道用户
type ChannelPlayer struct {
	Uid        int64  //! 用户Id
	Uname      string //! 用户名字
	Level      int    //! 等级
	IconId     int    //! 头像
	Portrait   int    //! 头像框
	Title      int    //! 称号
	ServerId   int    //! 服务器ID
	LastUpdate int64  //! 上次更新时间，超出15分钟没有刷新，视为掉线，忽略处理
}

// ! 初始化频道
func (self *ModChannel) InitChannel(channelType int) {
	self.MaxMsgId = 0
	self.ChannelType = channelType
	self.MsgIdLocker = new(sync.RWMutex)

	self.PlayerList = new(sync.Map)
	self.MsgArr = make([]*ChatMessage, MAX_MESSAGE_NUM)
}

// ! 获得频道角色
func (self *ModChannel) GetPlayer(uid int64) *ChannelPlayer {
	if uid == 0 {
		return nil
	}

	if p, ok := self.PlayerList.Load(uid); ok {
		return p.(*ChannelPlayer)
	}

	return nil
}

// ! 加入频道
func (self *ModChannel) AddPlayer(uid int64, uname string, level, iconId, portrait, title, serverid int) {
	p := self.GetPlayer(uid)
	if p == nil {
		utils.LogDebug("加入频道：", self.ChannelType, self.ChannelId, uid, uname)
		//! 不存在则添加
		p = new(ChannelPlayer)
		p.Uid = uid
		p.Uname = uname
		p.Level = level
		p.IconId = iconId
		p.Portrait = portrait
		p.Title = title
		p.ServerId = serverid

		self.PlayerList.Store(uid, p)

		//! 设置聊天频道
		GetChatMgr().SetPlayerChannel(self.ChannelType, uid, self)
	} else {
		//! 存在的话，则更新数据
		//! uid 全局唯一
		p.Uname = uname
		p.Level = level
		p.IconId = iconId
		p.Portrait = portrait
		p.Title = title
		p.ServerId = serverid

		ch := GetChatMgr().GetPlayerChannel(uid, self.ChannelType, 0, serverid)
		if ch != nil {
			ch.DelPlayer(uid)
		}

		//! 设置聊天频道
		GetChatMgr().SetPlayerChannel(self.ChannelType, uid, self)
	}
}

// ! 离开频道
func (self *ModChannel) DelPlayer(uid int64) {
	p := self.GetPlayer(uid)
	if p != nil {
		utils.LogDebug("离开频道：", self.ChannelType, self.ChannelId, uid, p.Uname)

		//! 存在角色，则删除
		self.PlayerList.Delete(uid)

		//! 删除聊天频道信息
		GetChatMgr().SetPlayerChannel(self.ChannelType, uid, nil)
	}
}

func (self *ModChannel) GetMaxMsgId() int {
	self.MsgIdLocker.Lock()
	self.MaxMsgId++
	self.MsgIdLocker.Unlock()
	return self.MaxMsgId
}

// ! 发送聊天消息
func (self *ModChannel) SendMessage(req *RPC_ChatActionReq, res *RPC_ChatActionRes, chType int) bool {
	p := core.GetPlayerMgr().GetCorePlayer(req.Uid, true)
	if !utils.IsNil(p) {
		//! 发送消息
		id := self.GetMaxMsgId()
		msg := &ChatMessage{
			MsgId:        id,
			Uid:          req.Uid,
			Uname:        p.GetUname(),
			Level:        p.GetLevel(),
			IconId:       p.GetIconId(),
			Portrait:     p.GetPortrait(),
			Title:        p.GetTitle(),
			Content:      req.Param2,
			SendTime:     int(model.TimeServer().Unix()),
			ServerId:     p.GetServerId(),
			TagetName:    req.Param3,
			ChatType:     req.Param4,
			ChatFrame:    p.GetChatFrame(),
			MergerServer: p.GetServerId(),
		}
		//! 简易的环形结构，回收利用，读取时注意
		self.SetMessage(msg)
		res.MsgList = append(res.MsgList, msg)

		self.BroadcastMessage(self.MaxMsgId, chType, req.ServerId)

		return true
	}

	return false
}

func (self *ModChannel) SetMessage(msg *ChatMessage) {
	arrIdx := msg.MsgId % MAX_MESSAGE_NUM
	self.MsgArr[arrIdx] = msg
}

func (self *ModChannel) GetMessage(msgId int) []*ChatMessage {
	rel := make([]*ChatMessage, 0)
	arrIdx := msgId % MAX_MESSAGE_NUM
	rel = append(rel, self.MsgArr[arrIdx])
	return rel
}

// ! 获取最新的聊天消息
func (self *ModChannel) QueryNewMessage(LastReqId int) []*ChatMessage {
	resArr := make([]*ChatMessage, 0)

	start := 1
	if self.MaxMsgId > 50 {
		start = self.MaxMsgId - 50
	}

	for i := start; i <= self.MaxMsgId; i++ {
		msg := self.GetMessage(i)
		if len(msg) > 0 && msg[0] != nil && msg[0].IsGag == model.LOGIC_FALSE {
			pl := core.GetPlayerMgr().GetCorePlayer(msg[0].Uid, false)
			if pl != nil {
				msg[0].Uname = pl.GetUname()
				msg[0].Level = pl.GetLevel()
				msg[0].IconId = pl.GetIconId()
				msg[0].Portrait = pl.GetPortrait()
				msg[0].Title = pl.GetTitle()
				msg[0].ChatFrame = pl.GetChatFrame()
				msg[0].MergerServer = pl.GetServerId()
				msg[0].ServerId = pl.GetServerId()
			}
			resArr = append(resArr, msg...)
		}
	}

	return resArr
}

// ! 取最新的10条聊天记录
func (self *ModChannel) QueryLastMessage() []*ChatMessage {
	return self.QueryNewMessage(self.MaxMsgId)
}

// ! 广播消息，发送聊天事件
func (self *ModChannel) BroadcastMessage(msgId int, chType int, serverId int) bool {
	if msgId <= 0 {
		return false
	}

	msg := self.GetMessage(msgId)
	if msg == nil {
		return false
	}
	for i := 0; i < len(msg); i++ {
		if msg[i] == nil {
			continue
		}
		pl := core.GetPlayerMgr().GetCorePlayer(msg[i].Uid, false)
		if !utils.IsNil(pl) {
			msg[i].Uname = pl.GetUname()
			msg[i].Level = pl.GetLevel()
			msg[i].IconId = pl.GetIconId()
			msg[i].Portrait = pl.GetPortrait()
			msg[i].Title = pl.GetTitle()
			msg[i].ChatFrame = pl.GetChatFrame()
			msg[i].MergerServer = pl.GetServerId()
		}
	}
	utils.LogDebug("BroadcastMessage:", utils.HF_JtoA(msg))
	switch chType {
	case CHAT_SERVER:
		groups := GetServerGroupMgr().GetGroups(serverId)
		for _, v := range groups {
			core.GetCenterApp().AddEvent(v.ServerId, core.CHAT_NEW_SERVER_MESSAGE, 0, 0,
				0, utils.HF_JtoA(msg))
		}
	case CHAT_PARTY:
		self.PlayerList.Range(func(uid, value interface{}) bool {
			p := value.(*ChannelPlayer)
			if p != nil {
				core.GetCenterApp().AddEvent(p.ServerId, core.CHAT_NEW_UNION_MESSAGE, uid.(int64), 0,
					p.ServerId, utils.HF_JtoA(msg))
			}
			return true
		})
	case CHAT_SYSTEM:
		core.GetCenterApp().AddEvent(serverId, core.CHAT_NEW_SYSTEM_MESSAGE, 0, 0,
			0, utils.HF_JtoA(msg))
	case CHAT_WORLD:
		core.GetCenterApp().AddEvent(serverId, core.CHAT_NEW_WORLD_MESSAGE, 0, 0,
			0, utils.HF_JtoA(msg))
	case CHAT_DEMONSLAYER:
		core.GetCenterApp().AddEvent(serverId, core.CHAT_NEW_WORLD_MESSAGE, 0, 0,
			0, utils.HF_JtoA(msg))
	}
	return true
}

// 处理因为中心服断线导致的聊天频道丢失问题
func (self *ModChannel) CheckPlayer(uid int64) {
	nowP := self.GetPlayer(uid)
	if nowP == nil {
		p := core.GetPlayerMgr().GetCorePlayer(uid, true)
		if p != nil {
			self.AddPlayer(p.GetUid(), p.GetUname(), p.GetLevel(), p.GetIconId(), p.GetPortrait(), p.GetTitle(), p.GetServerId())
		}
	}
}

// ! 删除禁言聊天
func (self *ModChannel) DeleteMessageByUid(uid int64) bool {
	p := self.GetPlayer(uid)
	if p != nil {
		start := 1
		if self.MaxMsgId > 1000 {
			start = self.MaxMsgId - 1000
		}

		isNeed := false
		for i := start; i <= self.MaxMsgId; i++ {
			msg := self.GetMessage(i)
			if len(msg) > 0 && msg[0].Uid == uid {
				msg[0].IsGag = model.LOGIC_TRUE
				isNeed = true
			}
		}

		//广播封禁信息
		if isNeed {
			self.PlayerList.Range(func(uidPlayer, value interface{}) bool {
				p := value.(*ChannelPlayer)
				if p != nil {
					core.GetCenterApp().AddEvent(p.ServerId, core.CHAT_GAP_PLAYER, uidPlayer.(int64), uid,
						self.ChannelType, "")
				}
				return true
			})
		}

		return true
	}

	return false
}
