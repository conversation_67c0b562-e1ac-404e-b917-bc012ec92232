package match

import (
	"fmt"
	json "github.com/bytedance/sonic"
	"master/db"
	"master/utils"
	"sort"
	"sync"
)

const (
	DESTROYMONSTER_RANK_MAX = 50
)

type JS_DestroyMonsterServer struct {
	SvrId   int    `json:"svrid"`
	SvrName string `json:"svrname"`
	Rank    int    `json:"rank"`
	Point   int    `json:"point"`
	Kill    int    `json:"kill"`
	Step    int    `json:"step"`
}

type JS_DestroyMonsterUserDB struct {
	Uid   int64  `json:"uid"`
	KeyId int    `json:"keyid"`
	SvrId int    `json:"svrid"`
	Info  string `json:"info"`

	info *JS_DestroyMonsterUser
	db.DataUpdate
}

type JS_DestroyMonsterUser struct {
	Uid      int64  `json:"uid"`
	SvrId    int    `json:"svrid"`
	SvrName  string `json:"svrname"`
	UName    string `json:"uname"`
	Level    int    `json:"level"`
	Vip      int    `json:"vip"`
	Icon     int    `json:"icon"`
	Point    int    `json:"point"`
	Portrait int    `json:"portrait"` // 边框  20190412 by zy
	Rank     int    `json:"rank"`
	Kill     int    `json:"kill"`
	Step     int    `json:"step"`
	KillAll  int    `json:"killall"`
}

type DestroyMonsterUserArr []*JS_DestroyMonsterUser

func (s DestroyMonsterUserArr) Len() int      { return len(s) }
func (s DestroyMonsterUserArr) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s DestroyMonsterUserArr) Less(i, j int) bool {
	return s[i].Point > s[j].Point
}

type DestroyMonsterServerArr []*JS_DestroyMonsterServer

func (s DestroyMonsterServerArr) Len() int      { return len(s) }
func (s DestroyMonsterServerArr) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s DestroyMonsterServerArr) Less(i, j int) bool {
	return s[i].Point > s[j].Point
}

type DestroyMonsterInfo struct {
	KeyId          int    `json:"keyid"`
	DestroyMonster string `json:"destroy_monster"`

	Mu                          *sync.RWMutex
	destroyMonster              map[int64]*JS_DestroyMonsterUser
	destroyMonsterByServer      map[int][]*JS_DestroyMonsterUser
	destroyMonsterNodeArr       DestroyMonsterUserArr
	db_list                     map[int64]*JS_DestroyMonsterUserDB //数据存储
	destroyMonsterServer        map[int]*JS_DestroyMonsterServer
	destroyMonsterServerNodeArr DestroyMonsterServerArr
}

type DestroyMonsterMgr struct {
	Locker                  *sync.RWMutex
	DestroyMonsterInfo      map[int]*DestroyMonsterInfo
	DestroyMonsterInfoFirst map[int]*DestroyMonsterInfo //判断key:N4*1000+serverid
}

var destroyMonsterMgr *DestroyMonsterMgr = nil

func GetDestroyMonsterMgr() *DestroyMonsterMgr {
	if destroyMonsterMgr == nil {
		destroyMonsterMgr = new(DestroyMonsterMgr)
		destroyMonsterMgr.DestroyMonsterInfo = make(map[int]*DestroyMonsterInfo)
		destroyMonsterMgr.DestroyMonsterInfoFirst = make(map[int]*DestroyMonsterInfo)
		destroyMonsterMgr.Locker = new(sync.RWMutex)
	}
	return destroyMonsterMgr
}

func (self *JS_DestroyMonsterUserDB) Encode() {
	self.Info = utils.HF_JtoA(self.info)
}

func (self *JS_DestroyMonsterUserDB) Decode() {
	json.Unmarshal([]byte(self.Info), &self.info)
}

// 存储数据库
func (self *DestroyMonsterMgr) OnSave() {
	for _, v := range self.DestroyMonsterInfo {
		v.Save()
	}
	for _, v := range self.DestroyMonsterInfoFirst {
		v.Save()
	}
}

func (self *DestroyMonsterInfo) Save() {
	self.Mu.Lock()
	defer self.Mu.Unlock()
	for _, v := range self.db_list {
		v.Encode()
		v.UpdateEx("keyid", v.KeyId)
	}
}

func (self *DestroyMonsterMgr) NewDestroyMonsterInfo(KeyId int) *DestroyMonsterInfo {
	data := new(DestroyMonsterInfo)
	data.KeyId = KeyId
	data.Mu = new(sync.RWMutex)
	data.destroyMonster = make(map[int64]*JS_DestroyMonsterUser)
	data.destroyMonsterNodeArr = make([]*JS_DestroyMonsterUser, 0)
	data.destroyMonsterByServer = make(map[int][]*JS_DestroyMonsterUser, 0)
	data.destroyMonsterServer = make(map[int]*JS_DestroyMonsterServer, 0)
	data.destroyMonsterServerNodeArr = make([]*JS_DestroyMonsterServer, 0)
	data.db_list = make(map[int64]*JS_DestroyMonsterUserDB, 0)
	return data
}

func (self *DestroyMonsterMgr) NewDestroyMonsterServer(serverId int, name string, keyId int) *JS_DestroyMonsterServer {
	data := new(JS_DestroyMonsterServer)
	data.SvrId = serverId
	data.SvrName = name
	data.Step = keyId
	return data
}

func (self *DestroyMonsterMgr) GetAllData() {
	self.LoadDestroyMonster()
}

func (self *DestroyMonsterMgr) LoadDestroyMonster() {
	self.Locker.Lock()
	defer self.Locker.Unlock()

	queryStr := fmt.Sprintf("select uid,keyid,svrid,info from `tbl_destroymonster`;")
	var msg JS_DestroyMonsterUserDB
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	for i := 0; i < len(res); i++ {
		data := res[i].(*JS_DestroyMonsterUserDB)
		if data.KeyId <= 0 {
			continue
		}

		_, ok := self.DestroyMonsterInfo[data.KeyId]
		if !ok {
			self.DestroyMonsterInfo[data.KeyId] = self.NewDestroyMonsterInfo(data.KeyId)
		}

		if self.DestroyMonsterInfo[data.KeyId] == nil {
			continue
		}
		data.Decode()
		if data.info == nil {
			continue
		}
		self.DestroyMonsterInfo[data.KeyId].destroyMonster[data.Uid] = data.info
		self.DestroyMonsterInfo[data.KeyId].destroyMonsterByServer[data.SvrId] =
			append(self.DestroyMonsterInfo[data.KeyId].destroyMonsterByServer[data.SvrId], data.info)
		self.DestroyMonsterInfo[data.KeyId].db_list[data.Uid] = data

		data.Init("tbl_destroymonster", data, false)

		_, okServer := self.DestroyMonsterInfo[data.KeyId].destroyMonsterServer[data.info.SvrId]
		if !okServer {
			self.DestroyMonsterInfo[data.KeyId].destroyMonsterServer[data.info.SvrId] = self.NewDestroyMonsterServer(data.info.SvrId, data.info.SvrName, data.info.Step)
		}
		self.DestroyMonsterInfo[data.KeyId].destroyMonsterServer[data.info.SvrId].Point += data.info.Point
		self.DestroyMonsterInfo[data.KeyId].destroyMonsterServer[data.info.SvrId].Kill += data.info.KillAll
	}

	for _, v := range self.DestroyMonsterInfo {
		v.MakeArr()
	}

	self.LoadDestroyMonsterFirst()
}

func (self *DestroyMonsterMgr) LoadDestroyMonsterFirst() {
	queryStr := fmt.Sprintf("select uid,keyid,svrid,info from `tbl_destroymonsterfirst`;")
	var msg JS_DestroyMonsterUserDB
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	for i := 0; i < len(res); i++ {
		data := res[i].(*JS_DestroyMonsterUserDB)
		if data.KeyId <= 0 {
			continue
		}
		serverKeyId := (data.KeyId/1000)*1000 + data.SvrId
		_, ok := self.DestroyMonsterInfoFirst[serverKeyId]
		if !ok {
			self.DestroyMonsterInfoFirst[serverKeyId] = self.NewDestroyMonsterInfo(serverKeyId)
		}

		if self.DestroyMonsterInfoFirst[serverKeyId] == nil {
			continue
		}
		data.Decode()
		if data.info == nil {
			continue
		}
		self.DestroyMonsterInfoFirst[serverKeyId].destroyMonster[data.Uid] = data.info
		self.DestroyMonsterInfoFirst[serverKeyId].destroyMonsterByServer[data.SvrId] =
			append(self.DestroyMonsterInfoFirst[serverKeyId].destroyMonsterByServer[data.SvrId], data.info)
		self.DestroyMonsterInfoFirst[serverKeyId].db_list[data.Uid] = data

		data.Init("tbl_destroymonsterfirst", data, false)

		_, okServer := self.DestroyMonsterInfoFirst[serverKeyId].destroyMonsterServer[data.info.SvrId]
		if !okServer {
			self.DestroyMonsterInfoFirst[serverKeyId].destroyMonsterServer[data.info.SvrId] = self.NewDestroyMonsterServer(data.info.SvrId, data.info.SvrName, data.info.Step)
		}
		self.DestroyMonsterInfoFirst[serverKeyId].destroyMonsterServer[data.info.SvrId].Point += data.info.Point
		self.DestroyMonsterInfoFirst[serverKeyId].destroyMonsterServer[data.info.SvrId].Kill += data.info.KillAll
	}

	for _, v := range self.DestroyMonsterInfoFirst {
		v.MakeArr()
	}
}

func (self *DestroyMonsterInfo) MakeArr() {
	self.Mu.Lock()
	defer self.Mu.Unlock()

	self.destroyMonsterNodeArr = make([]*JS_DestroyMonsterUser, 0)
	for _, v := range self.destroyMonster {
		self.destroyMonsterNodeArr = append(self.destroyMonsterNodeArr, v)
	}
	sort.Sort(self.destroyMonsterNodeArr)

	for i := 0; i < len(self.destroyMonsterNodeArr); i++ {
		self.destroyMonsterNodeArr[i].Rank = i + 1
	}
	//控制个人排行榜长度
	self.destroyMonsterServerNodeArr = make([]*JS_DestroyMonsterServer, 0)
	for _, v := range self.destroyMonsterServer {
		self.destroyMonsterServerNodeArr = append(self.destroyMonsterServerNodeArr, v)
	}
	sort.Sort(self.destroyMonsterServerNodeArr)

	for i := 0; i < len(self.destroyMonsterServerNodeArr); i++ {
		self.destroyMonsterServerNodeArr[i].Rank = i + 1
	}
}

func (self *DestroyMonsterMgr) UpdatePoint(req *RPC_DestroyMonsterActionReq, res *RPC_DestroyMonsterActionRes) {
	if req.SelfInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	info := self.GetDestroyMonsterInfo(req.SelfInfo.Step, req.SelfInfo.SvrId)
	info.UpdatePoint(req, res)
}

func (self *DestroyMonsterInfo) UpdatePoint(req *RPC_DestroyMonsterActionReq, res *RPC_DestroyMonsterActionRes) {
	if req.SelfInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.Mu.Lock()
	defer self.Mu.Unlock()

	addpoint := 0
	kill := 0
	info, ok := self.destroyMonster[req.SelfInfo.Uid]
	if ok {
		addpoint = req.SelfInfo.Point - info.Point
		info.SvrId = req.SelfInfo.SvrId
		info.SvrName = req.SelfInfo.SvrName
		info.UName = req.SelfInfo.UName
		info.Level = req.SelfInfo.Level
		info.Vip = req.SelfInfo.Vip
		info.Icon = req.SelfInfo.Icon
		info.Portrait = req.SelfInfo.Portrait
		info.Point = req.SelfInfo.Point
		info.KillAll += req.SelfInfo.Kill
		kill = req.SelfInfo.Kill
	} else {
		addpoint = req.SelfInfo.Point
		dbData := new(JS_DestroyMonsterUserDB)
		dbData.info = req.SelfInfo
		dbData.Uid = req.SelfInfo.Uid
		dbData.KeyId = self.KeyId
		dbData.SvrId = req.SelfInfo.SvrId
		dbData.info.KillAll = req.SelfInfo.Kill
		dbData.info.SvrId = req.SelfInfo.SvrId
		dbData.info.SvrName = req.SelfInfo.SvrName
		kill = req.SelfInfo.Kill
		dbData.Encode()
		self.db_list[req.Uid] = dbData
		dbData.info.Rank = len(self.destroyMonsterNodeArr) + 1

		if GetDestroyMonsterMgr().IsSingle(req.SelfInfo.Step) {
			db.InsertTable("tbl_destroymonsterfirst", dbData, 0, false)
			dbData.Init("tbl_destroymonsterfirst", dbData, false)
		} else {
			db.InsertTable("tbl_destroymonster", dbData, 0, false)
			dbData.Init("tbl_destroymonster", dbData, false)
		}

		self.destroyMonsterNodeArr = append(self.destroyMonsterNodeArr, dbData.info)
		self.destroyMonster[req.Uid] = req.SelfInfo
		self.destroyMonsterByServer[dbData.SvrId] =
			append(self.destroyMonsterByServer[dbData.SvrId], dbData.info)
	}

	infoNow, ok := self.destroyMonster[req.SelfInfo.Uid]

	for i := infoNow.Rank - 2; i >= 0; i-- {
		if infoNow.Point > self.destroyMonsterNodeArr[i].Point {
			self.destroyMonsterNodeArr[i].Rank++
			infoNow.Rank--
			self.destroyMonsterNodeArr.Swap(infoNow.Rank-1, self.destroyMonsterNodeArr[i].Rank-1)
		} else {
			break
		}
	}

	res.SelfInfo = infoNow
	if len(self.destroyMonsterNodeArr) >= DESTROYMONSTER_RANK_MAX {
		res.TopUser = self.destroyMonsterNodeArr[:DESTROYMONSTER_RANK_MAX]
	} else {
		res.TopUser = self.destroyMonsterNodeArr
	}
	//计算服务器排行
	_, okServer := self.destroyMonsterServer[infoNow.SvrId]
	if !okServer {
		self.destroyMonsterServer[infoNow.SvrId] = GetDestroyMonsterMgr().NewDestroyMonsterServer(infoNow.SvrId, infoNow.SvrName, infoNow.Step)
		self.destroyMonsterServer[infoNow.SvrId].Rank = len(self.destroyMonsterServerNodeArr) + 1
		self.destroyMonsterServerNodeArr = append(self.destroyMonsterServerNodeArr, self.destroyMonsterServer[infoNow.SvrId])
	}
	self.destroyMonsterServer[infoNow.SvrId].SvrName = infoNow.SvrName
	self.destroyMonsterServer[infoNow.SvrId].Point += addpoint
	self.destroyMonsterServer[infoNow.SvrId].Kill += kill

	serverNow, ok := self.destroyMonsterServer[infoNow.SvrId]
	for i := serverNow.Rank - 2; i >= 0; i-- {
		if serverNow.Point > self.destroyMonsterServerNodeArr[i].Point {
			self.destroyMonsterServerNodeArr[i].Rank++
			serverNow.Rank--
			self.destroyMonsterServerNodeArr.Swap(serverNow.Rank-1, self.destroyMonsterServerNodeArr[i].Rank-1)
		} else {
			break
		}
	}

	res.TopSvr = self.destroyMonsterServerNodeArr
	return
}

func (self *DestroyMonsterMgr) GetAllRank(req *RPC_DestroyMonsterActionReqAll, res *RPC_DestroyMonsterActionRes) {
	info := self.GetDestroyMonsterInfo(req.KeyId, req.ServerId)
	info.GetAllRank(req, res)
}

func (self *DestroyMonsterMgr) GetDestroyMonsterInfo(keyId int, serverId int) *DestroyMonsterInfo {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	if self.IsSingle(keyId) {
		serverKeyId := (keyId/1000)*1000 + serverId
		info, ok := self.DestroyMonsterInfoFirst[serverKeyId]
		if !ok {
			info = self.NewDestroyMonsterInfo(serverKeyId)
			self.DestroyMonsterInfoFirst[serverKeyId] = info
		}
		return self.DestroyMonsterInfoFirst[serverKeyId]
	} else {
		info, ok := self.DestroyMonsterInfo[keyId]
		if !ok {
			info = self.NewDestroyMonsterInfo(keyId)
			self.DestroyMonsterInfo[keyId] = info
		}
		return self.DestroyMonsterInfo[keyId]
	}
}

func (self *DestroyMonsterInfo) GetAllRank(req *RPC_DestroyMonsterActionReqAll, res *RPC_DestroyMonsterActionRes) {
	res.RetCode = RETCODE_OK
	self.Mu.RLock()
	defer self.Mu.RUnlock()
	if len(self.destroyMonsterNodeArr) >= DESTROYMONSTER_RANK_MAX {
		res.TopUser = self.destroyMonsterNodeArr[:DESTROYMONSTER_RANK_MAX]
	} else {
		res.TopUser = self.destroyMonsterNodeArr
	}
	res.TopSvr = self.destroyMonsterServerNodeArr
	res.ServerUser = self.destroyMonsterByServer[req.ServerId]
}

func (self *DestroyMonsterMgr) IsSingle(keyId int) bool {
	if keyId/1000 >= 1000 {
		return true
	}
	return false
}

func (self *DestroyMonsterMgr) ConsumerGmGetAwardList(req *RPC_DestroyMonsterActionReqAll, res *RPC_DestroyMonsterActionRes) {
	info := self.GetDestroyMonsterInfo(req.KeyId, req.ServerId)
	res.ServerUser = info.destroyMonsterByServer[req.ServerId]
	return
}
