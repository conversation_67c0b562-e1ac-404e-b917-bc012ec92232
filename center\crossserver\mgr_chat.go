package crossserver

import (
	"master/model"
	"sync"
)

const ()

// ! 聊天管理器
// ! 聊天频道只会增加，不会删除，回收后，只限制不加入
// ! 自定义聊天频道
type ChatMgr struct {
	WorldMap             *sync.Map //! 世界频道，Key = 频道Id  key:int  value:*channel
	PlayerWorldMap       *sync.Map //! 全局用户所在频道    key:int64   value:int
	UnionMap             *sync.Map //! 工会聊天频道    key:int  value:*channel
	PlayerUnionMap       *sync.Map //! 公会用户所在频道   key:int64   value:int
	DemonslayerMap       *sync.Map //! 世界频道，Key = 频道Id  key:int  value:*channel
	PlayerDemonslayerMap *sync.Map //! 全局用户所在频道    key:int64   value:int
}

// ! 全局唯一
var s_chatmgr *ChatMgr = nil

func GetChatMgr() *ChatMgr {
	if s_chatmgr == nil {
		s_chatmgr = new(ChatMgr)
		s_chatmgr.WorldMap = new(sync.Map)
		s_chatmgr.PlayerWorldMap = new(sync.Map)
		s_chatmgr.UnionMap = new(sync.Map)
		s_chatmgr.PlayerUnionMap = new(sync.Map)
		s_chatmgr.DemonslayerMap = new(sync.Map)
		s_chatmgr.PlayerDemonslayerMap = new(sync.Map)
	}

	return s_chatmgr
}

// ! 增加一个世界聊天频道
func (self *ChatMgr) GetWorldChannel(index int) *ModChannel {
	dataCh, ok := self.WorldMap.Load(index)
	if ok {
		return dataCh.(*ModChannel)
	}

	ch := new(ModChannel)
	ch.InitChannel(CHAT_WORLD)
	ch.ChannelId = index
	self.WorldMap.Store(ch.ChannelId, ch)
	return ch
}

// ! 增加一个世界聊天频道
func (self *ChatMgr) GetDemonslayerChannel(index int) *ModChannel {
	dataCh, ok := self.DemonslayerMap.Load(index)
	if ok {
		return dataCh.(*ModChannel)
	}

	ch := new(ModChannel)
	ch.InitChannel(CHAT_DEMONSLAYER)
	ch.ChannelId = index
	self.DemonslayerMap.Store(ch.ChannelId, ch)
	return ch
}

// ! 增加公会频道，加入公会自动加入
// ! 系统启动时，自动生成所有的公会频道
func (self *ChatMgr) AddUnionChannel(UnionId int) *ModChannel {
	channel := new(ModChannel)
	channel.InitChannel(CHAT_PARTY)
	channel.ChannelId = UnionId

	self.UnionMap.Store(channel.ChannelId, channel)
	return channel
}

// ! 设置角色频道信息
func (self *ChatMgr) SetPlayerChannel(chType int, uid int64, ch *ModChannel) {
	if chType == CHAT_PARTY {
		if ch == nil {
			self.PlayerUnionMap.Delete(uid)
		} else {
			self.PlayerUnionMap.Store(uid, ch)
		}
	} else if chType == CHAT_DEMONSLAYER {
		if ch == nil {
			self.PlayerDemonslayerMap.Delete(uid)
		} else {
			self.PlayerDemonslayerMap.Store(uid, model.LOGIC_TRUE)
		}
	} else {
		if ch == nil {
			self.PlayerWorldMap.Delete(uid)
		} else {
			self.PlayerWorldMap.Store(uid, model.LOGIC_TRUE)
		}
	}
}

func (self *ChatMgr) GetPlayerChannel(uid int64, chType int, param1 int, serverId int) *ModChannel {
	if chType == CHAT_PARTY {
		if param1 == 0 {
			return nil
		}
		return self.GetUnionChannel(param1)
	} else if chType == CHAT_WORLD {
		index := GetWorldChannelId(chType, serverId)
		ch := self.GetWorldChannel(index)
		return ch
	} else if chType == CHAT_DEMONSLAYER {
		index := GetWorldChannelId(chType, serverId)
		ch := self.GetDemonslayerChannel(index)
		return ch
	} else if chType == CHAT_SERVER {
		id := serverId
		groupId := GetServerGroupMgr().GetGroupId(serverId)
		if groupId > 0 {
			id = groupId
		}
		index := GetWorldChannelId(chType, id)
		ch := self.GetWorldChannel(index)
		return ch
	} else if chType == CHAT_SYSTEM {
		index := GetWorldChannelId(chType, serverId)
		ch := self.GetWorldChannel(index)
		return ch
	}

	return nil
}

// ! 获取公会聊天频道
func (self *ChatMgr) GetUnionChannel(unionId int) *ModChannel {
	if channel, ok := self.UnionMap.Load(unionId); ok {
		return channel.(*ModChannel)
	}

	return GetChatMgr().AddUnionChannel(unionId)
}

func (self *ChatMgr) ExitUnionChannel(uid int64) {
	ch, ok := self.PlayerUnionMap.Load(uid)
	if ok {
		ch.(*ModChannel).DelPlayer(uid)
	}
	return
}

func (self *ChatMgr) ClearServerChat() {
	deleteList := make([]int, 0)
	self.WorldMap.Range(func(id, value interface{}) bool {
		indexMin := CHAT_SERVER * 100000
		indexMax := (CHAT_SERVER + 1) * 100000
		channalId := id.(int)
		if channalId >= indexMin && channalId <= indexMax {
			deleteList = append(deleteList, channalId)
		}
		return true
	})
	for _, v := range deleteList {
		self.WorldMap.Delete(v)
	}
	return
}
func (self *ChatMgr) ClearDemonslayerChat() {
	deleteList := make([]int, 0)
	self.DemonslayerMap.Range(func(id, value interface{}) bool {
		indexMin := CHAT_SERVER * 100000
		indexMax := (CHAT_SERVER + 1) * 100000
		channalId := id.(int)
		if channalId >= indexMin && channalId <= indexMax {
			deleteList = append(deleteList, channalId)
		}
		return true
	})
	for _, v := range deleteList {
		self.DemonslayerMap.Delete(v)
	}
	return
}
