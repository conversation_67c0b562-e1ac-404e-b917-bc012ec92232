package main

import (
	"fmt"
	"log"
	"master/app"
	"master/center/crossserver"
	"master/center/server"
	"master/gate"
	"master/model"
	"net/http"
	_ "net/http/pprof"
	"os"
	"os/signal"
	"runtime"
	"sort"
	"syscall"
	"time"
)

// ! 注册信号量
func handleSignal(signalType os.Signal, handleFun func(*chan os.Signal)) {
	ch := make(chan os.Signal)
	signal.Notify(ch, signalType)
	go handleFun(&ch)
}

// ! 管道破裂
func handlePIPE(ch *chan os.Signal) {
	for {
		<-*ch
		log.Println("get a SIGPIPE")
	}
}

// ! ctrl+z
func handleTSTP(ch *chan os.Signal) {
	for {
		<-*ch
		log.Println("get a SIGTSTP")
		//CsvUtilMgr{}.Reload()
	}
}

// ! gdb trap
func handleTRAP(ch *chan os.Signal) {
	for {
		<-*ch
		log.Println("get a SIGTRAP")
	}
}

// ! ctrl+c
func handleINT(ch *chan os.Signal) {
	for {
		<-*ch
		log.Println("get a SIGINT")
		app.GetMasterApp().Close()
	}
}

func main() {

	runtime.GOMAXPROCS(runtime.NumCPU() * 2)
	//! 读入系统配置
	//app.GetMainApp().InitConfig()
	//GetServer().InitConfig()
	//! 读入csv
	//InitData()

	//! 连接数据库
	//app.GetMasterApp().ConnectDB()

	//! 注册信号量
	handleSignal(syscall.SIGPIPE, handlePIPE)
	//! 这个注释不删除,Linux下用
	//handleSignal(syscall.SIGTSTP, handleTSTP)
	handleSignal(syscall.SIGTRAP, handleTRAP)
	handleSignal(syscall.SIGINT, handleINT)

	//GetServer().GoService()
	//GetBackStageMgr().Init()
	//utils.TestSafeMap()

	//crossserver.TurnTimeAreaUnix(1701673986)

	conf := app.GetMasterApp().GetConfig()

	//http.HandleFunc("/fightserver", battle.FightServer)
	http.Handle("/", gate.GetGateApp().GetConnectHandler())
	http.HandleFunc("/servergroupnext", crossserver.GetServerGroupMgr().ServerGroupNext)
	http.HandleFunc("/serverzonenext", crossserver.GetServerGroupMgr().ServerZoneNext)
	http.HandleFunc("/arenacalscore", crossserver.ArenaCalScore)
	http.HandleFunc("/levelarenatest", crossserver.LevelArenaTest)
	http.HandleFunc("/addServerTime", model.AddServerTime)
	http.HandleFunc("/championstartnow", crossserver.GetChampionAllMgr().GMInitDataNow)
	http.HandleFunc("/championinfo", crossserver.GetChampionAllMgr().GM_GetNowInfo)
	http.HandleFunc("/serverinfo", ServerInfo)
	log.Println("绑定ip:", conf.Host)
	log.Println("服务器版本:", conf.ServerVer)
	app.GetMasterApp().StartService()

	//log.Fatal(http.ListenAndServe(conf.Host, nil))

}

func ServerInfo(w http.ResponseWriter, r *http.Request) {
	httpServerInfo := server.GetServerMgr().GetAllServer()

	sort.Slice(httpServerInfo, func(i, j int) bool {
		return httpServerInfo[i].ServerId < httpServerInfo[j].ServerId
	})

	for _, v := range httpServerInfo {
		w.Write([]byte(fmt.Sprintf("服务器[%d],积压事件长度[%d],最新上传时间:%s,最新请求时间:%s \n",
			v.ServerId, v.EventLen, time.Unix(v.LastConnectTime, 0).Format("2006-01-02 15:04:05"),
			time.Unix(v.LastConnectTime, 0).Format("2006-01-02 15:04:05"))))
	}
}
