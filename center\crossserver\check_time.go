package crossserver

import (
	"master/model"
	"master/utils"
	"time"
)

// 将配置表的时间转换成当前本地时间
func TurnConfigTimeAreaUnix(timeUnix int64) int64 {
	//计算时间戳日期
	configTime := time.Unix(timeUnix, 0)
	//转换成日期格式
	str := configTime.In(time.FixedZone("CST", 8*3600)).Format(utils.DATEFORMAT)
	//转换时期
	rTime, _ := time.ParseInLocation("2006-01-02 15:04:05", str, time.Local)
	//获得新时期的时间戳
	newTime := rTime.Unix()
	return TurnOldTimeAreaUnix(model.TimeServer().Unix(), newTime)
}

func IsTimeUnix(timeUnix int64) bool {
	return timeUnix > 1500000000
}

// 计算时间字符串所在的时区
func CalTimeStringArea(timeStr string) int {
	timeArea := 0
	openTime, err := time.Parse(utils.DATEFORMAT, timeStr)
	if err != nil {
		return timeArea
	}

	// 转换成日期格式
	str := openTime.In(time.FixedZone("CST", 8*3600)).Format(utils.DATEFORMAT)
	// 转换时期
	rTime, _ := time.ParseInLocation(utils.DATEFORMAT, str, time.Local)
	// 获得新时期的时间戳
	newTime := rTime.Unix()
	// 计算时区偏移
	timeArea = 8 + int((openTime.Unix()-newTime)/3600)

	return timeArea
}

// TurnNewTimeAreaUnix 将给定时间转换为新的时区偏移时间
// 参数 consultTime: 参考时间，用于计算时区偏移，以 Unix 时间戳表示
// 参数 timeUnix: 待转换的时间，以 Unix 时间戳表示
// 返回值 int64: 转换后的时间，以 Unix 时间戳表示
func TurnNewTimeAreaUnix(consultTime, timeUnix int64) int64 {
	// 获取参考时间的字符串表示，用于计算时区偏移
	nowTime := time.Unix(consultTime, 0)
	nowStr := nowTime.In(time.FixedZone("CST", 8*3600)).Format(utils.DATEFORMAT)
	nowTimeArea := CalTimeStringArea(nowStr)

	// 获取待转换时间的字符串表示，用于计算时区偏移
	judgeime := time.Unix(timeUnix, 0)
	judgeStr := judgeime.In(time.FixedZone("CST", 8*3600)).Format(utils.DATEFORMAT)
	judgeTimeArea := CalTimeStringArea(judgeStr)

	// 计算时区偏移，以秒为单位
	timeDstDis := int64((nowTimeArea - judgeTimeArea) * utils.HOUR_SECS)

	// 返回转换后的时间，加上时区偏移
	return timeUnix + timeDstDis
}

// 参数：相对于A时间  获得B时间的修正值   返回：B的修正值
func TurnOldTimeAreaUnix(consultTime, timeUnix int64) int64 {

	nowTime := time.Unix(consultTime, 0)
	nowStr := nowTime.In(time.FixedZone("CST", 8*3600)).Format(utils.DATEFORMAT)
	nowTimeArea := CalTimeStringArea(nowStr)

	//判断时间差
	judgeime := time.Unix(timeUnix, 0)
	judgeStr := judgeime.In(time.FixedZone("CST", 8*3600)).Format(utils.DATEFORMAT)
	judgeTimeArea := CalTimeStringArea(judgeStr)
	timeDstDis := int64((nowTimeArea - judgeTimeArea) * utils.HOUR_SECS)

	return timeUnix - timeDstDis
}
