package crossserver

import (
	"fmt"
	json "github.com/bytedance/sonic"
	"log"
	"master/center/conf"
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
	"net/http"
	"runtime/debug"
	"sync"
	"time"
)

const (
	TABLE_SERVERGROUP             = "tbl_servergroup"
	TABLE_SERVERGROUP_HISTORY     = "tbl_servergrouphistory"
	TABLE_SERVERGROUP_CONFIG      = "tbl_servergroupconfig"
	TABLE_SERVERGROUP_SET         = "tbl_servergroupset"
	REDIS_TABLE_NAME_BATTLERECORD = "crossserverarena_record"

	TABLE_SERVERZONE         = "tbl_serverzone"
	TABLE_SERVERZONE_HISTORY = "tbl_serverzonehistory"
	TABLE_SERVERZONE_CONFIG  = "tbl_serverzoneconfig"

	ACT_COSMIC_ARENA = 9545 // 复制的段位赛
	ACT_PEAK_ARENA   = 9546 // 复制的全力对决
	ACT_CITYBROKEN   = 9532 // 千城破
)

const (
	TIME_RESET_CROSS_SERVER_ARENA_RESET = 78 // 无限城循环时间
	TIME_RESET_CROSS_SERVER_ARENA_OPEN  = 79 // 无限城功能开启时间
	TIME_RESET_DEMON_SLAYER_WORLD_RESET = 86 // 鬼灭世界循环时间
	TIME_RESET_DEMON_SLAYER_WORLD_OPEN  = 87 // 鬼灭世界功能开启时间
)

type ServerGroupInfo struct {
	ServerId         int //服务器ID
	GroupId          int //组ID
	GroupServers     string
	LastGroupServers string
	Param            string
	ActivityInfo     string

	groupServers     *GroupServers //当前和哪些服务器分在一组
	lastGroupServers *GroupServers //上次分组记录
	param            *ServerParam  //服务器的信息，用于部分功能的展示和分组判断
	activityInfo     *ActivityInfo //荣耀殿堂，保存在服务器历届玩法相关信息

	db.DataUpdate
}

type ServerZoneInfo struct {
	ServerId        int //服务器ID
	ZoneId          int //组ID
	ZoneServers     string
	LastZoneServers string
	Param           string
	ActivityInfo    string

	zoneServers     *GroupServers //当前分组
	lastZoneServers *GroupServers //上次分组

	param        *ServerParam
	activityInfo *ActivityInfo //荣耀殿堂

	db.DataUpdate
}

type ServerGroupInfoHistory struct {
	Id               int64
	ServerId         int //服务器ID
	GroupId          int //组ID
	GroupServers     string
	LastGroupServers string
	Param            string
	ActivityInfo     string
	StartTime        int64
	EndTime          int64
	Periods          int

	db.DataUpdate
}

type ServerZoneInfoHistory struct {
	Id              int64
	ServerId        int //服务器ID
	ZoneId          int //组ID
	ZoneServers     string
	LastZoneServers string
	Param           string
	ActivityInfo    string
	StartTime       int64
	EndTime         int64
	Periods         int

	db.DataUpdate
}

type ServerGroupConfig struct {
	Id                    int
	Periods               int    //期数
	StartTime             int64  //跨区分组周期开始时间
	CentreTime            int64  //跨区分组周切换点
	EndTime               int64  //跨区分组周期结束时间
	CrossServerPlayConfig string //玩法配置
	GroupMax              int    //当前最大分区

	crossServerPlayConfig *CrossServerPlayConfig
	db.DataUpdate
}
type GroupConfig struct {
	StartTime  int64 //跨区分组周期开始时间
	CentreTime int64 //跨区分组周切换点
	EndTime    int64 //跨区分组周期结束时间
	Periods    int
}
type ServerInfo struct {
	ServerId     int    `json:"serverid"`
	MaxFightName string `json:"maxfightname"`
}
type ArenaConfig struct {
	StartTime  int64
	RewardTime int64
	EndTime    int64
	Reward     int
	Periods    int
}
type LevelArenaConfig struct {
	StartTime  int64
	RewardTime int64
	EndTime    int64
	Reward     int
	Periods    int
}
type CrossServerPlayConfig struct {
	ArenaConfig      *ArenaConfig
	LevelArenaConfig *LevelArenaConfig
}

type GroupServers struct {
	IsAuto   int
	ServerId []int
}

type TimeResetConfig struct {
	Id       int     `json:"id"`
	System   int     `json:"system"`
	TimeType int     `json:"timetype"`
	Continue int64   `json:"continue"`
	Cd       int64   `json:"cd"`
	Time     []int64 `json:"time"`
}

type ServerGroupMgr struct {
	ServerGroup       *sync.Map          //key：int(ServerId)  valye:*ServerGroupInfo
	ServerGroupConfig *ServerGroupConfig //顶上战争

	ServerZone       *sync.Map          //key：int(ServerId)  valye:*ServerZoneInfo
	ServerZoneConfig *ServerGroupConfig //大分组

	TimeResetConfigArr []*TimeResetConfig

	RecordId          int64
	RecordIdLocker    *sync.RWMutex
	ServerGroupLocker *sync.RWMutex
	nextCheckFitTime  int64
}
type ServerParam struct {
	OpenTime     int64  `json:"opentime"`     // 开服时间
	ActiveCount  int    `json:"activecount"`  // 活跃人数
	MaxFightName string `json:"maxfightname"` //! 战斗榜第一名的名字
}
type HallOfGlorys struct {
	StartTime   int64              `json:"starttime"`
	EndTime     int64              `json:"endtime"`
	HallOfGlory []*HallOfGloryInfo `json:"hallofglory"` // 荣耀殿堂
}
type ActivityInfo struct {
	HallOfGlorys               []*HallOfGlorys `json:"hallofglorys"`               // 荣耀殿堂
	LevelArenaHallOfGlorys     []*HallOfGlorys `json:"levelarenahallofglorys"`     // 跨服段位赛荣耀殿堂
	LevelArenaHallOfGlorysSelf []*HallOfGlorys `json:"levelarenahallofglorysself"` // 跨服段位赛荣耀殿堂(本服)
}
type HallOfGloryInfo struct {
	Uid       int64  `json:"uid"`
	SvrId     int    `json:"svrid"`
	SvrName   string `json:"svrname"`
	UName     string `json:"uname"`
	UnionName string `json:"unionname"`
	Score     int    `json:"score"`
	ScoreLv   int    `json:"scorelv"`
	RankPos   int    `json:"rankpos"`
	Level     int    `json:"level"`
	Vip       int    `json:"vip"`
	Icon      int    `json:"icon"`
	Portrait  int    `json:"portrait"`
	Title     int    `json:"title"`
	Fight     int64  `json:"fight"`
	Like      int    `json:"like"`
}

var serverGroupMgr *ServerGroupMgr = nil

func GetServerGroupMgr() *ServerGroupMgr {
	if serverGroupMgr == nil {
		serverGroupMgr = new(ServerGroupMgr)
		serverGroupMgr.RecordIdLocker = new(sync.RWMutex)
		serverGroupMgr.ServerGroupLocker = new(sync.RWMutex)
		serverGroupMgr.ServerGroup = new(sync.Map)
		serverGroupMgr.ServerZone = new(sync.Map)
		serverGroupMgr.ServerGroupConfig = new(ServerGroupConfig)
		serverGroupMgr.ServerZoneConfig = new(ServerGroupConfig)
	}

	return serverGroupMgr
}

func (self *ServerGroupMgr) GetAllData() {
	self.LoadCsv()
	self.LoadServerGroup()
	self.LoadServerZone()
	self.LoadServerGroupConfig()
}

func (self *ServerGroupMgr) Run() {
	defer func() {
		x := recover()
		if x != nil {
			log.Println(x, string(debug.Stack()))
			utils.LogError(x, string(debug.Stack()))
		}
	}()
	core.GetMasterApp().CheckWait()
	ticker := time.NewTicker(time.Second)
	for {
		select {
		case <-ticker.C:
			self.OnTimePlay()
			self.OnTime()
			self.OnTimeZone()
		}
	}
	ticker.Stop()
}

func (self *ServerGroupMgr) OnTime() {
	nowTime := model.TimeServer().Unix()
	//暂时屏蔽开启推送
	//if nowTime == self.ServerGroupConfig.StartTime {
	//	self.BroadCastToServer(core.CROSS_SERVER_NEW_START, 0, "")
	//	return
	//}
	if nowTime < self.ServerGroupConfig.EndTime {
		return
	}
	self.ServerGroupLocker.Lock()
	defer self.ServerGroupLocker.Unlock()
	//保存本次分组的历史数据
	self.SaveHistory()
	self.ChangePeriod() //计算顶上战争时间
	self.ResetServerGroupNew()
	//清除跨服聊天
	self.ClearGroupChat()
	self.ClearChampion(CHAMPION_GROUP_TYPE_GROUP)
	//跨服排行重排
	self.ResetRank()
	//通知给游戏服，期数已经切换
	groupConfig := GetServerGroupMgr().GetGroupConfig()
	self.BroadCastToServer(core.CROSS_SERVER_GROUP_CONFIG_UPDATE, 0, utils.HF_JtoA(groupConfig))
}

func (self *ServerGroupMgr) OnTimeZone() {
	nowTime := model.TimeServer().Unix()
	if nowTime < self.ServerZoneConfig.EndTime {
		return
	}
	//保存本次分组的历史数据
	self.SaveHistoryZone()
	self.ChangeZonePeriod() //计算顶上战争时间
	self.ResetServerZoneNew()
	//清除跨服聊天
	self.ClearZoneChat()
	self.ClearChampion(CHAMPION_GROUP_TYPE_ZONE)

	//通知给游戏服，期数已经切换
	groupConfig := GetServerGroupMgr().GetZoneConfig()
	self.BroadCastToServerZone(core.CROSS_SERVER_ZONE_CONFIG_UPDATE, 0, utils.HF_JtoA(groupConfig))
}

func (self *ServerGroupMgr) BroadCastToServer(event int, param1 int, param2 string) {
	self.ServerGroup.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerGroupInfo)
		if serverInfo != nil {
			core.GetCenterApp().AddEvent(serverInfo.ServerId, event, 0,
				0, param1, param2)
		}
		return true
	})
}

func (self *ServerGroupMgr) BroadCastToServerZone(event int, param1 int, param2 string) {
	self.ServerZone.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerZoneInfo)
		if serverInfo != nil {
			core.GetCenterApp().AddEvent(serverInfo.ServerId, event, 0,
				0, param1, param2)
		}
		return true
	})
}
func (self *ServerGroupMgr) OnTimePlay() {
	if self.CrossServerArenaNeedReward() {
		self.CrossServerArenaReward()
	}
	if self.CrossServerArenaNeedChange() {
		self.CrossServerArenaChange()
	}
	//跨服段位赛
	if self.CrossServerLevelArenaNeedReward() {
		self.CrossServerLevelArenaReward()
	}
	if self.CrossServerLevelArenaNeedChange() {
		self.CrossServerLevelArenaChange()
	}

	//跨服段位赛
	if self.CosmicArenaNeedReward() {
		GetCosmicArenaMgr().Mu.Lock()
		self.CosmicArenaReward()
		GetCosmicArenaMgr().Mu.Unlock()
	}

	if self.CosmicArenaNeedChange() {
		self.CosmicArenaChange()
	}

	// 复制全力对决
	if self.PeakArenaNeedReward() {
		GetPeakArenaMgr().Mu.Lock()
		self.PeakArenaReward()
		GetPeakArenaMgr().Mu.Unlock()
	}
	if self.PeakArenaNeedChange() {
		self.PeakArenaChange()
	}
}

//
//func (self *ServerGroupMgr) ResetServerGroup() {
//
//	//清空分组
//	self.ServerGroup.Range(func(serverId, value interface{}) bool {
//		serverInfo := value.(*ServerGroupInfo)
//		if serverInfo == nil {
//			return true
//		}
//		serverInfo.lastGroupServers = serverInfo.groupServers
//		serverInfo.GroupId = 0
//		serverInfo.groupServers = new(GroupServers)
//
//		serverInfo.lastZoneServers = serverInfo.zoneServers
//		serverInfo.ZoneGroupId = 0
//		serverInfo.zoneServers = new(GroupServers)
//		serverInfo.Encode()
//		return true
//	})
//
//	tempDataServer := make(map[int]int)
//	tempDataGroup := make(map[int][]int)
//	queryStr := fmt.Sprintf("select * from `%s`;", TABLE_SERVERGROUP_SET) //
//
//	type JS_server_group struct {
//		ServerId int `json:"serverid"`
//		GroupId  int `json:"groupid"`
//	}
//
//	var msg JS_server_group
//	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)
//
//	for i := 0; i < len(res); i++ {
//		data := res[i].(*JS_server_group)
//		if data.ServerId > 0 {
//			tempDataServer[data.ServerId] = data.GroupId
//			tempDataGroup[data.GroupId] = append(tempDataGroup[data.GroupId], data.ServerId)
//		}
//	}
//	self.ServerGroupConfig.GroupMax = 1
//	//先处理预设分组
//	notAutoList := make([]*ServerGroupInfo, 0)
//	self.ServerGroup.Range(func(serverId, value interface{}) bool {
//		serverInfo := value.(*ServerGroupInfo)
//		if serverInfo == nil {
//			return true
//		}
//		_, ok := tempDataServer[serverInfo.ServerId]
//		if !ok {
//			notAutoList = append(notAutoList, serverInfo)
//			return true
//		}
//		serverInfo.GroupId = tempDataServer[serverInfo.ServerId]
//		serverInfo.groupServers.IsAuto = core.LOGIC_TRUE
//		serverInfo.groupServers.ServerId = tempDataGroup[serverInfo.GroupId]
//		return true
//	})
//	//将剩下的服务器按照服务器id排序
//	sort.Slice(notAutoList, func(i, j int) bool {
//		return notAutoList[i].ServerId <= notAutoList[j].ServerId
//	})
//	for _, v := range notAutoList {
//		self.CheckFitSafe(v)
//	}
//	//落单的组拿出来融入其他组
//	singleServer := make([]int, 0)
//	maxGroupId := 0
//	self.ServerGroup.Range(func(serverId, value interface{}) bool {
//		serverInfo := value.(*ServerGroupInfo)
//		if serverInfo == nil {
//			return true
//		}
//		if len(serverInfo.groupServers.ServerId) < 2 {
//			singleServer = append(singleServer, serverInfo.ServerId)
//		} else {
//			if maxGroupId < maxGroupId {
//				maxGroupId = serverInfo.GroupId
//			}
//		}
//		return true
//	})
//	//zywait
//	//for _, v := range singleServer {
//	//
//	//}
//	//for _, v := range self.ServerGroup {
//	//	v.Encode()
//	//	utils.LogDebug(utils.HF_JtoA(v))
//	//}
//	//最后清空预设分组  zywait 方便测试稍后做
//	return
//}

func (self *ServerGroupMgr) GetCrossServerTime() (int64, int64, int64, int64) {
	endTime := int64(0)
	startTime := int64(0)
	nowContinue := int64(0)
	nowCD := int64(0)
	for _, config := range self.TimeResetConfigArr {
		if config.System == TIME_RESET_CROSS_SERVER_ARENA_RESET {
			if IsTimeUnix(config.Time[0]) {
				correctTime := TurnConfigTimeAreaUnix(config.Time[0])
				com := model.TimeServer().Unix() - correctTime
				nowContinue = config.Continue
				nowCD = config.Cd
				now := com / config.Continue
				startTime = correctTime + now*config.Continue
				endTime = startTime + config.Continue
			}
		}
	}
	return TurnNewTimeAreaUnix(model.TimeServer().Unix(), endTime), TurnNewTimeAreaUnix(model.TimeServer().Unix(), startTime), nowContinue, nowCD
}

func (self *ServerGroupMgr) GetZoneTime() (int64, int64, int64, int64) {
	endTime := int64(0)
	startTime := int64(0)
	nowContinue := int64(0)
	nowCD := int64(0)
	//for _, config := range self.TimeResetConfigArr {
	//	if config.System == TIME_RESET_DEMON_SLAYER_WORLD_RESET {
	//		if IsTimeUnix(config.Time[0]) {
	//			correctTime := TurnConfigTimeAreaUnix(config.Time[0])
	//			com := core.TimeServer().Unix() - correctTime
	//			nowContinue = config.Continue
	//			nowCD = config.Cd
	//			now := com / config.Continue
	//			startTime = correctTime + now*config.Continue
	//			endTime = startTime + config.Continue
	//		}
	//	}
	//}
	tNow := model.TimeServer()
	if tNow.Month() == 12 {
		startTime = time.Date(tNow.Year(), tNow.Month(), 1, 0, 0, 0, 0, tNow.Location()).Unix() // 当月第一天
		endTime = time.Date(tNow.Year()+1, 1, 1, 0, 0, 0, 0, tNow.Location()).Unix()            // 下月第一天
	} else {
		startTime = time.Date(tNow.Year(), tNow.Month(), 1, 0, 0, 0, 0, tNow.Location()).Unix() // 当月第一天
		endTime = time.Date(tNow.Year(), tNow.Month()+1, 1, 0, 0, 0, 0, tNow.Location()).Unix() // 下月第一天
	}
	nowContinue = endTime - startTime

	return TurnNewTimeAreaUnix(model.TimeServer().Unix(), endTime), TurnNewTimeAreaUnix(model.TimeServer().Unix(), startTime), nowContinue, nowCD
}

func (self *ServerGroupMgr) GetCrossServerFitTime() int64 {
	for _, config := range self.TimeResetConfigArr {
		if config.System == TIME_RESET_CROSS_SERVER_ARENA_OPEN {
			if IsTimeUnix(config.Time[0]) {
				correctTime := TurnConfigTimeAreaUnix(config.Time[0])
				return correctTime
			} else {
				return config.Time[0]
			}
		}
	}
	return 0
}
func (self *ServerGroupMgr) GetCrossServerFitTimeZone() int64 {
	for _, config := range self.TimeResetConfigArr {
		if config.System == TIME_RESET_DEMON_SLAYER_WORLD_OPEN {
			if IsTimeUnix(config.Time[0]) {
				correctTime := TurnConfigTimeAreaUnix(config.Time[0])
				return correctTime
			} else {
				return config.Time[0]
			}
		}
	}
	return 0
}
func (self *ServerGroupMgr) GetCrossServerArenaTime() (int64, int64, int64) {
	startTime := int64(0)
	rewardTime := int64(0)
	endTime := int64(0)
	for _, config := range self.TimeResetConfigArr {
		if config.System == 77 {
			if IsTimeUnix(config.Time[0]) {
				correctTime := TurnConfigTimeAreaUnix(config.Time[0])
				com := model.TimeServer().Unix() - correctTime
				allContinue := config.Continue + config.Cd
				if allContinue <= 0 {
					break
				}
				nowTimes := com / allContinue
				startTime = correctTime + nowTimes*allContinue
				endTime = startTime + allContinue
				//修改规则，CD参数这里作为正式开始后的休赛期
				startTime += config.Cd
				rewardTime = startTime + config.Continue
			}
		}
	}
	startTime = TurnNewTimeAreaUnix(model.TimeServer().Unix(), startTime)
	rewardTime = TurnNewTimeAreaUnix(model.TimeServer().Unix(), rewardTime)
	endTime = TurnNewTimeAreaUnix(model.TimeServer().Unix(), endTime)
	return startTime, rewardTime, endTime
}
func (self *ServerGroupMgr) ChangePeriod() {
	self.ServerGroupConfig.Periods++
	self.MakeGroupTime()
	return
}
func (self *ServerGroupMgr) ChangeZonePeriod() {
	self.ServerZoneConfig.Periods++
	self.MakeZoneTime()
	return
}
func (self *ServerGroupMgr) MakeGroupTime() {
	tNow := model.TimeServer()
	today := time.Date(tNow.Year(), tNow.Month(), tNow.Day(), 0, 0, 0, 0, tNow.Location()).Unix() // 当天5点时间戳
	//if tNow.Hour() < 5 {                                                                          // 5点前则判定前一天
	//	today -= utils.DAY_SECS
	//}
	endTime, startTime, nowContinue, nowCD := self.GetCrossServerTime()
	if nowContinue == 0 {
		return
	}
	times := (today - startTime) / int64(nowContinue)
	self.ServerGroupConfig.StartTime = startTime + times*nowContinue
	self.ServerGroupConfig.CentreTime = self.ServerGroupConfig.StartTime + int64(nowContinue)/2
	self.ServerGroupConfig.EndTime = endTime
	self.ServerGroupConfig.StartTime += nowCD
}
func (self *ServerGroupMgr) MakeZoneTime() {
	//tNow := core.TimeServer()
	//today := time.Date(tNow.Year(), tNow.Month(), tNow.Day(), 0, 0, 0, 0, tNow.Location()).Unix() // 当天5点时间戳
	//if tNow.Hour() < 5 {                                                                          // 5点前则判定前一天
	//	today -= utils.DAY_SECS
	//}
	endTime, startTime, nowContinue, nowCD := self.GetZoneTime()
	if nowContinue == 0 {
		return
	}
	//times := (today - startTime) / int64(nowContinue)
	self.ServerZoneConfig.StartTime = startTime // + times*nowContinue
	self.ServerZoneConfig.CentreTime = self.ServerZoneConfig.StartTime + int64(nowContinue)/2
	self.ServerZoneConfig.EndTime = endTime
	self.ServerZoneConfig.StartTime += nowCD
}
func (self *ServerGroupMgr) GetPeriods() int {
	if self.ServerGroupConfig == nil {
		return 0
	}
	return self.ServerGroupConfig.Periods
}
func (self *ServerGroupMgr) GetArenaPeriods() int {
	if self.ServerGroupConfig == nil {
		return 0
	}
	return self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig.Periods
}
func (self *ServerGroupMgr) GetPeakPeriods() int {
	if self.ServerZoneConfig == nil {
		return 0
	}
	return self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.Periods
}
func (self *ServerGroupMgr) GetServerGroupMap() map[int]int {
	rel := make(map[int]int)
	if self.ServerGroupConfig == nil {
		return rel
	}
	self.ServerGroup.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerGroupInfo)
		if serverInfo != nil {
			rel[serverInfo.ServerId] = serverInfo.GroupId
		}
		return true
	})
	return rel
}

func (self *ServerGroupMgr) GetServerZoneMap() map[int]int {
	rel := make(map[int]int)
	if self.ServerZoneConfig == nil {
		return rel
	}
	self.ServerZone.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerZoneInfo)
		if serverInfo != nil {
			rel[serverInfo.ServerId] = serverInfo.ZoneId
		}
		return true
	})
	return rel
}
func (self *ServerGroupMgr) ArenaChangePeriod() {
	self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig.Periods++
	self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig.Reward = model.LOGIC_FALSE
	self.MakeArenaTime()
	return
}
func (self *ServerGroupMgr) LevelArenaChangePeriod() {
	self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.Periods++
	self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.Reward = model.LOGIC_FALSE
	self.MakeLevelArenaTime()
	return
}

func (self *ServerGroupMgr) MakeArenaTime() {
	startTime, rewardTime, endTime := self.GetCrossServerArenaTime()
	self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig.StartTime = startTime
	self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig.RewardTime = rewardTime
	self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig.EndTime = endTime
	return
}
func (self *ServerGroupMgr) MakeLevelArenaTime() {
	startTime, rewardTime, endTime := self.GetCrossServerArenaTime() //todo 先用苍穹战场的配置
	self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.StartTime = startTime
	self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.RewardTime = rewardTime
	self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.EndTime = endTime
	return
}

func (self *ServerGroupMgr) LoadCsv() {
	utils.GetCsvUtilMgr().LoadCsv("Time_Reset", &self.TimeResetConfigArr)
	return
}

func (self *ServerGroupMgr) LoadServerGroup() {
	queryStr := fmt.Sprintf("select * from `%s`;", TABLE_SERVERGROUP)

	var msg ServerGroupInfo
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	for i := 0; i < len(res); i++ {
		data := res[i].(*ServerGroupInfo)
		data.Decode()
		data.CheckNil()
		self.ServerGroup.Store(data.ServerId, data)
		data.Init(TABLE_SERVERGROUP, data, false)
	}
}

func (self *ServerGroupMgr) LoadServerZone() {
	queryStr := fmt.Sprintf("select * from `%s`;", TABLE_SERVERZONE)

	var msg ServerZoneInfo
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	for i := 0; i < len(res); i++ {
		data := res[i].(*ServerZoneInfo)
		data.Decode()
		data.CheckNil()
		self.ServerZone.Store(data.ServerId, data)
		data.Init(TABLE_SERVERZONE, data, false)
	}
}
func (self *ServerGroupInfo) CheckNil() {
	if self.groupServers == nil {
		self.groupServers = new(GroupServers)
	}
	if self.param == nil {
		self.param = new(ServerParam)
	}
	if self.activityInfo == nil {
		self.activityInfo = new(ActivityInfo)
	}
	if self.activityInfo.HallOfGlorys == nil {
		self.activityInfo.HallOfGlorys = make([]*HallOfGlorys, 0)
	}
}

func (self *ServerZoneInfo) CheckNil() {
	if self.zoneServers == nil {
		self.zoneServers = new(GroupServers)
	}
	if self.param == nil {
		self.param = new(ServerParam)
	}
	if self.activityInfo == nil {
		self.activityInfo = new(ActivityInfo)
	}
	if self.activityInfo.HallOfGlorys == nil {
		self.activityInfo.HallOfGlorys = make([]*HallOfGlorys, 0)
	}
}

func (self *ServerGroupMgr) LoadServerGroupConfig() {
	queryStr := fmt.Sprintf("select * from `%s` limit 1;", TABLE_SERVERGROUP_CONFIG)

	ret := db.GetDBMgr().DBUser.GetOneData(queryStr, self.ServerGroupConfig, TABLE_SERVERGROUP_CONFIG, 0)
	if ret == true {
		self.ServerGroupConfig.Decode()
	} else {
		self.ServerGroupConfig.Id = 1
		db.InsertTable(TABLE_SERVERGROUP_CONFIG, self.ServerGroupConfig, 0, false)
	}
	self.ServerGroupConfig.Init(TABLE_SERVERGROUP_CONFIG, self.ServerGroupConfig, false)

	queryZoneStr := fmt.Sprintf("select * from `%s` limit 1;", TABLE_SERVERZONE_CONFIG)

	retZone := db.GetDBMgr().DBUser.GetOneData(queryZoneStr, self.ServerZoneConfig, TABLE_SERVERZONE_CONFIG, 0)
	if retZone == true {
		self.ServerZoneConfig.Decode()
	} else {
		self.ServerZoneConfig.Id = 1
		db.InsertTable(TABLE_SERVERZONE_CONFIG, self.ServerZoneConfig, 0, false)
	}
	self.ServerZoneConfig.Init(TABLE_SERVERZONE_CONFIG, self.ServerZoneConfig, false)

	self.CheckNil()
	return
}
func (self *ServerGroupMgr) CheckNil() {
	if self.ServerGroupConfig.crossServerPlayConfig == nil {
		self.ServerGroupConfig.crossServerPlayConfig = new(CrossServerPlayConfig)
	} else {
		self.MakeGroupTime()
	}
	if self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig == nil {
		self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig = new(ArenaConfig)
	} else {
		self.MakeArenaTime()
	}
	if self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig == nil {
		self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig = new(LevelArenaConfig)
	} else {
		self.MakeLevelArenaTime()
	}

	if self.ServerZoneConfig.crossServerPlayConfig == nil {
		self.ServerZoneConfig.crossServerPlayConfig = new(CrossServerPlayConfig)
	} else {
		self.MakeZoneTime()
	}
	if self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig == nil {
		self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig = new(ArenaConfig)
	}
	if self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig == nil {
		self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig = new(LevelArenaConfig)
	}
}

func (self *ServerGroupInfo) Encode() {
	self.GroupServers = utils.HF_JtoA(self.groupServers)
	self.LastGroupServers = utils.HF_JtoA(self.lastGroupServers)
	self.Param = utils.HF_JtoA(self.param)
	self.ActivityInfo = utils.HF_JtoA(self.activityInfo)
}

func (self *ServerGroupInfo) Decode() {
	json.Unmarshal([]byte(self.GroupServers), &self.groupServers)
	json.Unmarshal([]byte(self.LastGroupServers), &self.lastGroupServers)
	json.Unmarshal([]byte(self.Param), &self.param)
	json.Unmarshal([]byte(self.ActivityInfo), &self.activityInfo)
}

func (self *ServerGroupInfo) Save() {
	self.Encode()
	self.Update(true, false)
}

func (self *ServerZoneInfo) Encode() {
	self.ZoneServers = utils.HF_JtoA(self.zoneServers)
	self.LastZoneServers = utils.HF_JtoA(self.lastZoneServers)
	self.Param = utils.HF_JtoA(self.param)
	self.ActivityInfo = utils.HF_JtoA(self.activityInfo)
}

func (self *ServerZoneInfo) Decode() {
	json.Unmarshal([]byte(self.ZoneServers), &self.zoneServers)
	json.Unmarshal([]byte(self.LastZoneServers), &self.lastZoneServers)
	json.Unmarshal([]byte(self.Param), &self.param)
	json.Unmarshal([]byte(self.ActivityInfo), &self.activityInfo)
}

func (self *ServerZoneInfo) Save() {
	self.Encode()
	self.Update(true, false)
}

func (self *ServerGroupConfig) Save() {
	self.Encode()
	self.Update(true, false)
}

func (self *ServerGroupConfig) Encode() {
	self.CrossServerPlayConfig = utils.HF_JtoA(self.crossServerPlayConfig)
}

func (self *ServerGroupConfig) Decode() {
	json.Unmarshal([]byte(self.CrossServerPlayConfig), &self.crossServerPlayConfig)
}

// 存储数据库
func (self *ServerGroupMgr) OnSave() {
	self.ServerGroup.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerGroupInfo)
		serverInfo.Save()
		return true
	})

	self.ServerGroupConfig.Save()
}

// 存储数据库
func (self *ServerGroupMgr) OnSaveZone() {
	self.ServerZone.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerZoneInfo)
		serverInfo.Save()
		return true
	})

	self.ServerZoneConfig.Save()
}

func (self *ServerGroupMgr) NewCrossServer(serverId int) *ServerGroupInfo {
	serverGroup := new(ServerGroupInfo)
	serverGroup.ServerId = serverId
	serverGroup.groupServers = new(GroupServers)
	serverGroup.param = new(ServerParam)
	serverGroup.activityInfo = new(ActivityInfo)
	return serverGroup
}

func (self *ServerGroupMgr) NewCrossServerZone(serverId int) *ServerZoneInfo {
	serverGroup := new(ServerZoneInfo)
	serverGroup.ServerId = serverId
	serverGroup.zoneServers = new(GroupServers)
	serverGroup.param = new(ServerParam)
	serverGroup.activityInfo = new(ActivityInfo)
	return serverGroup
}

func (self *ServerGroupMgr) CrossServerArenaNeedReward() bool {
	if self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig.Reward == model.LOGIC_TRUE {
		return false
	}
	nowTime := model.TimeServer().Unix()
	if nowTime < self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig.RewardTime {
		return false
	}
	return true
}
func (self *ServerGroupMgr) CrossServerArenaNeedChange() bool {
	nowTime := model.TimeServer().Unix()
	if nowTime < self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig.EndTime {
		return false
	}
	return true
}
func (self *ServerGroupMgr) CrossServerLevelArenaNeedReward() bool {
	if self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.Reward == model.LOGIC_TRUE {
		return false
	}
	nowTime := model.TimeServer().Unix()
	if nowTime < self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.RewardTime {
		return false
	}
	return true
}
func (self *ServerGroupMgr) CrossServerLevelArenaNeedChange() bool {
	nowTime := model.TimeServer().Unix()
	if nowTime < self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.EndTime {
		return false
	}
	return true
}

func (self *ServerGroupMgr) CosmicArenaNeedReward() bool {
	if self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.Reward == model.LOGIC_TRUE {
		return false
	}
	nowTime := model.TimeServer().Unix()
	if self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.RewardTime == 0 {
		return false
	}
	if nowTime < self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.RewardTime {
		return false
	}
	return true
}
func (self *ServerGroupMgr) CosmicArenaNeedChange() bool {
	nowTime := model.TimeServer().Unix()
	if self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.EndTime == 0 {
		return false
	}
	if nowTime < self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.EndTime {
		return false
	}
	return true
}

func (self *ServerGroupMgr) PeakArenaNeedReward() bool {
	if self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.Reward == model.LOGIC_TRUE {
		return false
	}
	nowTime := model.TimeServer().Unix()
	if self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.RewardTime == 0 {
		return false
	}
	if nowTime < self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.RewardTime {
		return false
	}
	return true
}
func (self *ServerGroupMgr) PeakArenaNeedChange() bool {
	nowTime := model.TimeServer().Unix()
	if self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.EndTime == 0 {
		return false
	}
	if nowTime < self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.EndTime {
		return false
	}
	return true
}

// 发奖
func (self *ServerGroupMgr) CrossServerArenaReward() {
	//通知游戏服发送
	GetCrossServerArenaMgr().SendRankReward()
	self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig.Reward = model.LOGIC_TRUE
	return
}

func (self *ServerGroupMgr) CrossServerLevelArenaReward() {
	//通知游戏服发送
	GetCrossServerLevelArenaMgr().SendRankReward()
	self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.Reward = model.LOGIC_TRUE
	return
}

func (self *ServerGroupMgr) CosmicArenaReward() {
	//通知游戏服发送
	GetCosmicArenaMgr().SendRankReward()
	self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.Reward = model.LOGIC_TRUE
	return
}

func (self *ServerGroupMgr) PeakArenaReward() {
	//通知游戏服发送
	GetPeakArenaMgr().SendRankReward()
	self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.Reward = model.LOGIC_TRUE
	return
}

// 切换期数
func (self *ServerGroupMgr) MakeHallOfGlorys() {
	self.ServerGroup.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerGroupInfo)
		if serverInfo == nil {
			return true
		}
		if serverInfo.activityInfo == nil {
			serverInfo.activityInfo = new(ActivityInfo)
		}

		hallOfGlorys := GetCrossServerArenaMgr().GetHallOfGlory(serverInfo.GroupId)
		if hallOfGlorys == nil {
			return true
		}
		hallOfGlorys.StartTime = self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig.StartTime
		hallOfGlorys.EndTime = self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig.EndTime
		serverInfo.activityInfo.HallOfGlorys = append(serverInfo.activityInfo.HallOfGlorys, hallOfGlorys)
		return true
	})
}
func (self *ServerGroupMgr) MakeLevelArenaHallOfGlorys() {
	self.ServerGroup.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerGroupInfo)
		if serverInfo == nil {
			return true
		}
		if serverInfo.activityInfo == nil {
			serverInfo.activityInfo = new(ActivityInfo)
		}

		hallOfGlorys := GetCrossServerLevelArenaMgr().GetHallOfGlory(serverInfo.GroupId, 0)
		if hallOfGlorys != nil {
			hallOfGlorys.StartTime = self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.StartTime
			hallOfGlorys.EndTime = self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.EndTime
			serverInfo.activityInfo.LevelArenaHallOfGlorys = append(serverInfo.activityInfo.LevelArenaHallOfGlorys, hallOfGlorys)
		}

		hallOfGlorysSelf := GetCrossServerLevelArenaMgr().GetHallOfGlory(serverInfo.GroupId, serverInfo.ServerId)
		if hallOfGlorysSelf != nil {
			hallOfGlorysSelf.StartTime = self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.StartTime
			hallOfGlorysSelf.EndTime = self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.EndTime
			serverInfo.activityInfo.LevelArenaHallOfGlorysSelf = append(serverInfo.activityInfo.LevelArenaHallOfGlorysSelf, hallOfGlorys)
		}
		return true
	})
}
func (self *ServerGroupMgr) MakeCosmicArenaHallOfGlorys() {
	self.ServerZone.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerZoneInfo)
		if serverInfo == nil {
			return true
		}
		if serverInfo.activityInfo == nil {
			serverInfo.activityInfo = new(ActivityInfo)
		}

		hallOfGlorys := GetCosmicArenaMgr().GetHallOfGlory(serverInfo.ZoneId, 0)
		if hallOfGlorys != nil {
			hallOfGlorys.StartTime = self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.StartTime
			hallOfGlorys.EndTime = self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.EndTime
			serverInfo.activityInfo.LevelArenaHallOfGlorys = append(serverInfo.activityInfo.LevelArenaHallOfGlorys, hallOfGlorys)
		}

		hallOfGlorysSelf := GetCosmicArenaMgr().GetHallOfGlory(serverInfo.ZoneId, serverInfo.ServerId)
		if hallOfGlorysSelf != nil {
			hallOfGlorysSelf.StartTime = self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.StartTime
			hallOfGlorysSelf.EndTime = self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.EndTime
			serverInfo.activityInfo.LevelArenaHallOfGlorysSelf = append(serverInfo.activityInfo.LevelArenaHallOfGlorysSelf, hallOfGlorys)
		}
		return true
	})
}
func (self *ServerGroupMgr) MakePeakArenaHallOfGlorys() {
	self.ServerZone.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerZoneInfo)
		if serverInfo == nil {
			return true
		}
		if serverInfo.activityInfo == nil {
			serverInfo.activityInfo = new(ActivityInfo)
		}

		hallOfGlorys := GetPeakArenaMgr().GetHallOfGlory(serverInfo.ZoneId)
		if hallOfGlorys != nil {
			hallOfGlorys.StartTime = self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.StartTime
			hallOfGlorys.EndTime = self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.EndTime
			serverInfo.activityInfo.HallOfGlorys = append(serverInfo.activityInfo.HallOfGlorys, hallOfGlorys)
		}
		return true
	})
}
func (self *ServerGroupMgr) CrossServerArenaChange() {
	GetCrossServerArenaMgr().Mu.Lock()
	defer GetCrossServerArenaMgr().Mu.Unlock()
	//记录名人堂
	self.MakeHallOfGlorys()
	//自己重新生成时间
	self.ArenaChangePeriod()
	//通知苍穹战场清空数据
	GetCrossServerArenaMgr().ClearData()
	return
}
func (self *ServerGroupMgr) CrossServerLevelArenaChange() {
	GetCrossServerLevelArenaMgr().Mu.Lock()
	defer GetCrossServerLevelArenaMgr().Mu.Unlock()
	//记录段位赛名人堂
	self.MakeLevelArenaHallOfGlorys()
	//重新生成时间
	self.LevelArenaChangePeriod()
	//通知段位赛清空数据
	GetCrossServerLevelArenaMgr().ClearData()
	return
}
func (self *ServerGroupMgr) CosmicArenaChange() {
	GetCosmicArenaMgr().Mu.Lock()
	defer GetCosmicArenaMgr().Mu.Unlock()
	//记录段位赛名人堂
	self.MakeCosmicArenaHallOfGlorys()
	self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.EndTime = 0
	//通知段位赛清空数据
	GetCosmicArenaMgr().ClearData()
	self.BroadCastToServerZone(core.CROSS_SERVER_COSMICARENA_CONFIG_UPDATE, 0, "")
	return
}
func (self *ServerGroupMgr) PeakArenaChange() {
	GetPeakArenaMgr().Mu.Lock()
	defer GetPeakArenaMgr().Mu.Unlock()
	//记录段位赛名人堂
	self.MakePeakArenaHallOfGlorys()
	self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.EndTime = 0
	//通知段位赛清空数据
	GetPeakArenaMgr().ClearData()
	self.BroadCastToServerZone(core.CROSS_SERVER_PEAK_ARENA_CONFIG_UPDATE, 0, "")
	return
}
func (self *ServerGroupMgr) GetArenaConfig() *ArenaConfig {
	return self.ServerGroupConfig.crossServerPlayConfig.ArenaConfig
}
func (self *ServerGroupMgr) GetPeakConfig() *ArenaConfig {
	return self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig
}
func (self *ServerGroupMgr) GetLevelArenaConfig() *LevelArenaConfig {
	return self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig
}
func (self *ServerGroupMgr) GetCosmicArenaConfig() *LevelArenaConfig {
	return self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig
}
func (self *ServerGroupMgr) GetGroupConfig() *GroupConfig {
	config := new(GroupConfig)
	config.StartTime = self.ServerGroupConfig.StartTime
	config.CentreTime = self.ServerGroupConfig.CentreTime
	config.EndTime = self.ServerGroupConfig.EndTime
	config.Periods = self.ServerGroupConfig.Periods
	return config
}

func (self *ServerGroupMgr) GetZoneConfig() *GroupConfig {
	config := new(GroupConfig)
	config.StartTime = self.ServerZoneConfig.StartTime
	config.CentreTime = self.ServerZoneConfig.CentreTime
	config.EndTime = self.ServerZoneConfig.EndTime
	config.Periods = self.ServerZoneConfig.Periods
	return config
}

func (self *ServerGroupMgr) UploadServer(req *RPC_RegCrossServerReq, res *RPC_RegCrossServerRes) {
	self.ServerGroupLocker.Lock()
	defer self.ServerGroupLocker.Unlock()
	crossServerData, ok := self.ServerGroup.Load(req.ServerId)
	if !ok {
		crossServer := self.NewCrossServer(req.ServerId)
		db.InsertTable(TABLE_SERVERGROUP, crossServer, 0, false)
		crossServer.Init(TABLE_SERVERGROUP, crossServer, false)
		self.ServerGroup.Store(crossServer.ServerId, crossServer)
		crossServerData = crossServer
	}
	crossServer := crossServerData.(*ServerGroupInfo)
	if crossServer == nil {
		return
	}
	crossServer.param.OpenTime = req.OpenTime
	crossServer.param.MaxFightName = req.MaxFightName
	self.CheckFitSafe(crossServer, false)

	crossServerDataZone, ok := self.ServerZone.Load(req.ServerId)
	if !ok {
		crossServerzone := self.NewCrossServerZone(req.ServerId)
		db.InsertTable(TABLE_SERVERZONE, crossServerzone, 0, false)
		crossServerzone.Init(TABLE_SERVERZONE, crossServerzone, false)
		self.ServerZone.Store(crossServerzone.ServerId, crossServerzone)
		crossServerDataZone = crossServerzone
	}
	crossServerZone := crossServerDataZone.(*ServerZoneInfo)
	if crossServerZone == nil {
		return
	}
	crossServerZone.param.OpenTime = req.OpenTime
	crossServerZone.param.MaxFightName = req.MaxFightName
	self.CheckFitSafeZone(crossServerZone, false)
	return
}

func (self *ServerGroupMgr) UpdateActivity(req *RPC_UpdateActivityReq, res *RPC_UpdateActivityRes) {
	if req.Periods != 0 {
		if req.ActivityId == ACT_COSMIC_ARENA {
			GetCosmicArenaMgr().Mu.Lock()
			defer GetCosmicArenaMgr().Mu.Unlock()
			if self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.Periods != req.Periods {
				if self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.Reward != model.LOGIC_TRUE {
					self.CosmicArenaReward()
				}
				//记录段位赛名人堂
				self.MakeCosmicArenaHallOfGlorys()
				self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.Periods = req.Periods
				self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.StartTime = req.StartTime
				self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.RewardTime = req.RewardTime
				self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.EndTime = req.EndTime
				self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.Reward = model.LOGIC_FALSE

				//通知段位赛清空数据
				GetCosmicArenaMgr().ClearData()
			}
		} else if req.ActivityId == ACT_PEAK_ARENA {
			GetPeakArenaMgr().Mu.Lock()
			defer GetPeakArenaMgr().Mu.Unlock()
			if self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.Periods != req.Periods {
				if self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.Reward != model.LOGIC_TRUE {
					self.PeakArenaReward()
				}
				//记录段位赛名人堂
				self.MakePeakArenaHallOfGlorys()
				self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.Periods = req.Periods
				self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.StartTime = req.StartTime
				self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.RewardTime = req.RewardTime
				self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.EndTime = req.EndTime
				self.ServerZoneConfig.crossServerPlayConfig.ArenaConfig.Reward = model.LOGIC_FALSE

				//通知段位赛清空数据
				GetPeakArenaMgr().ClearData()
			}
		} else if req.ActivityId == ACT_CITYBROKEN {
			if req.EndTime != 0 {
				GetCityBrokenMgr().CheckCityBroken(req)
			}
		}
	}
	return
}

func (self *ServerGroupMgr) GetGroupId(serverId int) int {
	data, ok := self.ServerGroup.Load(serverId)
	if !ok {
		return 0
	}
	serverGroupInfo := data.(*ServerGroupInfo)
	if serverGroupInfo == nil {
		return 0
	}
	return serverGroupInfo.GroupId
}

func (self *ServerGroupMgr) GetZoneId(serverId int) int {
	data, ok := self.ServerZone.Load(serverId)
	if !ok {
		return 0
	}
	serverZoneInfo := data.(*ServerZoneInfo)
	if serverZoneInfo == nil {
		return 0
	}
	return serverZoneInfo.ZoneId
}

func (self *ServerGroupMgr) GetRecordId() int64 {
	self.RecordIdLocker.Lock()
	defer self.RecordIdLocker.Unlock()
	recordId := (model.TimeServer().Unix()%10000000)*1000 + self.RecordId%1000
	self.RecordId += 3
	return recordId
}

func (self *ServerGroupMgr) HallOfGlory(serverId int) (int, string) {
	self.ServerGroupLocker.RLock()
	defer self.ServerGroupLocker.RUnlock()

	data, ok := self.ServerGroup.Load(serverId)
	if !ok {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	serverGroup := data.(*ServerGroupInfo)
	return RETCODE_DATA_CROSS_OK, utils.HF_JtoA(serverGroup.activityInfo.HallOfGlorys)
}

func (self *ServerGroupMgr) LevelHallOfGlory(serverId int) (int, string, string) {
	self.ServerGroupLocker.RLock()
	defer self.ServerGroupLocker.RUnlock()

	data, ok := self.ServerGroup.Load(serverId)
	if !ok {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	serverGroup := data.(*ServerGroupInfo)
	return RETCODE_DATA_CROSS_OK, utils.HF_JtoA(serverGroup.activityInfo.LevelArenaHallOfGlorys), utils.HF_JtoA(serverGroup.activityInfo.LevelArenaHallOfGlorysSelf)
}

func (self *ServerGroupMgr) HallOfGloryLike(serverId int, targetUid int64) (int, string) {
	self.ServerGroupLocker.RLock()
	defer self.ServerGroupLocker.RUnlock()

	data, ok := self.ServerGroup.Load(serverId)
	if !ok {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	serverGroup := data.(*ServerGroupInfo)
	isFind := false
	size := len(serverGroup.activityInfo.HallOfGlorys)
	relInfo := make([]*HallOfGloryInfo, 0)
	if size > 0 {
		for _, v := range serverGroup.activityInfo.HallOfGlorys[size-1].HallOfGlory {
			if v.Uid == targetUid {
				isFind = true
				v.Like++
			}
		}
		relInfo = serverGroup.activityInfo.HallOfGlorys[size-1].HallOfGlory
	}
	if !isFind {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}

	return RETCODE_DATA_CROSS_OK, utils.HF_JtoA(relInfo)
}

func (self *ServerGroupMgr) LevelHallOfGloryLike(serverId int, targetUid int64, nType int) (int, string, string) {
	self.ServerGroupLocker.RLock()
	defer self.ServerGroupLocker.RUnlock()

	data, ok := self.ServerGroup.Load(serverId)
	if !ok {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	serverGroup := data.(*ServerGroupInfo)
	isFind := false
	if nType == model.LOGIC_TRUE {
		size := len(serverGroup.activityInfo.LevelArenaHallOfGlorysSelf)
		if size > 0 {
			for _, v := range serverGroup.activityInfo.LevelArenaHallOfGlorysSelf[size-1].HallOfGlory {
				if v.Uid == targetUid {
					isFind = true
					v.Like++
				}
			}
		}
	} else {
		size := len(serverGroup.activityInfo.HallOfGlorys)
		if size > 0 {
			for _, v := range serverGroup.activityInfo.HallOfGlorys[size-1].HallOfGlory {
				if v.Uid == targetUid {
					isFind = true
					v.Like++
				}
			}
		}
	}
	if !isFind {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	relInfo := make([]*HallOfGloryInfo, 0)
	relInfoSelf := make([]*HallOfGloryInfo, 0)
	size := len(serverGroup.activityInfo.LevelArenaHallOfGlorysSelf)
	if size > 0 {
		relInfoSelf = serverGroup.activityInfo.LevelArenaHallOfGlorysSelf[size-1].HallOfGlory
	}
	size = len(serverGroup.activityInfo.LevelArenaHallOfGlorys)
	if size > 0 {
		relInfo = serverGroup.activityInfo.LevelArenaHallOfGlorys[size-1].HallOfGlory
	}
	return RETCODE_DATA_CROSS_OK, utils.HF_JtoA(relInfo), utils.HF_JtoA(relInfoSelf)
}

func (self *ServerGroupMgr) CheckFitSafe(targetServer *ServerGroupInfo, reset bool) {
	// 获得顶上战争开启时间
	fitTime := self.GetCrossServerFitTime()
	nowTime := model.TimeServer().Unix()
	// 已经持续了多久 比开启时间要短则跳过 说明还没到开启时间
	if nowTime-targetServer.param.OpenTime < fitTime {
		return
	}
	// 如果已经分组则返回
	if targetServer.GroupId > 0 {
		return
	}
	//看看当前组缺不缺对手
	isNeedNewGroup := true
	oldServerId := 0
	oldGroupId := 0
	NowGroupMax := self.ServerGroupConfig.GroupMax

	self.ServerGroup.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerGroupInfo)
		if serverInfo == nil {
			return true
		}
		if serverInfo.GroupId >= NowGroupMax {
			NowGroupMax = serverInfo.GroupId + 1
		}
		if len(serverInfo.groupServers.ServerId) == 1 {
			// 和上次已经分过组了
			if serverInfo.IsHistory(targetServer.ServerId) {
				return true
			}
			// 找到了不需要新分组 把这个服加入这个空一个的分组中
			isNeedNewGroup = false
			oldServerId = serverInfo.ServerId
			oldGroupId = serverInfo.GroupId
			serverInfo.groupServers.ServerId = append(serverInfo.groupServers.ServerId, targetServer.ServerId)
			return false
		}
		return true
	})

	if !isNeedNewGroup {
		targetServer.GroupId = oldGroupId
		targetServer.groupServers.ServerId = append(targetServer.groupServers.ServerId, oldServerId)
		targetServer.groupServers.ServerId = append(targetServer.groupServers.ServerId, targetServer.ServerId)
		return
	}
	if !reset && NowGroupMax != self.ServerGroupConfig.GroupMax {
		GetChampionAllMgr().AddInfo(CHAMPION_GROUP_TYPE_GROUP, self.ServerZoneConfig.GroupMax)
	}
	self.ServerGroupConfig.GroupMax = NowGroupMax
	targetServer.GroupId = self.ServerGroupConfig.GroupMax
	targetServer.groupServers.ServerId = append(targetServer.groupServers.ServerId, targetServer.ServerId)
	return
}

func (self *ServerGroupInfo) IsHistory(serverId int) bool {
	if self.lastGroupServers != nil {
		for _, oldServer := range self.lastGroupServers.ServerId {
			if oldServer == serverId {
				return true
			}
		}
	}
	return false
}

func (self *ServerGroupMgr) CheckFitSafeZone(targetServer *ServerZoneInfo, reset bool) {
	// 获得鬼灭世界开启时间
	fitTime := self.GetCrossServerFitTimeZone()
	nowTime := model.TimeServer().Unix()
	// 已经持续了多久 比开启时间要短则跳过 说明还没到开启时间
	if nowTime-targetServer.param.OpenTime < fitTime {
		return
	}
	// 如果已经分组则返回
	if targetServer.ZoneId > 0 {
		return
	}
	//看看当前组缺不缺对手
	isNeedNewZone := true
	oldServerId := []int{}
	oldZoneId := 0
	NowZoneMax := self.ServerZoneConfig.GroupMax

	//拿到需要分配的服务器的分组级别
	zoneIdConfig := self.GetZoneingId(targetServer)
	if zoneIdConfig == nil {
		return
	}

	self.ServerZone.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerZoneInfo)
		if serverInfo == nil {
			return true
		}
		//! 去掉自己或者错误的
		if serverInfo.ZoneId == 0 {
			return true
		}
		// 判断分组级别是否一致
		oldzoneIdConfig := self.GetZoneingId(serverInfo)
		if oldzoneIdConfig == nil {
			return true
		}
		// 找到分组相同的组
		if oldzoneIdConfig.GroupingId != zoneIdConfig.GroupingId {
			return true
		}
		// 找到分组相同的组
		if len(serverInfo.zoneServers.ServerId) < zoneIdConfig.ZoneNum && len(serverInfo.zoneServers.ServerId) > 0 {
			// 和上次已经分过组了
			//if serverInfo.IsHistory(targetServer.ServerId) {
			//	return true
			//}
			// 找到了不需要新分组 把这个服加入这个空一个的分组中
			isNeedNewZone = false
			oldServerId = serverInfo.zoneServers.ServerId
			oldZoneId = serverInfo.ZoneId
			//serverInfo.zoneServers.ServerId = append(serverInfo.zoneServers.ServerId, targetServer.ServerId)
			return false
		}
		return true
	})

	if !isNeedNewZone {
		targetServer.ZoneId = oldZoneId
		targetServer.zoneServers.ServerId = append(targetServer.zoneServers.ServerId, oldServerId...)

		//! 对所有分组的成员增加新的ServerId
		self.ServerZone.Range(func(serverId, value interface{}) bool {
			serverInfo := value.(*ServerZoneInfo)
			if serverInfo == nil {
				return true
			}
			//! 排除所有的分组
			if serverInfo.ZoneId != oldZoneId {
				return true
			}

			serverInfo.zoneServers.ServerId = append(serverInfo.zoneServers.ServerId, targetServer.ServerId)
			return true
		})

		//targetServer.zoneServers.ServerId = append(targetServer.zoneServers.ServerId, targetServer.ServerId)
		return
	}

	//! 自己是新的一组，单独成为新的一组
	self.ServerZoneConfig.GroupMax = NowZoneMax + 1
	targetServer.ZoneId = self.ServerZoneConfig.GroupMax
	targetServer.zoneServers.ServerId = append(targetServer.zoneServers.ServerId, targetServer.ServerId)

	if !reset {
		GetChampionAllMgr().AddInfo(CHAMPION_GROUP_TYPE_ZONE, self.ServerZoneConfig.GroupMax)
	}
	return
}

func (self *ServerZoneInfo) IsHistory(serverId int) bool {
	if self.lastZoneServers != nil {
		for _, oldServer := range self.lastZoneServers.ServerId {
			if oldServer == serverId {
				return true
			}
		}
	}
	return false
}

func (self *ServerGroupMgr) SaveHistory() {

	startTime := self.ServerGroupConfig.StartTime
	endTime := self.ServerGroupConfig.EndTime
	periods := self.ServerGroupConfig.Periods
	//清空分组
	self.ServerGroup.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerGroupInfo)
		if serverInfo == nil {
			return true
		}
		serverInfo.Encode()
		serverGroupInfoHistory := new(ServerGroupInfoHistory)
		serverGroupInfoHistory.ServerId = serverInfo.ServerId
		serverGroupInfoHistory.GroupId = serverInfo.GroupId
		serverGroupInfoHistory.GroupServers = serverInfo.GroupServers
		serverGroupInfoHistory.LastGroupServers = serverInfo.LastGroupServers
		serverGroupInfoHistory.Param = serverInfo.Param
		serverGroupInfoHistory.ActivityInfo = serverInfo.ActivityInfo
		serverGroupInfoHistory.StartTime = startTime
		serverGroupInfoHistory.EndTime = endTime
		serverGroupInfoHistory.Periods = periods
		db.InsertTable(TABLE_SERVERGROUP_HISTORY, serverGroupInfoHistory, 0, false)
		return true
	})
	return
}

func (self *ServerGroupMgr) SaveHistoryZone() {
	startTime := self.ServerZoneConfig.StartTime
	endTime := self.ServerZoneConfig.EndTime
	periods := self.ServerZoneConfig.Periods
	//清空分组
	self.ServerZone.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerZoneInfo)
		if serverInfo == nil {
			return true
		}
		serverInfo.Encode()
		serverZoneInfoHistory := new(ServerZoneInfoHistory)
		serverZoneInfoHistory.ServerId = serverInfo.ServerId
		serverZoneInfoHistory.ZoneId = serverInfo.ZoneId
		serverZoneInfoHistory.ZoneServers = serverInfo.ZoneServers
		serverZoneInfoHistory.LastZoneServers = serverInfo.LastZoneServers
		serverZoneInfoHistory.Param = serverInfo.Param
		serverZoneInfoHistory.ActivityInfo = serverInfo.ActivityInfo
		serverZoneInfoHistory.StartTime = startTime
		serverZoneInfoHistory.EndTime = endTime
		serverZoneInfoHistory.Periods = periods
		db.InsertTable(TABLE_SERVERZONE_HISTORY, serverZoneInfoHistory, 0, false)
		return true
	})
	return
}

func (self *ServerGroupMgr) ServerGroupNext(w http.ResponseWriter, r *http.Request) {
	self.ServerGroupConfig.EndTime = 0
	w.Write([]byte(fmt.Sprintf("即将开始分组\n")))
}
func (self *ServerGroupMgr) ServerZoneNext(w http.ResponseWriter, r *http.Request) {
	self.ServerZoneConfig.EndTime = 0
	w.Write([]byte(fmt.Sprintf("即将开始分组\n")))
}
func (self *ServerGroupMgr) GetGroups(serverId int) []*ServerInfo {
	rel := make([]*ServerInfo, 0)
	data, ok := self.ServerGroup.Load(serverId)
	if !ok {
		return nil
	}
	serverInfo := data.(*ServerGroupInfo)
	if serverInfo == nil {
		return nil
	}
	if serverInfo.groupServers == nil {
		return nil
	}
	for _, v := range serverInfo.groupServers.ServerId {
		nowData, nowOk := self.ServerGroup.Load(v)
		if !nowOk {
			continue
		}
		nowServerInfo := nowData.(*ServerGroupInfo)
		if nowServerInfo == nil {
			continue
		}
		serverInfo := new(ServerInfo)
		serverInfo.ServerId = v
		if nowServerInfo.param != nil {
			serverInfo.MaxFightName = nowServerInfo.param.MaxFightName
		}
		rel = append(rel, serverInfo)
	}
	return rel
}

func (self *ServerGroupMgr) GetZones(serverId int) []*ServerInfo {
	rel := make([]*ServerInfo, 0)
	data, ok := self.ServerZone.Load(serverId)
	if !ok {
		return nil
	}
	serverInfo := data.(*ServerZoneInfo)
	if serverInfo == nil {
		return nil
	}
	if serverInfo.zoneServers == nil {
		return nil
	}
	for _, v := range serverInfo.zoneServers.ServerId {
		nowData, nowOk := self.ServerZone.Load(v)
		if !nowOk {
			continue
		}
		nowServerInfo := nowData.(*ServerZoneInfo)
		if nowServerInfo == nil {
			continue
		}
		serverInfo := new(ServerInfo)
		serverInfo.ServerId = v
		if nowServerInfo.param != nil {
			serverInfo.MaxFightName = nowServerInfo.param.MaxFightName
		}
		rel = append(rel, serverInfo)
	}
	return rel
}

func (self *ServerGroupMgr) ClearGroupChat() {
	GetChatMgr().ClearServerChat()
	return
}

func (self *ServerGroupMgr) ClearZoneChat() {
	GetChatMgr().ClearDemonslayerChat()
	return
}
func (self *ServerGroupMgr) ClearChampion(groupType int) {
	return
}

func (self *ServerGroupMgr) ResetRank() {
	GetRankMgr().ResetSortRank()
	return
}

func (self *ServerGroupMgr) GetLevelArenaPeriods() int {
	if self.ServerGroupConfig == nil {
		return 0
	}
	return self.ServerGroupConfig.crossServerPlayConfig.LevelArenaConfig.Periods
}

func (self *ServerGroupMgr) GetCosmicArenaPeriods() int {
	if self.ServerZoneConfig == nil {
		return 0
	}
	return self.ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.Periods
}

func (self *ServerGroupMgr) GetGroupingId(servergroupinfo *ServerGroupInfo) *conf.ServerGrouping {
	nowTime := model.TimeServer().Unix()
	day := int((nowTime-servergroupinfo.param.OpenTime)/utils.DAY_SECS) + 1
	if day <= 0 {
		return nil
	}
	for _, v := range conf.ServerGroupingSlice {
		if day >= v.GroupStar && day <= v.GroupFinish {
			return v
		}
	}
	return nil
}

func (self *ServerGroupMgr) GetZoneingId(servergroupinfo *ServerZoneInfo) *conf.ServerZone {
	nowTime := model.TimeServer().Unix()
	day := int((nowTime-servergroupinfo.param.OpenTime)/utils.DAY_SECS) + 1
	if day <= 0 {
		return nil
	}
	for _, v := range conf.ServerZoneSlice {
		if day >= v.GroupStar && day <= v.GroupFinish {
			return v
		}
	}
	return nil
}

func (self *ServerGroupMgr) ResetServerGroupNew() {

	self.ServerGroup.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerGroupInfo)
		if serverInfo == nil {
			return true
		}
		serverInfo.lastGroupServers = serverInfo.groupServers
		serverInfo.GroupId = 0
		serverInfo.groupServers = new(GroupServers)
		serverInfo.Encode()
		return true
	})

	self.ServerGroupConfig.GroupMax = 1
	//将服务器根据新规则分组
	serverMap := make(map[int]map[int]*ServerGroupInfo, 0)
	self.ServerGroup.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerGroupInfo)
		if serverInfo == nil {
			return true
		}
		//拿到当前服务器的分组级别
		groupingIdConfig := self.GetGroupingId(serverInfo)
		if groupingIdConfig == nil {
			return true
		}
		_, ok := serverMap[groupingIdConfig.GroupingId]
		if !ok {
			serverMap[groupingIdConfig.GroupingId] = make(map[int]*ServerGroupInfo)
		}
		serverMap[groupingIdConfig.GroupingId][serverInfo.ServerId] = serverInfo
		return true
	})
	utils.LogDebug(utils.HF_JtoA(serverMap))
	//辅助运算
	nowList := make([]*ServerGroupInfo, 0)
	for _, v := range conf.ServerGroupingSlice {
		if v.GroupNum == 0 {
			continue
		}
		//将对应分组的服务器加入到待分组列表里
		for _, serverInfo := range serverMap[v.GroupingId] {
			nowList = append(nowList, serverInfo)
		}
		//2个服务器1组的分组需要走以前的老的逻辑,默认是配置表的最后几行，所以放入备选组 但放最后处理
		if v.GroupNum == 2 {
			continue
		}
		//根据数量进行分组
		for {
			if len(nowList) < v.GroupNum {
				break
			}
			groupList := nowList[:v.GroupNum]
			serverIds := make([]int, 0)
			for _, nowServer := range groupList {
				serverIds = append(serverIds, nowServer.ServerId)
			}
			for _, nowServer := range groupList {
				nowServer.groupServers = new(GroupServers)
				nowServer.groupServers.ServerId = serverIds
				nowServer.GroupId = self.ServerGroupConfig.GroupMax
			}
			self.ServerGroupConfig.GroupMax++
			nowList = nowList[v.GroupNum:]
		}
	}
	for _, v := range nowList {
		self.CheckFitSafe(v, true)
	}
	return
}

func (self *ServerGroupMgr) ResetServerZoneNew() {
	self.ServerZone.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerZoneInfo)
		if serverInfo == nil {
			return true
		}
		serverInfo.lastZoneServers = serverInfo.zoneServers
		serverInfo.ZoneId = 0
		serverInfo.zoneServers = new(GroupServers)
		serverInfo.Encode()
		return true
	})
	self.ServerZoneConfig.GroupMax = 1
	//将服务器根据新规则分组
	serverMap := make(map[int]map[int]*ServerZoneInfo, 0)
	self.ServerZone.Range(func(serverId, value interface{}) bool {
		serverInfo := value.(*ServerZoneInfo)
		if serverInfo == nil {
			return true
		}
		//拿到当前服务器的分组级别
		zoneConfig := self.GetZoneingId(serverInfo)
		if zoneConfig == nil {
			return true
		}
		_, ok := serverMap[zoneConfig.GroupingId]
		if !ok {
			serverMap[zoneConfig.GroupingId] = make(map[int]*ServerZoneInfo)
		}
		serverMap[zoneConfig.GroupingId][serverInfo.ServerId] = serverInfo
		return true
	})
	utils.LogDebug(utils.HF_JtoA(serverMap))
	//辅助运算
	nowList := make([]*ServerZoneInfo, 0)
	for _, v := range conf.ServerZoneSlice {
		if v.ZoneNum == 0 {
			continue
		}
		//将对应分组的服务器加入到待分组列表里
		for _, serverInfo := range serverMap[v.GroupingId] {
			nowList = append(nowList, serverInfo)
		}
		//2个服务器1组的分组需要走以前的老的逻辑,默认是配置表的最后几行，所以放入备选组 但放最后处理

		//根据数量进行分组
		for {
			if len(nowList) < v.ZoneNum {
				break
			}
			groupList := nowList[:v.ZoneNum]
			serverIds := make([]int, 0)
			for _, nowServer := range groupList {
				serverIds = append(serverIds, nowServer.ServerId)
			}
			for _, nowServer := range groupList {
				nowServer.zoneServers = new(GroupServers)
				nowServer.zoneServers.ServerId = serverIds
				nowServer.ZoneId = self.ServerZoneConfig.GroupMax
			}
			self.ServerZoneConfig.GroupMax++
			nowList = nowList[v.ZoneNum:]
		}
	}
	for _, v := range nowList {
		self.CheckFitSafeZone(v, true)
	}
	return
}
