package crossserver

import (
	"fmt"
	json "github.com/bytedance/sonic"
	"log"
	"master/center/battle"
	"master/center/conf"
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
	"net/http"
	"runtime/debug"
	"sort"
	"sync"
	"time"
)

const (
	TABLE_CROSS_SERVER_LEVELARENA             = "tbl_crossserverlevelarena"
	CROSS_SERVER_LEVELARENA_SCORE_START       = 1000
	CROSS_SERVER_LEVELARENA_SECONDKILL_PARAM  = 20000
	CROSS_SERVER_LEVELARENA_RANK_MAX          = 100 //
	CROSS_SERVER_LEVELARENA_ENEMY_CHOOSE_LIST = 10  //对手备选数量，提高效率
	CROSS_SERVER_LEVELARENA_SCORELV_FIND      = 12  //大于这个段位，采取扩大搜索
)

type lstCrossServerLevelArenaRank []*CrossServerLevelArenaUser

func (s lstCrossServerLevelArenaRank) Len() int      { return len(s) }
func (s lstCrossServerLevelArenaRank) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s lstCrossServerLevelArenaRank) Less(i, j int) bool {
	if s[i].IsSign == s[j].IsSign {
		if s[i].Score == s[j].Score {
			return s[i].Uid < s[j].Uid
		} else {
			return s[i].Score >= s[j].Score
		}
	}
	return s[i].IsSign >= s[j].IsSign
}

type CrossServerLevelArenaDB struct {
	Id         int    `json:"id"`
	Uid        int64  `json:"uid"`
	Periods    int    `json:"periods"`
	GroupId    int    `json:"groupid"`
	SvrId      int    `json:"svrid"`
	Info       string `json:"info"`
	FightInfos string `json:"fightinfos"`

	info       *CrossServerLevelArenaUser
	fightInfos *model.JS_FightInfo
	db.DataUpdate
}

type CrossServerLevelArenaUser struct {
	Uid             int64                               `json:"uid"`
	SvrId           int                                 `json:"svrid"`
	SvrName         string                              `json:"svrname"`
	UName           string                              `json:"uname"`
	UnionName       string                              `json:"unionname"`
	Score           int                                 `json:"score"`
	ScoreLv         int                                 `json:"scorelv"`
	ScoreLvMax      int                                 `json:"scorelvmax"`
	RankPos         int                                 `json:"rankpos"`
	Level           int                                 `json:"level"`
	Vip             int                                 `json:"vip"`
	Icon            int                                 `json:"icon"`
	Portrait        int                                 `json:"portrait"`
	Title           int                                 `json:"title"`
	Fight           int64                               `json:"fight"`
	Robot           int                                 `json:"robot"`
	LevelArenaFight []*model.CrossServerLevelArenaFight `json:"arenafight"` //战报集
	CantList        map[int64]int                       `json:"-"`
	IsSign          int                                 `json:"issign"`   //是否正式参赛
	AllTimes        int                                 `json:"alltimes"` //总场
	WinTimes        int                                 `json:"wintimes"` //胜场
	HeroId          int                                 `json:"heroid"`   //! 给客户端显示半身像
}

type CrossServerLevelArenaEnd struct {
	UserInfo     *CrossServerLevelArenaUser `json:"userinfo"`
	BattleRecord *model.BattleRecord        `json:"battlerecord"`
	Record       []byte                     `json:"record"`
}

// 跨服段位赛 跨服
type CrossServerLevelArenaMgr struct {
	CrossServerLevelArenaInfo *sync.Map                       //key: group
	JJCRobotConfigMap         map[int]map[int]*JJCRobotConfig //key:  段位scorelv
	RecordId                  int64
	Config                    CrossServerLevelArenaConfig
	BattleListen              *sync.Map
	BattleListenRoll          *sync.Map
	Mu                        *sync.RWMutex //sync.map 不能规避逻辑上的互斥
}

type CrossServerLevelArenaConfig struct {
	ScoreStart      int //50 玩家初次进入需求积分
	SecondKillParam int //51 碾压战力相差万分比系数
}

type CrossServerLevelArenaInfo struct {
	groupId      int
	periods      int
	rankInfo     []*CrossServerLevelArenaUser       //排行数据    key:rank
	db_list      map[int64]*CrossServerLevelArenaDB //数据存储
	Locker       *sync.RWMutex
	rankInfoShow []*CrossServerLevelArenaUser //给玩家看的排行
}

var crossServerLevelArenaMgr *CrossServerLevelArenaMgr = nil

func GetCrossServerLevelArenaMgr() *CrossServerLevelArenaMgr {
	if crossServerLevelArenaMgr == nil {
		crossServerLevelArenaMgr = new(CrossServerLevelArenaMgr)
		crossServerLevelArenaMgr.CrossServerLevelArenaInfo = new(sync.Map)
		crossServerLevelArenaMgr.BattleListen = new(sync.Map)
		crossServerLevelArenaMgr.BattleListenRoll = new(sync.Map)
		crossServerLevelArenaMgr.Mu = new(sync.RWMutex)
		crossServerLevelArenaMgr.LoadCsv()
	}
	return crossServerLevelArenaMgr
}

func GetRollFight(fightInfos *model.JS_FightInfo) int64 {
	count := 0
	fightAll := int64(0)
	for _, v := range fightInfos.Heroinfo {
		if v.Heroid == 0 {
			continue
		}
		count += 1
		fightAll += v.Fight
	}
	if count == 0 {
		return 0
	}
	return fightAll / int64(count)
}

func GetRankInfoCrossServerLevelArena(data *CrossServerLevelArenaUser) *model.RankInfo {
	rel := new(model.RankInfo)
	if data == nil {
		return rel
	}
	rel.Uid = data.Uid
	rel.SvrId = data.SvrId
	rel.UName = data.UName
	rel.Level = data.Level
	rel.Vip = data.Vip
	rel.Icon = data.Icon
	rel.Portrait = data.Portrait
	rel.Title = data.Title
	rel.Fight = data.Fight
	rel.Num = int64(data.Score)
	rel.UnionName = data.UnionName
	rel.Param = int64(data.ScoreLv)
	rel.HeroId = data.HeroId
	//if data.IsSign == core.LOGIC_TRUE {
	rel.Rank = data.RankPos
	//}
	return rel
}

func (self *CrossServerLevelArenaMgr) Run() {
	defer func() {
		x := recover()
		if x != nil {
			log.Println(x, string(debug.Stack()))
			utils.LogError(x, string(debug.Stack()))
		}
	}()

	ticker := time.NewTicker(time.Second * 1)
	for {
		select {
		case <-ticker.C:
			self.CheckBattleFinish()
			//先用实时算法
			//if core.TimeServer().Unix()%30 == 0 {
			//	self.SortRank()
			//}
		}
	}
	ticker.Stop()
}

func (self *CrossServerLevelArenaInfo) GetRobotUser(config *JJCRobotConfig) *CrossServerLevelArenaUser {
	data := new(CrossServerLevelArenaUser)
	data.Uid = int64(config.Jjcscore)
	return data
}

func (self *CrossServerLevelArenaMgr) LoadCsv() {
	self.JJCRobotConfigMap = make(map[int]map[int]*JJCRobotConfig, 0)
	JJCRobotConfigTemp := make([]*JJCRobotConfig, 0)
	utils.GetCsvUtilMgr().LoadCsv("Jjc_Robot", &JJCRobotConfigTemp)
	for _, v := range JJCRobotConfigTemp {
		if v.Type != 6 {
			continue
		}
		_, ok := self.JJCRobotConfigMap[v.Jjcdan]
		if !ok {
			self.JJCRobotConfigMap[v.Jjcdan] = make(map[int]*JJCRobotConfig)
		}
		self.JJCRobotConfigMap[v.Jjcdan][v.Id] = v
	}

	configNum := 0
	//50 玩家初次进入需求积分
	self.Config.ScoreStart = CROSS_SERVER_LEVELARENA_SCORE_START
	configNum = conf.GetInitNum(50)
	if configNum > 0 {
		self.Config.ScoreStart = configNum
		configNum = 0
	}
	//51 碾压战力相差万分比系数
	self.Config.SecondKillParam = CROSS_SERVER_LEVELARENA_SECONDKILL_PARAM
	configNum = conf.GetInitNum(51)
	if configNum > 0 {
		self.Config.SecondKillParam = configNum
		configNum = 0
	}
	return
}

func (self *CrossServerLevelArenaDB) Encode() {
	self.Info = utils.HF_JtoA(self.info)
	self.FightInfos = utils.HF_JtoA(self.fightInfos)
}

func (self *CrossServerLevelArenaDB) Decode() {
	json.Unmarshal([]byte(self.Info), &self.info)
	json.Unmarshal([]byte(self.FightInfos), &self.fightInfos)
}

// 存储数据库
func (self *CrossServerLevelArenaMgr) OnSave() {
	self.CrossServerLevelArenaInfo.Range(func(key, info interface{}) bool {
		if info == nil {
			return true
		}

		serverGroup := info.(*CrossServerLevelArenaInfo)
		serverGroup.Locker.RLock()
		for _, user := range serverGroup.db_list {
			index := user.info.RankPos - 1
			if index < 0 {
				continue
			}
			user.info = serverGroup.rankInfo[user.info.RankPos-1]
			user.Encode()
			user.UpdateEx("periods", user.Periods)
		}
		serverGroup.Locker.RUnlock()
		return true
	})
}

func (self *CrossServerLevelArenaMgr) GetAllData() {
	periods := GetServerGroupMgr().GetLevelArenaPeriods()
	serverGroupMap := GetServerGroupMgr().GetServerGroupMap()

	queryStr := fmt.Sprintf("select * from `%s` where periods = %d;", TABLE_CROSS_SERVER_LEVELARENA, periods)
	var msg CrossServerLevelArenaDB
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	resCorrect := make(map[int64]*CrossServerLevelArenaDB)
	for i := 0; i < len(res); i++ {
		data := res[i].(*CrossServerLevelArenaDB)
		//看看是否匹配分组
		groupId, groupOk := serverGroupMap[data.SvrId]
		if groupOk && groupId != data.GroupId {
			continue
		}
		data.Decode()
		if data.info == nil {
			continue
		}
		oldInfo, ok := resCorrect[data.Uid]
		if ok {
			if oldInfo.info.Score > data.info.Score {
				continue
			}
		}
		resCorrect[data.Uid] = data
	}

	for _, data := range resCorrect {
		info, ok := self.CrossServerLevelArenaInfo.Load(data.GroupId)
		if !ok {
			info = self.NewCrossServerLevelArenaInfo(data.GroupId, periods)
			self.CrossServerLevelArenaInfo.Store(data.GroupId, info)
		}

		if data.info == nil {
			data.info = new(CrossServerLevelArenaUser)
			data.info.Uid = data.Uid
			data.info.SetScore(self.Config.ScoreStart)
			continue
		}
		infoData := info.(*CrossServerLevelArenaInfo)
		data.Init(TABLE_CROSS_SERVER_LEVELARENA, data, false)
		infoData.db_list[data.Uid] = data
	}
	self.CrossServerLevelArenaInfo.Range(func(key, info interface{}) bool {
		if info == nil {
			return true
		}

		serverGroup := info.(*CrossServerLevelArenaInfo)
		serverGroup.InitRank()
		return true
	})
}
func (self *CrossServerLevelArenaUser) SetScore(score int) {
	self.Score = score
	if self.ScoreLv == 0 {
		self.ScoreLv = conf.CalScoreLv(score, 0)
	}
}
func (self *CrossServerLevelArenaInfo) InitRank() {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	self.rankInfo = make([]*CrossServerLevelArenaUser, 0)
	for _, v := range self.db_list {
		self.rankInfo = append(self.rankInfo, v.info)
	}
	sort.Sort(lstCrossServerLevelArenaRank(self.rankInfo))
	for index, v := range self.rankInfo {
		v.RankPos = index + 1
		v.ScoreLv = conf.CalScoreLv(v.Score, v.RankPos)
	}
	return
}
func (self *CrossServerLevelArenaMgr) NewCrossServerLevelArenaInfo(groupId int, periods int) *CrossServerLevelArenaInfo {
	data := new(CrossServerLevelArenaInfo)
	data.groupId = groupId
	data.periods = periods
	data.rankInfo = make([]*CrossServerLevelArenaUser, 0)
	data.db_list = make(map[int64]*CrossServerLevelArenaDB)
	data.Locker = new(sync.RWMutex)
	return data
}
func (self *CrossServerLevelArenaMgr) GetCrossServerLevelArenaInfo(serverId int) *CrossServerLevelArenaInfo {
	self.Mu.Lock()
	defer self.Mu.Unlock()
	//获得该服务器分组
	groupId := GetServerGroupMgr().GetGroupId(serverId)
	periods := GetServerGroupMgr().GetLevelArenaPeriods()

	info, ok := self.CrossServerLevelArenaInfo.Load(groupId)
	if !ok {
		info = self.NewCrossServerLevelArenaInfo(groupId, periods)
		self.CrossServerLevelArenaInfo.Store(groupId, info)
	}

	return info.(*CrossServerLevelArenaInfo)
}
func (self *CrossServerLevelArenaMgr) GetCrossServerLevelArenaInfoByGroupId(groupId int) *CrossServerLevelArenaInfo {
	info, ok := self.CrossServerLevelArenaInfo.Load(groupId)
	if !ok {
		return nil
	}

	return info.(*CrossServerLevelArenaInfo)
}
func (self *CrossServerLevelArenaMgr) GetGroupConfig() string {
	//config := GetServerGroupMgr().GetLevelArenaConfig()
	groupConfig := GetServerGroupMgr().GetGroupConfig()
	return utils.HF_JtoA(groupConfig)
}
func (self *CrossServerLevelArenaMgr) GetConfig(serverId int) (string, string, string, string) {
	config := GetServerGroupMgr().GetLevelArenaConfig()
	groupConfig := GetServerGroupMgr().GetGroupConfig()
	groups := GetServerGroupMgr().GetGroups(serverId)
	levelLevelArenaConfig := GetServerGroupMgr().GetLevelArenaConfig()
	return utils.HF_JtoA(config), utils.HF_JtoA(groupConfig), utils.HF_JtoA(groups), utils.HF_JtoA(levelLevelArenaConfig)
}
func (self *CrossServerLevelArenaMgr) GetInfo(uid int64, serverId int, fightInfos *model.JS_FightInfo) (int, string) {
	if fightInfos == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	crossServerLevelArenaInfo := self.GetCrossServerLevelArenaInfo(serverId)
	if crossServerLevelArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	info := crossServerLevelArenaInfo.GetInfo(uid, fightInfos)
	infoStr := ""
	if info != nil {
		infoStr = utils.HF_JtoA(info)
	}
	return RETCODE_DATA_CROSS_OK, infoStr
}

func (self *CrossServerLevelArenaMgr) FightStart(uid int64, serverId int, fightInfos *model.JS_FightInfo) (int, string, string) {
	if fightInfos == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	crossServerLevelArenaInfo := self.GetCrossServerLevelArenaInfo(serverId)
	if crossServerLevelArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	selfInfo := crossServerLevelArenaInfo.GetInfoDB(uid, fightInfos)
	if selfInfo == nil || selfInfo.fightInfos == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	//寻找对手并加入战斗服计算
	_, enemyFightInfo := crossServerLevelArenaInfo.GetEnemy(uid)
	//检查对手是机器人的话
	if enemyFightInfo == nil || enemyFightInfo.Uid == 0 {
		enemyFightInfo = self.GetRobotFightInfo(selfInfo.info.ScoreLv)
		enemyFightInfo.Param = int64(selfInfo.info.ScoreLv)
	}
	//如果触发碾压 则不进入战斗服
	isRolling := true
	attackFight := GetRollFight(selfInfo.fightInfos)
	defenceFight := GetRollFight(enemyFightInfo)
	defenceFight = defenceFight * int64(self.Config.SecondKillParam) / 10000
	if attackFight < defenceFight {
		isRolling = false
	}
	//添加战斗给战斗服
	random := model.TimeServer().Unix()
	if isRolling == true {
		//ret := battle.GetFightMgr().AddFightID(selfInfo.fightInfos, enemyFightInfo, int(random), 0, 0)
		battleData := &model.ArenaFightList{
			Type:    0,
			FightId: battle.GetFightMgr().GetFightInfoID(),
			Random:  random,
			Time:    model.TimeServer().Unix(),
			Attack:  selfInfo.fightInfos,
			Defend:  enemyFightInfo,
			BossId:  0}
		//用回调函数去处理
		self.BattleListenRoll.Store(battleData.FightId, battleData)
	} else {
		ret := battle.GetFightMgr().AddFightID(selfInfo.fightInfos, enemyFightInfo, int(random), 0, 0, core.BATTLE_TYPE_LEVELARENA, 0)
		battleData := &model.ArenaFightList{
			Type:    0,
			FightId: ret,
			Random:  random,
			Time:    model.TimeServer().Unix(),
			Attack:  selfInfo.fightInfos,
			Defend:  enemyFightInfo,
			BossId:  0}
		self.BattleListen.Store(battleData.FightId, battleData)
	}
	//获取随机人物给客户端做演示
	matchInfo := crossServerLevelArenaInfo.GetMatchShow(uid)
	matchInfoStr := ""
	if len(matchInfo) > 0 {
		matchInfoStr = utils.HF_JtoA(matchInfo)
	}
	selfInfoStr := utils.HF_JtoA(selfInfo.info)
	return RETCODE_DATA_CROSS_OK, matchInfoStr, selfInfoStr
}
func (self *CrossServerLevelArenaInfo) GetInfoDB(uid int64, fightInfos *model.JS_FightInfo) *CrossServerLevelArenaDB {
	if fightInfos == nil {
		return nil
	}
	self.Locker.Lock()
	defer self.Locker.Unlock()
	infoDB, ok := self.db_list[uid]
	if !ok {
		infoDB = new(CrossServerLevelArenaDB)
		infoDB.Uid = uid
		infoDB.Periods = self.periods
		infoDB.GroupId = self.groupId
		infoDB.SvrId = fightInfos.Server
		infoDB.fightInfos = fightInfos
		infoDB.info = self.NewInfo(fightInfos)

		infoDB.Encode()
		id := db.InsertTable(TABLE_CROSS_SERVER_LEVELARENA, infoDB, 0, false)
		infoDB.Id = int(id)
		infoDB.Init(TABLE_CROSS_SERVER_LEVELARENA, infoDB, false)
		infoDB.info.RankPos = len(self.rankInfo) + 1
		self.rankInfo = append(self.rankInfo, infoDB.info)
		self.db_list[uid] = infoDB
	}
	infoDB.SetFightInfo(fightInfos)
	return infoDB
}

func (self *CrossServerLevelArenaInfo) GetFightinfo(uid int64) *model.JS_FightInfo {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	infoDB, ok := self.db_list[uid]
	if !ok {
		return nil
	}
	return infoDB.fightInfos
}
func (self *CrossServerLevelArenaInfo) GetInfo(uid int64, fightInfos *model.JS_FightInfo) *CrossServerLevelArenaUser {
	infoDB := self.GetInfoDB(uid, fightInfos)
	if infoDB == nil {
		return nil
	}
	infoDB.SetFightInfo(fightInfos)
	if infoDB.info != nil && fightInfos != nil {
		infoDB.info.Fight = 0
		infoDB.info.UName = infoDB.fightInfos.Uname
		infoDB.info.Icon = infoDB.fightInfos.Iconid
		infoDB.info.Portrait = infoDB.fightInfos.Portrait
		infoDB.info.Title = infoDB.fightInfos.Title
		infoDB.info.Level = infoDB.fightInfos.Level
		realValue := infoDB.fightInfos.Deffight / 100
		infoDB.info.Fight += realValue * 100
		if len(infoDB.fightInfos.Heroinfo) > 0 {
			infoDB.info.HeroId = infoDB.fightInfos.Heroinfo[0].Heroid
		}
	}
	return infoDB.info
}

func (self *CrossServerLevelArenaInfo) GetDanInitScore(oldScoreLv int) int {
	score := GetCrossServerLevelArenaMgr().Config.ScoreStart
	config := conf.GetDanConfig(oldScoreLv)
	if config != nil {
		score = config.DanScore
	}
	return score
}
func (self *CrossServerLevelArenaInfo) NewInfo(fightInfo *model.JS_FightInfo) *CrossServerLevelArenaUser {
	data := new(CrossServerLevelArenaUser)
	data.Uid = fightInfo.Uid
	data.SvrId = fightInfo.Server
	data.UName = fightInfo.Uname
	data.UnionName = fightInfo.UnionName
	oldScoreLv := GetOfflineInfoMgr().GetLevelArenaScoreLvHistory(data.Uid)
	score := self.GetDanInitScore(oldScoreLv)
	data.SetScore(score)
	data.Level = fightInfo.Level
	data.Vip = fightInfo.Vip
	data.Icon = fightInfo.Iconid
	if len(fightInfo.Heroinfo) > 0 {
		data.HeroId = fightInfo.Heroinfo[0].Heroid
	}
	data.Portrait = fightInfo.Portrait
	data.Title = fightInfo.Title
	realValue := fightInfo.Deffight / 100
	data.Fight += realValue * 100
	return data
}

func (self *CrossServerLevelArenaInfo) GetEnemy(uid int64) (*CrossServerLevelArenaUser, *model.JS_FightInfo) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	relUser := new(CrossServerLevelArenaUser)
	relFight := new(model.JS_FightInfo)
	info, ok := self.db_list[uid]
	if !ok {
		return nil, nil
	}
	if info.info == nil {
		return nil, nil
	}
	index := info.info.RankPos - 1
	if index < 0 || index >= len(self.rankInfo) {
		return nil, nil
	}
	//先把自己2个档位差的人全部取出来，作为备选列表
	score := self.rankInfo[index].Score
	scoreLv := self.rankInfo[index].ScoreLv
	scoreLvMap := make(map[int]map[int64]int)

	for i := index - 1; i >= 0; i-- {
		//if self.rankInfo[i].IsSign == core.LOGIC_FALSE {
		//	continue
		//}
		nowScoreLv := self.rankInfo[i].ScoreLv
		if self.IsStopFind(scoreLv, nowScoreLv-scoreLv) {
			break
		}
		_, ok := scoreLvMap[nowScoreLv]
		if !ok {
			scoreLvMap[nowScoreLv] = make(map[int64]int)
		}
		if len(scoreLvMap[nowScoreLv]) >= CROSS_SERVER_LEVELARENA_ENEMY_CHOOSE_LIST {
			continue
		}
		scoreLvMap[nowScoreLv][self.rankInfo[i].Uid] = model.LOGIC_TRUE
	}
	for i := index + 1; i < len(self.rankInfo); i++ {
		//if self.rankInfo[i].IsSign == core.LOGIC_FALSE {
		//	continue
		//}
		nowScoreLv := self.rankInfo[i].ScoreLv
		if self.IsStopFind(scoreLv, scoreLv-nowScoreLv) {
			break
		}
		_, ok := scoreLvMap[nowScoreLv]
		if !ok {
			scoreLvMap[nowScoreLv] = make(map[int64]int)
		}
		if len(scoreLvMap[nowScoreLv]) >= CROSS_SERVER_LEVELARENA_ENEMY_CHOOSE_LIST {
			continue
		}
		scoreLvMap[nowScoreLv][self.rankInfo[i].Uid] = model.LOGIC_TRUE
	}
	cantList := make(map[int64]int)
	for k := range self.rankInfo[index].CantList {
		cantList[k] = model.LOGIC_TRUE
	}
	cantList[uid] = model.LOGIC_TRUE

	enemy, cantList := self.GetFirstEnemy(scoreLvMap, cantList, scoreLv, score)
	relUser = enemy
	for k := range self.rankInfo[index].CantList {
		delete(cantList, k)
	}
	self.rankInfo[index].CantList = cantList

	enemyInfo, ok := self.db_list[relUser.Uid]
	if ok {
		relFight = enemyInfo.fightInfos
	} else {

	}

	return relUser, relFight
}

// 根据当前段位，和目标段位差，判断是否要停止寻找
func (self *CrossServerLevelArenaInfo) IsStopFind(nowScoreLv, disScoreLv int) bool {
	//这个段位之上的人一定是扩大搜索
	if nowScoreLv >= CROSS_SERVER_LEVELARENA_SCORELV_FIND && disScoreLv > 2 {
		return true
	}
	if nowScoreLv < CROSS_SERVER_LEVELARENA_SCORELV_FIND && disScoreLv > 0 {
		return true
	}
	return false
}

func (self *CrossServerLevelArenaInfo) GetFirstEnemy(scoreLvMap map[int]map[int64]int, cantList map[int64]int, scoreLv int, score int) (*CrossServerLevelArenaUser, map[int64]int) {

	for k := range scoreLvMap[scoreLv] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv+1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv+2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	//12以上段位一定要找个真人
	if scoreLv >= CROSS_SERVER_LEVELARENA_SCORELV_FIND {
		for _, v := range self.rankInfo {
			k := v.Uid
			_, ok := cantList[k]
			if ok {
				continue
			}
			cantList[k] = model.LOGIC_TRUE
			return self.db_list[k].info, cantList
		}
	}
	//前面全部没有选中，开始补机器人
	robotInfo := new(CrossServerLevelArenaUser)
	robotInfo.Score = score
	return robotInfo, cantList
}

// 1. 低一个档位
// 2. 低两个档位
// 3. 当前档位
// 4. 补机器人
func (self *CrossServerLevelArenaInfo) GetThirdEnemy(scoreLvMap map[int]map[int64]int, cantList map[int64]int, scoreLv int, score int) (*CrossServerLevelArenaUser, map[int64]int) {
	for k := range scoreLvMap[scoreLv-1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	//前面全部没有选中，开始补机器人
	robotInfo := new(CrossServerLevelArenaUser)
	robotInfo.Score = score
	return robotInfo, cantList
}

func (self *CrossServerLevelArenaInfo) CalAttackEnd(attack *model.JS_FightInfo, defend *model.JS_FightInfo,
	battleInfo *model.BattleInfo, record []byte) {

	self.Locker.Lock()
	defer self.Locker.Unlock()
	attackStartScore := 1000 //初始积分
	attackScore := attackStartScore
	defenceStartScore := 1000 //初始积分
	defenceScore := defenceStartScore
	scoreLv := 1
	attackPoint := 0    //变化的积分
	defencePoint := 0   //变化的积分
	attackTimes := 0    //
	attackWinTimes := 0 //
	attackUid := int64(0)
	if attack.Uid > 0 {
		attackUid = attack.Uid
		attackInfo, ok := self.db_list[attackUid]
		if ok && attackInfo.info != nil {
			attackStartScore = attackInfo.info.Score
			attackInfo.info.IsSign = model.LOGIC_TRUE
			scoreLv = attackInfo.info.ScoreLv
		}
	}
	//防守方需要排除机器人
	defendUid := int64(0)
	if defend.Uid > 0 {
		defendUid = defend.Uid
		defendInfo, ok := self.db_list[defendUid]
		if ok && defendInfo.info != nil {
			defenceStartScore = defendInfo.info.Score
		}
	}
	config := conf.GetDanConfig(scoreLv)
	if config == nil {
		return
	}
	attackTimes += 1
	if battleInfo.Result == model.ATTACK_WIN {
		attackWinTimes += 1
		attackPoint = int(conf.CountAddPoint(true, config, int64(attackStartScore), int64(defenceStartScore)))
		defencePoint = int(conf.CountMinPoint(true, config, int64(attackStartScore), int64(defenceStartScore)))
	} else {
		attackPoint = int(conf.CountMinPoint(false, config, int64(attackStartScore), int64(defenceStartScore)))
		defencePoint = int(conf.CountAddPoint(false, config, int64(attackStartScore), int64(defenceStartScore)))
	}
	if attackUid > 0 {
		attackInfo, ok := self.db_list[attackUid]
		if !ok || attackInfo.info == nil {
			return
		}
		attackInfo.info.Score += attackPoint
		if attackInfo.info.Score < 0 {
			attackInfo.info.Score = 0
		}
		attackScore = attackInfo.info.Score
		attackInfo.info.AllTimes += attackTimes
		attackInfo.info.WinTimes += attackWinTimes
	}
	if defendUid > 0 {
		defendInfo, ok := self.db_list[defendUid]
		if ok && defendInfo.info != nil && defendInfo.info.IsSign == model.LOGIC_TRUE {
			defendInfo.info.Score += defencePoint
			if defendInfo.info.Score < 0 {
				defendInfo.info.Score = 0
			}
			if defendInfo.info.Score+defencePoint < 0 {
				defenceScore = 0
			} else {
				defenceScore = defendInfo.info.Score + defencePoint
			}
		}
	} else {
		defenceScore += defencePoint
	}

	self.SortRank()

	if battleInfo.UserInfo[0] != nil {
		battleInfo.UserInfo[0].Score = attackScore
		battleInfo.UserInfo[0].Param1 = attackPoint
	}
	if battleInfo.UserInfo[1] != nil {
		battleInfo.UserInfo[1].Score = defenceScore
		battleInfo.UserInfo[1].Param1 = defencePoint
	}

	battleRecord := model.BattleRecord{}
	battleRecord.Id = battleInfo.Id
	battleRecord.LevelID = battleInfo.LevelID
	battleRecord.Result = battleInfo.Result
	battleRecord.RandNum = battleInfo.Random
	battleRecord.FightInfo[0] = attack
	battleRecord.FightInfo[1] = defend
	db.HMSetRedisEx(REDIS_TABLE_NAME_BATTLERECORD, battleRecord.Id, &battleRecord, utils.DAY_SECS*1)
	if attackUid > 0 {
		attackInfo, ok := self.db_list[attackUid]
		if ok {
			//生成战报
			attackCrossServerLevelArenaFight := self.NewCrossServerLevelArenaFight(battleInfo.Id, attack, defend, battleInfo, battleInfo.Result, 0, attackPoint, defend)
			attackInfo.info.LevelArenaFight = append(attackInfo.info.LevelArenaFight, attackCrossServerLevelArenaFight)
			if len(attackInfo.info.LevelArenaFight) > 10 {
				attackInfo.info.LevelArenaFight = attackInfo.info.LevelArenaFight[1:]
			}
			//通知给攻击方
			msg := new(CrossServerLevelArenaEnd)
			msg.UserInfo = attackInfo.info
			msg.BattleRecord = &battleRecord
			msg.Record = record
			core.GetCenterApp().AddEvent(attack.Server, core.MATCH_CROSSLEVELARENA_ATTACKEND, attack.Uid,
				0, 0, utils.HF_JtoA(msg))
		}
	}
	if defendUid > 0 {
		defendInfo, ok := self.db_list[defendUid]
		if ok {
			//生成战报
			defendCrossServerLevelArenaFight := self.NewCrossServerLevelArenaFight(battleInfo.Id, attack, defend, battleInfo, battleInfo.Result, 1, defencePoint, attack)
			defendInfo.info.LevelArenaFight = append(defendInfo.info.LevelArenaFight, defendCrossServerLevelArenaFight)
			if len(defendInfo.info.LevelArenaFight) > 10 {
				defendInfo.info.LevelArenaFight = defendInfo.info.LevelArenaFight[1:]
			}
			//通知给攻击方
			msg := new(CrossServerLevelArenaEnd)
			msg.UserInfo = defendInfo.info
			msg.BattleRecord = &battleRecord
			msg.Record = record
			core.GetCenterApp().AddEvent(defend.Server, core.MATCH_CROSSLEVELARENA_BE_ATTACK, defend.Uid,
				0, 0, utils.HF_JtoA(msg))
		}
	}
	return
}

// 战斗服没能返回结果，返回客户端未匹配成功
func (self *CrossServerLevelArenaInfo) FightError(attack *model.JS_FightInfo) {
	if attack == nil {
		return
	}
	attackUid := int64(0)
	if attack.Uid > 0 {
		attackUid = attack.Uid
		attackInfo, ok := self.db_list[attackUid]
		if ok && attackInfo.info != nil {
			core.GetCenterApp().AddEvent(attack.Server, core.MATCH_CROSSLEVELARENA_FIGHT_ERROR, attack.Uid,
				0, int(attackUid), "")
		}
	}
	return
}

func (self *CrossServerLevelArenaInfo) NewCrossServerLevelArenaFight(fightId int64, attack *model.JS_FightInfo, defend *model.JS_FightInfo,
	battleInfo *model.BattleInfo, result int, side int, point int, fightInfo *model.JS_FightInfo) *model.CrossServerLevelArenaFight {

	data := new(model.CrossServerLevelArenaFight)
	data.FightId = fightId
	data.Side = side
	data.Result = result
	data.Point = point
	data.Uid = fightInfo.Uid
	data.IconId = fightInfo.Iconid
	data.Portrait = fightInfo.Portrait
	data.Title = fightInfo.Title
	data.Name = fightInfo.Uname
	data.Time = model.TimeServer().Unix()
	data.Level = fightInfo.Level
	if side == 0 {
		data.Fight += attack.Deffight
	} else {
		data.Fight += defend.Deffight
	}
	data.BattleInfo = battleInfo
	return data
}

func (self *CrossServerLevelArenaMgr) GetRank(uid int64, serverId int, fightInfos *model.JS_FightInfo) (int, string, string, string, string) {
	crossServerLevelArenaInfo := self.GetCrossServerLevelArenaInfo(serverId)
	if crossServerLevelArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", "", "", ""
	}
	selfInfo, rankInfo, selfInfoServer, rankInfoServer := crossServerLevelArenaInfo.GetRank(uid, fightInfos, serverId)
	selfInfoStr := ""
	rankInfoStr := ""
	selfInfoServerStr := ""
	rankInfoServerStr := ""
	if selfInfo != nil {
		selfInfoStr = utils.HF_JtoA(selfInfo)
	}
	if rankInfo != nil {
		rankInfoStr = utils.HF_JtoA(rankInfo)
	}
	if selfInfoServer != nil {
		selfInfoServerStr = utils.HF_JtoA(selfInfoServer)
	}
	if rankInfoServer != nil {
		rankInfoServerStr = utils.HF_JtoA(rankInfoServer)
	}
	return RETCODE_DATA_CROSS_OK, selfInfoStr, rankInfoStr, selfInfoServerStr, rankInfoServerStr
}

func (self *CrossServerLevelArenaInfo) GetRank(uid int64, fightInfos *model.JS_FightInfo, serverId int) (*model.RankInfo, []*model.RankInfo, *model.RankInfo, []*model.RankInfo) {
	selfInfo := self.GetInfoDB(uid, fightInfos)
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rankInfo := make([]*model.RankInfo, 0)
	rankInfoServer := make([]*model.RankInfo, 0)
	for _, v := range self.rankInfo {
		//出现没打过的人 不出现在排行榜上
		//if v.IsSign == core.LOGIC_FALSE {
		//	break
		//}
		//本服的榜满了直接跳出
		if len(rankInfoServer) > CROSS_SERVER_LEVELARENA_RANK_MAX {
			break
		}
		if v.SvrId == serverId {
			rankUserServer := GetRankInfoCrossServerLevelArena(v)
			rankInfoServer = append(rankInfoServer, rankUserServer)
		}
		if len(rankInfo) > CROSS_SERVER_LEVELARENA_RANK_MAX {
			continue
		}
		rankUser := GetRankInfoCrossServerLevelArena(v)
		if v.IsSign == model.LOGIC_FALSE {
			continue
		}
		rankInfo = append(rankInfo, rankUser)
	}
	if selfInfo == nil {
		return nil, rankInfo, nil, rankInfoServer
	}
	// 注意 rankInfo,rankInfoServer是浅拷贝 所以排名不能在这里修改，给游戏服去处理
	return GetRankInfoCrossServerLevelArena(selfInfo.info), rankInfo, GetRankInfoCrossServerLevelArena(selfInfo.info), rankInfoServer
}

func (self *CrossServerLevelArenaMgr) GetRecord(uid int64, serverId int, id int64) (int, string) {
	utils.LogDebug("CrossServerLevelArenaMgr,GetRecord:", id)
	var battleRecord model.BattleRecord
	value, flag, err := db.HGetRedisEx(REDIS_TABLE_NAME_BATTLERECORD, id, fmt.Sprintf("%d", id))
	if err != nil || !flag {
		return 0, ""
	}
	err1 := json.Unmarshal([]byte(value), &battleRecord)
	if err1 != nil {
		return 0, ""
	}
	if battleRecord.Id == 0 {
		return 0, ""
	}
	return 0, utils.HF_JtoA(battleRecord)
}

func (self *CrossServerLevelArenaMgr) GetReplay(uid int64, serverId int, id int64) (int, string) {
	utils.LogDebug("CrossServerLevelArenaMgr,GetRecord:", id)
	fightData := model.FightReplay{}
	value, flag, err := db.HGetRedisEx(battle.BATTLE_REPLAY_RECORD, id, fmt.Sprintf("%d", id))
	if err != nil || !flag {
		return 0, ""
	}
	if flag {
		err1 := json.Unmarshal([]byte(value), &fightData)
		if err1 != nil {
			return 0, ""
		}
	}

	return 0, utils.HF_JtoA(fightData)
}

func (self *CrossServerLevelArenaMgr) Look(uid int64, serverId int, targetUid int64) (int, string, string) {
	crossServerLevelArenaInfo := self.GetCrossServerLevelArenaInfo(serverId)
	if crossServerLevelArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	userInfo, userFightInfo := crossServerLevelArenaInfo.Look(targetUid)
	return RETCODE_DATA_CROSS_OK, utils.HF_JtoA(userInfo), utils.HF_JtoA(userFightInfo)
}

func (self *CrossServerLevelArenaInfo) Look(uid int64) (*CrossServerLevelArenaUser, *model.JS_FightInfo) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	info, ok := self.db_list[uid]
	if !ok {
		return nil, nil
	}
	return info.info, info.fightInfos
}

func (self *CrossServerLevelArenaMgr) GetHallOfGlory(groupId int, serverId int) *HallOfGlorys {
	info := self.GetCrossServerLevelArenaInfoByGroupId(groupId)
	if info == nil {
		return nil
	}
	return info.GetHallOfGlorys(serverId)
}

func (self *CrossServerLevelArenaInfo) GetHallOfGlorys(serverId int) *HallOfGlorys {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rel := new(HallOfGlorys)
	for i := 0; i <= len(self.rankInfo); i++ {
		if i >= len(self.rankInfo) {
			break
		}
		if serverId > 0 && serverId != self.rankInfo[i].SvrId {
			continue
		}
		data := self.MakeHallOfGloryInfo(self.rankInfo[i])
		rel.HallOfGlory = append(rel.HallOfGlory, data)
		if len(rel.HallOfGlory) >= 3 {
			break
		}
	}
	return rel
}
func (self *CrossServerLevelArenaInfo) MakeHallOfGloryInfo(data *CrossServerLevelArenaUser) *HallOfGloryInfo {
	hallOfGloryInfo := new(HallOfGloryInfo)
	if data == nil {
		return hallOfGloryInfo
	}
	hallOfGloryInfo.Uid = data.Uid
	hallOfGloryInfo.SvrId = data.SvrId
	hallOfGloryInfo.SvrName = data.SvrName
	hallOfGloryInfo.UName = data.UName
	hallOfGloryInfo.UnionName = data.UnionName
	hallOfGloryInfo.Score = data.Score
	hallOfGloryInfo.ScoreLv = data.ScoreLv
	hallOfGloryInfo.RankPos = data.RankPos
	hallOfGloryInfo.Level = data.Level
	hallOfGloryInfo.Vip = data.Vip
	hallOfGloryInfo.Icon = data.Icon
	hallOfGloryInfo.Portrait = data.Portrait
	hallOfGloryInfo.Title = data.Title
	hallOfGloryInfo.Fight = data.Fight
	return hallOfGloryInfo
}

// 这个功能暂缓  要保存历史分数，需要拉人上来  考虑做离线模块或放弃这个设定 wait
func (self *CrossServerLevelArenaMgr) ClearData() {
	self.CrossServerLevelArenaInfo.Range(func(key, info interface{}) bool {
		if info == nil {
			return true
		}
		data := info.(*CrossServerLevelArenaInfo)
		data.SaveScoreLvHistory()
		return true
	})

	self.CrossServerLevelArenaInfo = new(sync.Map)
}

func (self *CrossServerLevelArenaInfo) SaveScoreLvHistory() {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	for _, v := range self.db_list {
		if v == nil || v.info == nil {
			continue
		}
		if v.info.ScoreLv <= 0 {
			continue
		}
		GetOfflineInfoMgr().SetLevelArenaScoreLvHistory(v.Uid, v.info.ScoreLv)
	}
}

func (self *CrossServerLevelArenaMgr) UpdateInfo(uid int64, serverId int, fightInfos *model.JS_FightInfo) (int, string) {
	if fightInfos == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	crossServerLevelArenaInfo := self.GetCrossServerLevelArenaInfo(serverId)
	if crossServerLevelArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	info := crossServerLevelArenaInfo.UpdateInfo(uid, fightInfos)
	infoStr := ""
	if info != nil {
		infoStr = utils.HF_JtoA(info)
	}
	return RETCODE_DATA_CROSS_OK, infoStr
}
func (self *CrossServerLevelArenaInfo) UpdateInfo(uid int64, fightInfos *model.JS_FightInfo) *CrossServerLevelArenaUser {
	infoDB := self.GetInfoDB(uid, fightInfos)
	if infoDB == nil {
		return nil
	}
	infoDB.SetFightInfo(fightInfos)
	if infoDB.info != nil && infoDB.fightInfos != nil {
		infoDB.info.Fight = 0
		infoDB.info.UName = infoDB.fightInfos.Uname
		infoDB.info.Icon = infoDB.fightInfos.Iconid
		infoDB.info.Portrait = infoDB.fightInfos.Portrait
		infoDB.info.Title = infoDB.fightInfos.Title
		infoDB.info.Level = infoDB.fightInfos.Level
		infoDB.info.UnionName = infoDB.fightInfos.UnionName
		infoDB.info.SvrId = infoDB.fightInfos.Server
		realValue := infoDB.fightInfos.Deffight / 100
		infoDB.info.Fight += realValue * 100
	}
	return infoDB.info
}

func (self *CrossServerLevelArenaDB) SetFightInfo(fightInfos *model.JS_FightInfo) {
	if fightInfos == nil {
		return
	}
	if len(fightInfos.Heroinfo) == 0 {
		return
	}
	if self.info != nil {
		fightInfos.Param = int64(self.info.ScoreLv)
		self.info.Fight = fightInfos.Deffight
	}
	self.fightInfos = fightInfos
}
func (self *CrossServerLevelArenaMgr) SendRankReward() {
	period := GetServerGroupMgr().GetLevelArenaPeriods()
	self.CrossServerLevelArenaInfo.Range(func(key, info interface{}) bool {
		if info == nil {
			return true
		}
		v := info.(*CrossServerLevelArenaInfo)
		//跨服记录
		rewardList := v.GetRewardList()
		HallOfGlorysInfo := GetCrossServerLevelArenaMgr().GetHallOfGlory(v.groupId, 0)
		if HallOfGlorysInfo != nil {
			HallOfGlorysInfo.StartTime = GetServerGroupMgr().ServerGroupConfig.crossServerPlayConfig.ArenaConfig.StartTime
			HallOfGlorysInfo.EndTime = GetServerGroupMgr().ServerGroupConfig.crossServerPlayConfig.ArenaConfig.EndTime
		}
		for _, rewardInfo := range rewardList {
			rewardInfo.Period = period
			data, ok := GetServerGroupMgr().ServerGroup.Load(rewardInfo.ServerId)
			if ok {
				serverInfo := data.(*ServerGroupInfo)
				if serverInfo.activityInfo == nil {
					serverInfo.activityInfo = new(ActivityInfo)
				}
				serverInfo.activityInfo.LevelArenaHallOfGlorys = append(serverInfo.activityInfo.LevelArenaHallOfGlorys, HallOfGlorysInfo)
				//本服奖励
				hallOfGlorysSelf := GetCrossServerLevelArenaMgr().GetHallOfGlory(serverInfo.GroupId, serverInfo.ServerId)
				if hallOfGlorysSelf != nil {
					hallOfGlorysSelf.StartTime = GetServerGroupMgr().ServerGroupConfig.crossServerPlayConfig.ArenaConfig.StartTime
					hallOfGlorysSelf.EndTime = GetServerGroupMgr().ServerGroupConfig.crossServerPlayConfig.ArenaConfig.EndTime
					serverInfo.activityInfo.LevelArenaHallOfGlorysSelf = append(serverInfo.activityInfo.LevelArenaHallOfGlorysSelf, hallOfGlorysSelf)
				}
			}

			info := utils.HF_JtoA(rewardInfo)
			WriterLog(2, info)
			core.GetCenterApp().AddEvent(rewardInfo.ServerId, core.CROSS_SERVER_LEVELARENA_RANK_REWARD, 0,
				0, 0, info)
			//给玩家打上排行标记
			for _, user := range rewardInfo.RewardInfo {
				GetOfflineInfoMgr().SetLevelAreanRank(user.Uid, period, user.RankPos)
			}
		}
		return true
	})
}

func (self *CrossServerLevelArenaInfo) GetRewardList() map[int]*RewardList {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rewardMap := make(map[int]*RewardList)
	for _, v := range self.rankInfo {
		if v.IsSign == model.LOGIC_FALSE {
			break
		}
		info, ok := rewardMap[v.SvrId]
		if !ok {
			info = new(RewardList)
			info.ServerId = v.SvrId
			rewardMap[info.ServerId] = info
		}
		info.RewardInfo = append(info.RewardInfo, &RewardInfo{Uid: v.Uid, RankPos: v.RankPos})
	}
	return rewardMap
}
func (self *CrossServerLevelArenaMgr) DeletePlayerRecord(req *RPC_RankDoLikeReq) {
	crossServerLevelArenaInfo := self.GetCrossServerLevelArenaInfo(req.ServerId)
	if crossServerLevelArenaInfo == nil {
		return
	}
	crossServerLevelArenaInfo.DeletePlayerRecord(req.Uid)
}
func (self *CrossServerLevelArenaInfo) DeletePlayerRecord(uid int64) {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	user, ok := self.db_list[uid]
	if !ok {
		return
	}
	if user.info == nil {
		return
	}
	user.info.Score = 0
	sort.Sort(lstCrossServerLevelArenaRank(self.rankInfo))
	for index, v := range self.rankInfo {
		v.RankPos = index + 1
	}
}

func (self *CrossServerLevelArenaInfo) GetMatchShow(uid int64) []*model.RankInfo {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rankInfo := make([]*model.RankInfo, 0)
	for _, v := range self.rankInfo {
		//出现没打过的人 不出现在排行榜上
		//if v.IsSign == core.LOGIC_FALSE {
		//	break
		//}
		if len(rankInfo) > 10 {
			continue
		}
		if v.Uid == uid {
			continue
		}
		rankUser := GetRankInfoCrossServerLevelArena(v)
		rankInfo = append(rankInfo, rankUser)
	}
	return rankInfo
}

func (self *CrossServerLevelArenaMgr) CheckBattleFinish() {
	now := model.TimeServer().Unix()

	deleteList := make([]int64, 0)
	self.BattleListen.Range(func(key, battleData interface{}) bool {
		if battleData == nil {
			deleteList = append(deleteList, key.(int64))
			return true
		}
		value := battleData.(*model.ArenaFightList)
		//如果超时直接返回机器人
		if value.Time+10 < now {
			crossServerLevelArenaInfo := self.GetCrossServerLevelArenaInfo(value.Attack.Server)
			if crossServerLevelArenaInfo != nil {
				//crossServerLevelArenaInfo.FightError(value.Attack)

				//生成一个机器人，必胜
				robotFightInfo := self.GetRobotFightInfo(value.Attack.Level)
				value.Defend = robotFightInfo

				battleInfo := new(model.BattleInfo)
				battleInfo.Id = value.FightId

				var attackHeroInfo []*model.BattleHeroInfo
				for i := 0; i < 5; i++ {
					level, star, skin, exclusiveLv, heroId := 0, 0, 0, 0, 0
					if i < len(value.Attack.Heroinfo) {
						level = value.Attack.Heroinfo[i].Levels
						star = value.Attack.Heroinfo[i].Stars
						skin = value.Attack.Heroinfo[i].Skin
						exclusiveLv = value.Attack.Heroinfo[i].HeroExclusiveLv
						heroId = value.Attack.Heroinfo[i].Heroid
					}
					attackHeroInfo = append(attackHeroInfo, &model.BattleHeroInfo{HeroID: heroId, HeroLv: level,
						HeroStar: star, HeroSkin: skin, Hp: 10000, Energy: 10000, Damage: 0,
						TakeDamage: 0, Healing: 0, ArmyInfo: nil, ExclusiveLv: exclusiveLv,
						UseSkill: nil})
				}
				defendHeroInfo := []*model.BattleHeroInfo{}
				for i := 0; i < 5; i++ {
					level, star, skin, exclusiveLv, heroId := 0, 0, 0, 0, 0
					if i < len(value.Defend.Heroinfo) {
						level = value.Defend.Heroinfo[i].Levels
						star = value.Defend.Heroinfo[i].Stars
						skin = value.Defend.Heroinfo[i].Skin
						exclusiveLv = value.Defend.Heroinfo[i].HeroExclusiveLv
						heroId = value.Defend.Heroinfo[i].Heroid
					}
					defendHeroInfo = append(defendHeroInfo, &model.BattleHeroInfo{HeroID: heroId, HeroLv: level,
						HeroStar: star, HeroSkin: skin, Hp: 0, Energy: 0, Damage: 0,
						TakeDamage: 0, Healing: 0, ArmyInfo: nil, ExclusiveLv: exclusiveLv,
						UseSkill: nil})
				}
				battleInfo.UserInfo[model.POS_ATTACK] = NewBattleUserInfo(value.Attack, attackHeroInfo)
				battleInfo.UserInfo[model.POS_DEFENCE] = NewBattleUserInfo(value.Defend, defendHeroInfo)

				battleInfo.Time = model.TimeServer().Unix()
				battleInfo.Random = value.Random
				battleInfo.LevelID = 600001
				battleInfo.Result = model.ATTACK_WIN
				battleInfo.Param = model.LOGIC_TRUE //触发碾压
				//battleInfo.Param = core.LOGIC_FALSE //触发碾压
				crossServerLevelArenaInfo.CalAttackEnd(value.Attack, value.Defend, battleInfo, []byte{})

			}
			deleteList = append(deleteList, key.(int64))
			return true
		}
		// 尝试获取战斗结果
		FightResult := battle.GetFightMgr().GetResult(value.FightId)
		if FightResult == nil {
			return true
		}
		crossServerLevelArenaInfo := self.GetCrossServerLevelArenaInfo(value.Attack.Server)
		if crossServerLevelArenaInfo != nil {
			battleInfo := new(model.BattleInfo)
			battleInfo.Id = value.FightId
			var attackHeroInfo []*model.BattleHeroInfo
			for i, v := range FightResult.Info[model.POS_ATTACK] {
				level, star, skin, exclusiveLv := 0, 0, 0, 0
				if i < len(FightResult.Fight[model.POS_ATTACK].Heroinfo) {
					level = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Levels
					star = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Stars
					skin = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Skin
					exclusiveLv = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].HeroExclusiveLv
				}
				attackHeroInfo = append(attackHeroInfo, &model.BattleHeroInfo{HeroID: v.Heroid, HeroLv: level,
					HeroStar: star, HeroSkin: skin, Hp: v.Hp, Energy: v.Energy, Damage: v.Damage,
					TakeDamage: v.TakeDamage, Healing: v.Healing, ArmyInfo: nil, ExclusiveLv: exclusiveLv,
					UseSkill: nil})
			}
			defendHeroInfo := []*model.BattleHeroInfo{}
			for i, v := range FightResult.Info[model.POS_DEFENCE] {
				level, star, skin, exclusiveLv := 0, 0, 0, 0
				if i < len(FightResult.Fight[model.POS_DEFENCE].Heroinfo) {
					level = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Levels
					star = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Stars
					skin = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Skin
					exclusiveLv = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].HeroExclusiveLv
				}
				defendHeroInfo = append(defendHeroInfo, &model.BattleHeroInfo{v.Heroid, level, star, skin, v.Hp, v.Energy, v.Damage, v.TakeDamage, v.Healing, nil, exclusiveLv, nil})
			}
			battleInfo.UserInfo[model.POS_ATTACK] = NewBattleUserInfo(value.Attack, attackHeroInfo)
			battleInfo.UserInfo[model.POS_DEFENCE] = NewBattleUserInfo(value.Defend, defendHeroInfo)
			battleInfo.Time = value.Time
			battleInfo.Random = value.Random
			battleInfo.Result = FightResult.Result
			battleInfo.LevelID = 600003
			crossServerLevelArenaInfo.CalAttackEnd(value.Attack, value.Defend, battleInfo, FightResult.ResultDetail.Record)
		}
		deleteList = append(deleteList, key.(int64))
		return true
	})

	for _, v := range deleteList {
		self.BattleListen.Delete(v)
		battle.GetFightMgr().DelResult(v)
	}
}

func NewBattleUserInfo(fightInfo *model.JS_FightInfo, battleHeroInfo []*model.BattleHeroInfo) *model.BattleUserInfo {
	rel := new(model.BattleUserInfo)
	rel.Uid = fightInfo.Uid
	rel.Name = fightInfo.Uname
	rel.Icon = fightInfo.Iconid
	rel.Portrait = fightInfo.Portrait
	rel.Fight = fightInfo.Deffight
	rel.ServerId = fightInfo.Server
	rel.Title = fightInfo.Title
	rel.UnionId = fightInfo.UnionId
	rel.UnionName = fightInfo.UnionName
	rel.Level = fightInfo.Level
	rel.HeroInfo = battleHeroInfo

	return rel
}

type RobotInfo struct {
	Hero    int `json:"optionhero"`
	NpcLv   int `json:"herolv"`
	NpcStar int `json:"robotstar"`
}

func (self *CrossServerLevelArenaMgr) RandRobotConfig(scoreLv int) *JJCRobotConfig {

	for _, v := range self.JJCRobotConfigMap[scoreLv] {
		return v
	}
	for _, v := range self.JJCRobotConfigMap[1] {
		return v
	}
	return nil
}

func (self *CrossServerLevelArenaMgr) GetRobotFightInfo(scoreLv int) *model.JS_FightInfo {

	cfg := GetCrossServerLevelArenaMgr().RandRobotConfig(scoreLv)
	//这里不会返回空，检查表
	if cfg == nil {
		return nil
	}

	data := new(model.JS_FightInfo)

	data.Portrait = 1000 //机器人边框  20190412 by zy
	data.Level = cfg.Level
	data.Uname = "Guard"
	data.Defhero = make([]int, 0)
	data.Heroinfo = make([]model.JS_HeroInfo, 0)
	data.HeroParam = make([]model.JS_HeroParam, 0)

	var heroes []*RobotInfo
	for i := 0; i < len(cfg.Hero); i++ {
		if cfg.Hero[i] == 0 {
			continue
		}

		heroes = append(heroes, &RobotInfo{cfg.Hero[i], cfg.NpcLv[i], cfg.NpcStar[i]})
	}
	heroes = HF_GetRandomArr(heroes, 5)

	maxValue := int(cfg.Fight[0] - cfg.Fight[1])
	if maxValue <= 0 {
		maxValue = 50000
	}
	nowIconId := 0
	data.Deffight = cfg.Fight[1] + int64(utils.HF_RandInt(1, maxValue))
	for i := 0; i < len(heroes); i++ {
		err := data.FightTeamPos.AddFightPos(i + 1)
		if err != nil {
			continue
		}
		data.Defhero = append(data.Defhero, i)
		var hero model.JS_HeroInfo
		if heroes[i] != nil {
			hero.Heroid = heroes[i].Hero
			if nowIconId == 0 {
				nowIconId = hero.Heroid
			}
			hero.Color = cfg.NpcQuality
			hero.HeroKeyId = i + 1
			hero.Stars = heroes[i].NpcStar
			hero.HeroQuality = heroes[i].NpcStar
			hero.Levels = heroes[i].NpcLv
			hero.Skin = 0
			hero.Soldiercolor = 6
			hero.Skilllevel1 = 0
			hero.Skilllevel2 = 0
			hero.Skilllevel3 = 0
			hero.Skilllevel4 = 0
			hero.Skilllevel5 = 0
			hero.Skilllevel6 = 0
			hero.Fervor1 = 0
			hero.Fervor2 = 0
			hero.Fervor3 = 0
			hero.Fervor4 = 0
			hero.Fight = data.Deffight / 5

			hero.ArmsSkill = make([]model.JS_ArmsSkill, 0)
			hero.MainTalent = 0

		}
		att, att_ext, energy := hero.CountFight(cfg.BaseTypes, cfg.BaseValues)

		data.Heroinfo = append(data.Heroinfo, hero)
		var param model.JS_HeroParam
		param.Heroid = hero.Heroid
		param.Param = att
		param.ExtAttr = att_ext
		param.Hp = param.Param[1]
		param.Energy = energy
		param.Energy = 0

		data.HeroParam = append(data.HeroParam, param)
	}
	if nowIconId != 0 {
		data.Iconid = nowIconId + 10000000
	} else {
		data.Iconid = 10001001
	}

	return data
}

// 得到一个随机数组
func HF_GetRandomArr(arr []*RobotInfo, num int) []*RobotInfo {
	if len(arr) <= num {
		return arr
	}
	lst := make([]*RobotInfo, 0)
	for len(arr) > 0 && len(lst) < num {
		index := utils.HF_GetRandom(len(arr))
		lst = append(lst, arr[index])
		copy(arr[index:], arr[index+1:])
		arr = arr[:len(arr)-1]
	}
	return lst
}

func (self *CrossServerLevelArenaMgr) SortRank() {
	self.CrossServerLevelArenaInfo.Range(func(key, info interface{}) bool {
		if info == nil {
			return true
		}
		serverGroup := info.(*CrossServerLevelArenaInfo)
		serverGroup.SortRank()
		return true
	})
}

func (self *CrossServerLevelArenaInfo) SortRank() {
	sort.Sort(lstCrossServerLevelArenaRank(self.rankInfo))
	for index, v := range self.rankInfo {
		v.RankPos = index + 1
		v.ScoreLv = conf.CalScoreLv(v.Score, v.RankPos)
		if v.ScoreLvMax < v.ScoreLv {
			v.ScoreLvMax = v.ScoreLv
		}
	}
}

func (self *CrossServerLevelArenaMgr) CheckRoll() {
	deleteList := make([]int64, 0)
	self.BattleListenRoll.Range(func(key, battleData interface{}) bool {
		if battleData == nil {
			deleteList = append(deleteList, key.(int64))
			return true
		}
		value := battleData.(*model.ArenaFightList)

		crossServerLevelArenaInfo := self.GetCrossServerLevelArenaInfo(value.Attack.Server)
		if crossServerLevelArenaInfo != nil {
			battleInfo := new(model.BattleInfo)
			battleInfo.Id = value.FightId
			var attackHeroInfo []*model.BattleHeroInfo
			for i, v := range value.Attack.Heroinfo {
				level, star, skin, exclusiveLv := 0, 0, 0, 0
				if i < len(value.Attack.Heroinfo) {
					level = value.Attack.Heroinfo[i].Levels
					star = value.Attack.Heroinfo[i].Stars
					skin = value.Attack.Heroinfo[i].Skin
					exclusiveLv = value.Attack.Heroinfo[i].HeroExclusiveLv
				}
				attackHeroInfo = append(attackHeroInfo, &model.BattleHeroInfo{HeroID: v.Heroid, HeroLv: level,
					HeroStar: star, HeroSkin: skin, Hp: 10000, Energy: 10000, Damage: 0,
					TakeDamage: 0, Healing: 0, ArmyInfo: nil, ExclusiveLv: exclusiveLv,
					UseSkill: nil})
			}
			defendHeroInfo := []*model.BattleHeroInfo{}
			for i, v := range value.Defend.Heroinfo {
				level, star, skin, exclusiveLv := 0, 0, 0, 0
				if i < len(value.Defend.Heroinfo) {
					level = value.Defend.Heroinfo[i].Levels
					star = value.Defend.Heroinfo[i].Stars
					skin = value.Defend.Heroinfo[i].Skin
					exclusiveLv = value.Defend.Heroinfo[i].HeroExclusiveLv
				}
				defendHeroInfo = append(defendHeroInfo, &model.BattleHeroInfo{v.Heroid, level,
					star, skin, 0, 0, 0, 0,
					0, nil, exclusiveLv, nil})
			}
			battleInfo.UserInfo[model.POS_ATTACK] = NewBattleUserInfo(value.Attack, attackHeroInfo)
			battleInfo.UserInfo[model.POS_DEFENCE] = NewBattleUserInfo(value.Defend, defendHeroInfo)
			battleInfo.Time = value.Time
			battleInfo.Random = value.Random
			battleInfo.Result = battle.WIN_ATTCK

			battleInfo.Id = value.FightId
			battleInfo.Time = model.TimeServer().Unix()
			battleInfo.Random = value.Random
			battleInfo.LevelID = 600001
			battleInfo.Result = model.ATTACK_WIN
			battleInfo.Param = model.LOGIC_TRUE //触发碾压
			crossServerLevelArenaInfo.CalAttackEnd(value.Attack, value.Defend, battleInfo, []byte{})
		}
		deleteList = append(deleteList, key.(int64))
		return true
	})

	for _, v := range deleteList {
		self.BattleListenRoll.Delete(v)
	}
}

func LevelArenaTest(w http.ResponseWriter, r *http.Request) {
	serverId := utils.HF_Atoi(r.FormValue("serverId"))
	uid := utils.HF_AtoI64(r.FormValue("uid"))

	crossServerLevelArenaInfo := GetCrossServerLevelArenaMgr().GetCrossServerLevelArenaInfo(serverId)
	if crossServerLevelArenaInfo == nil {
		w.Write([]byte(fmt.Sprintf("找不到该服务器数据 \n")))
		return
	}
	crossServerLevelArenaInfo.LevelArenaTest(uid, w)
}

func (self *CrossServerLevelArenaInfo) LevelArenaTest(uid int64, w http.ResponseWriter) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	info, ok := self.db_list[uid]
	if !ok {
		w.Write([]byte(fmt.Sprintf("找不到玩家数据 \n")))
		return
	}
	if info.info == nil {
		w.Write([]byte(fmt.Sprintf("找不到战斗数据 \n")))
		return
	}
	index := info.info.RankPos - 1
	if index < 0 || index >= len(self.rankInfo) {
		return
	}
	scoreLv := self.rankInfo[index].ScoreLv
	w.Write([]byte(fmt.Sprintf("玩家[%d]段位[%d] \n", uid, scoreLv)))
	scoreLvMap := make(map[int]map[int64]int)
	scoreLvMapDebug := make(map[int64]*CrossServerLevelArenaUser)
	for i := index - 1; i >= 0; i-- {
		//if self.rankInfo[i].IsSign == core.LOGIC_FALSE {
		//	continue
		//}
		nowScoreLv := self.rankInfo[i].ScoreLv
		if self.IsStopFind(scoreLv, nowScoreLv-scoreLv) {
			break
		}
		_, ok := scoreLvMap[nowScoreLv]
		if !ok {
			scoreLvMap[nowScoreLv] = make(map[int64]int)
		}
		if len(scoreLvMap[nowScoreLv]) >= CROSS_SERVER_LEVELARENA_ENEMY_CHOOSE_LIST {
			continue
		}
		scoreLvMapDebug[self.rankInfo[i].Uid] = self.rankInfo[i]
	}
	for i := index + 1; i < len(self.rankInfo); i++ {
		//if self.rankInfo[i].IsSign == core.LOGIC_FALSE {
		//	continue
		//}
		nowScoreLv := self.rankInfo[i].ScoreLv
		if self.IsStopFind(scoreLv, scoreLv-nowScoreLv) {
			break
		}
		_, ok := scoreLvMap[nowScoreLv]
		if !ok {
			scoreLvMap[nowScoreLv] = make(map[int64]int)
		}
		if len(scoreLvMap[nowScoreLv]) >= CROSS_SERVER_LEVELARENA_ENEMY_CHOOSE_LIST {
			continue
		}
		scoreLvMapDebug[self.rankInfo[i].Uid] = self.rankInfo[i]
	}
	w.Write([]byte(fmt.Sprintf("候选列表: \n")))
	for _, v := range scoreLvMapDebug {
		w.Write([]byte(fmt.Sprintf("玩家[%d]名字[%s]段位[%d]分数[%d]: \n", v.Uid, v.UName, v.ScoreLv, v.Score)))
	}

	cantList := make(map[int64]int)
	for k := range self.rankInfo[index].CantList {
		cantList[k] = model.LOGIC_TRUE
	}
	cantList[uid] = model.LOGIC_TRUE
	w.Write([]byte(fmt.Sprintf("BAN列表: \n")))
	for k, _ := range cantList {
		w.Write([]byte(fmt.Sprintf("玩家[%d]: \n", k)))
	}

	return
}

func (self *CrossServerLevelArenaMgr) GameServerGetRankReward(serverId int, userList string) {

	targetList := make(map[int64]int)
	json.Unmarshal([]byte(userList), &targetList)
	if len(targetList) == 0 {
		return
	}
	targetPeriod := GetServerGroupMgr().GetLevelArenaPeriods() - 1

	rewardInfo := new(RewardList)
	rewardInfo.ServerId = serverId
	rewardInfo.Period = targetPeriod
	rewardInfo.RewardInfo = make([]*RewardInfo, 0)
	for uid, period := range targetList {
		if period >= targetPeriod {
			continue
		}
		rankPos := GetOfflineInfoMgr().GetLevelAreanRank(uid, targetPeriod)
		if rankPos == 0 {
			continue
		}
		rewardInfo.RewardInfo = append(rewardInfo.RewardInfo, &RewardInfo{Uid: uid, RankPos: rankPos})
	}
	core.GetCenterApp().AddEvent(rewardInfo.ServerId, core.CROSS_SERVER_LEVELARENA_RANK_REWARD, 0,
		0, 0, utils.HF_JtoA(rewardInfo))
}

func (self *CrossServerLevelArenaMgr) GetChampionSign(count int, groupId int) []*model.JS_FightInfo {
	data, ok := self.CrossServerLevelArenaInfo.Load(groupId)
	if !ok {
		return nil
	}

	rank := []*model.JS_FightInfo{}
	info := data.(*CrossServerLevelArenaInfo)
	size := len(info.rankInfo)
	if info == nil || size == 0 {
		return nil
	}
	if size < count {
		count = size
	}
	rankList := info.rankInfo[:size]
	for _, player := range rankList {
		playerFightInfo := info.GetFightinfo(player.Uid)
		if playerFightInfo != nil {
			continue
		}
		rank = append(rank, playerFightInfo)
	}
	return rank
}
