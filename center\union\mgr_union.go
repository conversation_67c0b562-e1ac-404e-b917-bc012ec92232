package union

import (
	"fmt"
	"log"
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
	"runtime/debug"
	"sort"
	"sync"
	"time"
)

const UNION_LIST_MAX = 100

// UnionMgr ! 公会管理类
type UnionMgr struct {
	Sql_Union       map[int]*UnionInfo
	LastCallTime    map[int]int64 //! 招募时间保存
	LastCheckMaster map[int]int64 //! 检查军团长时间
	Locker          *sync.RWMutex //! 操作保护
	TimeLocker      *sync.RWMutex //! 时间保护
	UnionCount      int
	RefreshTime     int64

	CommunityConfigs map[int]*CommunityConfig
}

func (self *UnionMgr) LoadCsv() {
	utils.GetCsvUtilMgr().LoadCsv("Guild_Lv", &self.CommunityConfigs)
}

var s_unionmgr *UnionMgr

func GetUnionMgr() *UnionMgr {
	if s_unionmgr == nil {
		s_unionmgr = new(UnionMgr)
		s_unionmgr.Sql_Union = make(map[int]*UnionInfo)
		s_unionmgr.LastCallTime = make(map[int]int64)
		s_unionmgr.LastCheckMaster = make(map[int]int64)
		s_unionmgr.Locker = new(sync.RWMutex)
		s_unionmgr.TimeLocker = new(sync.RWMutex)

		s_unionmgr.CommunityConfigs = make(map[int]*CommunityConfig)
		s_unionmgr.LoadCsv()
	}

	return s_unionmgr
}

// OnSave ! 保存数据
func (self *UnionMgr) OnSave() {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	for _, v := range self.Sql_Union {
		v.onSave()
	}
}

//	func (self *UnionMgr) GetData() {
//		var union UnionInfo
//		sql := fmt.Sprintf("select * from `tbl_union`")
//		res := db.GetDBMgr().DBUser.GetAllData(sql, &union)
//
//		for i := 0; i < len(res); i++ {
//			data := res[i].(*UnionInfo)
//			//data.record = make([]JS_UnionRecord, 0)
//			data.Init("tbl_union", data, false)
//			data.Locker = new(sync.RWMutex)
//			data.ChangeMaster.CheckPlayer = make([]int64, 0)
//			data.Decode()
//
//			self.Sql_Union[data.Id] = data
//
//			data.CheckChangeMasterState() //! 验证同步状态
//
//			//! 重新开服的时候，如果在线状态，则修改为离线
//			for j := 0; j < len(data.member); j++ {
//				if data.member[j].Lastlogintime == 0 {
//					data.member[j].Lastlogintime = time.Now().Unix()
//				}
//				member := player.GetPlayerMgr().GetPlayer(data.member[j].Uid, false)
//				passid := member.Data.PassId
//				if passid == 0 {
//					data.member[j].Stage = 110101
//				} else {
//					data.member[j].Stage = passid
//				}
//			}
//		}
//		//self.OnHunterRefresh()
//	}

func (self *UnionMgr) GetAllData() {
	var union UnionInfo
	sql := fmt.Sprintf("select * from `tbl_union`")
	res := db.GetDBMgr().DBUser.GetAllData(sql, &union)

	unionMax := 0
	for i := 0; i < len(res); i++ {
		data := res[i].(*UnionInfo)
		if data.Id > 0 {
			data.record = make([]*JS_UnionRecord, 0)
			data.Locker = new(sync.RWMutex)
			data.ChangeMaster.CheckPlayer = make([]int64, 0)
			data.Decode()
			data.CheckChangeMasterState() //! 验证同步状态

			//! 重新开服的时候，如果在线状态，则修改为离线
			for j := 0; j < len(data.member); j++ {
				if data.member[j].Lastlogintime == 0 {
					data.member[j].Lastlogintime = model.TimeServer().Unix()
				}
			}
			data.Init("tbl_union", data, false)

			self.Sql_Union[data.Id] = data

			tempMax := data.Id % 1000000
			if tempMax > unionMax {
				unionMax = tempMax
			}
		}
	}

	s_unionmgr.UnionCount = unionMax + 1
}

func (self *UnionMgr) GetData(unionid int) *UnionInfo {
	var union UnionInfo
	sql := fmt.Sprintf("select * from `tbl_union` where id = %d", unionid)
	db.GetDBMgr().DBUser.GetOneData(sql, &union, "", 0)

	if union.Id > 0 {
		data := &union
		data.record = make([]*JS_UnionRecord, 0)
		data.Locker = new(sync.RWMutex)
		data.ChangeMaster.CheckPlayer = make([]int64, 0)
		data.Decode()
		data.CheckChangeMasterState() //! 验证同步状态

		//! 重新开服的时候，如果在线状态，则修改为离线
		for j := 0; j < len(data.member); j++ {
			if data.member[j].Lastlogintime == 0 {
				data.member[j].Lastlogintime = model.TimeServer().Unix()
			}
			//member := player.GetPlayerMgr().GetPlayer(data.member[j].Uid, false)
			//passid := member.Data.PassId
			//if passid == 0 {
			//	data.member[j].Stage = 110101
			//} else {
			//	data.member[j].Stage = passid
			//}
		}

		data.Init("tbl_union", data, false)
		return data
	} else {
		return nil
	}
}

func (self *UnionMgr) GetUnion(union_uid int) *UnionInfo {
	if union_uid == 0 {
		return nil
	}
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	union, ok := self.Sql_Union[union_uid]
	if !ok {
		union = self.GetData(union_uid)
		if union != nil {
			self.Sql_Union[union_uid] = union
		} else {
			return nil
		}
	}
	return union
}

func (self *UnionMgr) CheckName(unionname string, unionid int) bool {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	for _, value := range self.Sql_Union {
		if value.Unionname == unionname {
			if unionid < 0 {
				return true
			} else {
				if value.Id != unionid {
					return true
				}
			}
		}
	}
	return false
}

// CreateUnion 创建军团
func (self *UnionMgr) CreateUnion(icon int, unionname string, uid int64, mastername string, huntInfo []*JS_UnionHunt, serverid int) int {
	self.Locker.Lock()
	defer self.Locker.Unlock()

	union := new(UnionInfo)
	union.Icon = icon
	union.Unionname = unionname
	union.Masteruid = uid
	union.Mastername = mastername
	union.member = make([]*JS_UnionMember, 0)
	union.apply = make([]*JS_UnionApply, 0)
	union.Level = 1
	union.Locker = new(sync.RWMutex)
	union.Joinlevel = UNION_JOIN_LEVEL_BASE
	union.ServerID = serverid
	union.Exp = 0
	union.ChangeMaster.CheckPlayer = make([]int64, 0)
	union.huntInfo = huntInfo
	union.Id = serverid*1000000 + self.UnionCount
	union.Encode()

	_, ok := self.Sql_Union[union.Id]
	if ok {
		return 0
	}
	db.InsertTable("tbl_union", union, 0, false)
	union.Init("tbl_union", union, false)
	self.Sql_Union[union.Id] = union

	self.UnionCount++

	return union.Id
}

// DissolveUnion 创建军团
func (self *UnionMgr) DissolveUnion(unionid int) {
	self.Locker.Lock()
	defer self.Locker.Unlock()

	union, ok := self.Sql_Union[unionid]
	if ok {
		db.DeleteTable("tbl_union", union, []int{0})
		delete(self.Sql_Union, unionid)
	}
}

func (self *UnionMgr) GetUnionCallTime(unionid int) int64 {
	self.TimeLocker.RLock()
	defer self.TimeLocker.RUnlock()

	calltime, ok := self.LastCallTime[unionid]
	if !ok {
		return 0
	}

	return calltime
}

func (self *UnionMgr) GetUnionCheckMaster(unionid int) int64 {
	self.TimeLocker.RLock()
	defer self.TimeLocker.RUnlock()

	checktime, ok := self.LastCheckMaster[unionid]
	if !ok {
		return 0
	}

	return checktime
}

func (self *UnionMgr) SetUnionCallTime(unionid int, calltime int64) {
	self.TimeLocker.Lock()
	defer self.TimeLocker.Unlock()

	self.LastCallTime[unionid] = calltime
}

func (self *UnionMgr) SetUnionCheckMaster(unionid int, checktime int64) {
	self.TimeLocker.Lock()
	defer self.TimeLocker.Unlock()

	self.LastCheckMaster[unionid] = checktime
}

// CheckFull 军团检查是否满员
func (self *UnionMgr) CheckFull(unioninfo *UnionInfo) bool {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	csv_community := self.CommunityConfigs[unioninfo.Level]

	if len(unioninfo.member) >= csv_community.Membernum {
		return true
	}
	return false
}

func (self *UnionMgr) CheckBraveHand(data *UnionInfo) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	now := model.TimeServer().Unix()
	nLen := len(data.braveHand)
	for i := nLen - 1; i >= 0; i-- {
		if data.braveHand[i].EndTime > 0 && now >= data.braveHand[i].EndTime {
			data.braveHand = append(data.braveHand[0:i], data.braveHand[i+1:]...)
		}
	}
}

// Run 开启战斗协程
func (self *UnionMgr) Run() {
	defer func() {
		x := recover()
		if x != nil {
			log.Println(x, string(debug.Stack()))
			utils.LogError(x, string(debug.Stack()))
		}
	}()

	ticker := time.NewTicker(time.Minute)
	for {
		select {
		case <-ticker.C:
			//self.CheckHunterFightEnd()
			//self.OnHunterRefresh()
			self.OnHunterRefresh()
			self.OnTime()
		}
	}
	ticker.Stop()
}

func (self *UnionMgr) OnTime() {
	tNow := model.TimeServer()
	if tNow.Hour() == 0 && tNow.Minute() < 5 && tNow.Unix()-self.RefreshTime > 1800 {
		//!每天5：00检测，
		self.RefreshTime = tNow.Unix()
		self.OnRefresh()
	}
}

func (self *UnionMgr) OnRefresh() {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	for _, value := range self.Sql_Union {
		value.OnRefresh()
	}
}

func (self *UnionMgr) OnHunterRefresh() {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	endtime := self.GetRefreshTime()
	for _, union := range self.Sql_Union {
		union.OnHunterRefresh(endtime)
		union.CheckHunterFightEnd(endtime)
		union.RefreshActivityLimit()
	}
}

// GetRefreshTime 开启战斗协程
func (self *UnionMgr) GetRefreshTime() int64 {
	//if core.TimeServer().Hour() < 5 {
	//	return time.Date(core.TimeServer().Year(), core.TimeServer().Month(), core.TimeServer().Day(), 5, 0, 0, 0, core.TimeServer().Location()).Unix()
	//} else {
	//	return time.Date(core.TimeServer().Year(), core.TimeServer().Month(),core.TimeServer().Day(), 5, 0, 0, 0, core.TimeServer().Location()).Unix() + utils.DAY_SECS
	//}
	return time.Date(model.TimeServer().Year(), model.TimeServer().Month(), model.TimeServer().Day(), 0, 0, 0, 0, model.TimeServer().Location()).Unix() + utils.DAY_SECS
}

func (self *UnionMgr) GetUnionMember(unionId int, uid int64) *JS_UnionMember {
	union := self.GetUnion(unionId)
	if union != nil {
		return union.GetMember(uid)
	}

	return nil
}

func (self *UnionMgr) MakeUnionActivityData(period int) (*sync.Map, *sync.Map) {

	unionActivityFight := new(sync.Map)
	unionActivityMap := new(sync.Map)
	self.Locker.Lock()
	defer self.Locker.Unlock()

	unionList := make([]*UnionInfo, 0)
	//需要记录1个没资格的对手 尽量保证参与感
	var tempUnion *UnionInfo
	tempMax := int64(0)
	for _, v := range self.Sql_Union {
		v.UnionActivityId = 0
		if !v.IsCanSignUnionActivity() {
			if tempMax < v.Fight {
				tempMax = v.Fight
				tempUnion = v
			}
			continue
		}
		unionList = append(unionList, v)
	}
	//尽量凑成偶数,凑不成再走轮空
	size := len(unionList)
	if size%2 == 1 && tempUnion != nil {
		unionList = append(unionList, tempUnion)
		size = len(unionList)
	}
	//去除轮空
	if size%2 == 1 {
		//发送胜负奖励
		//configReward := csvs.GetResultAwardConfigMap(2, 2)
		unionList[size-1].UnionActivityId = -1
		utils.LogDebug(fmt.Sprintf("会战轮空公会:", unionList[size-1].UnionActivityId))
		for _, member := range unionList[size-1].member {
			GetUnionActivityMgr().SendResultAward(member.ServerID, member.Uid, 1)

			//TODO 发送奖励到游戏服
		}
		size -= 1
		if size >= 0 {
			unionList = unionList[:size]
		}
	}
	//分组
	startId := 0
	//机器人ID重置
	//self.StartRobotUid = model.ROBOT_UNIONACTIVITY_UID_END
	alreadyMap := make(map[int]int)

	for _, attackUnion := range unionList {
		_, ok := alreadyMap[attackUnion.Id]
		if ok {
			continue
		}
		attackId := attackUnion.Id
		alreadyMap[attackId] = core.LOGIC_TRUE
		defenceId := 0
		//1.	公会战力总值差距30%以内且胜场数相同的公会进行配队
		for _, defenceUnion := range unionList {
			_, ok := alreadyMap[defenceUnion.Id]
			if ok {
				continue
			}
			minFight := (attackUnion.Fight * 7000) / 10000
			maxFight := (attackUnion.Fight * 13000) / 10000
			if defenceUnion.Fight < minFight || defenceUnion.Fight > maxFight {
				continue
			}
			if defenceUnion.UnionActivityWin != attackUnion.UnionActivityWin {
				continue
			}
			defenceId = defenceUnion.Id
			alreadyMap[defenceId] = core.LOGIC_TRUE
			break
		}
		if defenceId == 0 {
			//2.	公会战力总值差距50%以内且胜场数相同的公会进行配队
			for _, defenceUnion := range unionList {
				_, ok := alreadyMap[defenceUnion.Id]
				if ok {
					continue
				}
				minFight := (attackUnion.Fight * 5000) / 10000
				maxFight := (attackUnion.Fight * 15000) / 10000
				if defenceUnion.Fight < minFight || defenceUnion.Fight > maxFight {
					continue
				}
				if defenceUnion.UnionActivityWin != attackUnion.UnionActivityWin {
					continue
				}
				defenceId = defenceUnion.Id
				alreadyMap[defenceId] = core.LOGIC_TRUE
				break
			}
		}
		if defenceId == 0 {
			//3.	公会战力总值差距50%以内且胜场数相差3以内的公会进行配队
			for _, defenceUnion := range unionList {
				_, ok := alreadyMap[defenceUnion.Id]
				if ok {
					continue
				}
				minFight := (attackUnion.Fight * 5000) / 10000
				maxFight := (attackUnion.Fight * 15000) / 10000
				if defenceUnion.Fight < minFight || defenceUnion.Fight > maxFight {
					continue
				}
				minWin := attackUnion.UnionActivityWin - 3
				maxWin := attackUnion.UnionActivityWin + 3
				if defenceUnion.UnionActivityWin < minWin || defenceUnion.UnionActivityWin > maxWin {
					continue
				}
				defenceId = defenceUnion.Id
				alreadyMap[defenceId] = core.LOGIC_TRUE
				break
			}
		}
		if defenceId == 0 {
			//4.	公会战力总值差距50%以内且胜场数相差8以内的公会进行配队
			for _, defenceUnion := range unionList {
				_, ok := alreadyMap[defenceUnion.Id]
				if ok {
					continue
				}
				minFight := (attackUnion.Fight * 5000) / 10000
				maxFight := (attackUnion.Fight * 15000) / 10000
				if defenceUnion.Fight < minFight || defenceUnion.Fight > maxFight {
					continue
				}
				minWin := attackUnion.UnionActivityWin - 8
				maxWin := attackUnion.UnionActivityWin + 8
				if defenceUnion.UnionActivityWin < minWin || defenceUnion.UnionActivityWin > maxWin {
					continue
				}
				defenceId = defenceUnion.Id
				alreadyMap[defenceId] = core.LOGIC_TRUE
				break
			}
		}
		if defenceId == 0 {
			//5.	公会胜场相差8以内的公会进行配队
			for _, defenceUnion := range unionList {
				_, ok := alreadyMap[defenceUnion.Id]
				if ok {
					continue
				}
				minWin := attackUnion.UnionActivityWin - 8
				maxWin := attackUnion.UnionActivityWin + 8
				if defenceUnion.UnionActivityWin < minWin || defenceUnion.UnionActivityWin > maxWin {
					continue
				}
				defenceId = defenceUnion.Id
				alreadyMap[defenceId] = core.LOGIC_TRUE
				break
			}
		}
		//没找到的公会，最后随机匹配
		if defenceId == 0 {
			//把主匹配移除
			delete(alreadyMap, attackId)
			continue
		}
		//找到的公会处理
		defenceUnion := self.Sql_Union[defenceId]
		if defenceUnion == nil {
			continue
		}
		startId++
		unionFight := new(UnionFight)
		unionFight.Period = period
		unionFight.TeamId = startId
		//unionFight.AttackSide = self.GetUnionActivitySide(attackUnion, false)
		//unionFight.DefenceSide = self.GetUnionActivitySide(self.Sql_Union[defenceId], false)
		unionActivityFight.Store(unionFight.TeamId, unionFight)
		unionActivityMap.Store(attackId, unionFight.TeamId)
		unionActivityMap.Store(defenceId, unionFight.TeamId)
		attackUnion.UnionActivityId = unionFight.TeamId
		defenceUnion.UnionActivityId = unionFight.TeamId

		unionFight.Encode()
		id := db.InsertTable(TABLE_NAME_UNIONACTIVITY_FIGHT, unionFight, 0, false)
		unionFight.Id = int(id)
		unionFight.Init(TABLE_NAME_UNIONACTIVITY_FIGHT, unionFight, false)
	}
	//处理随机配队伍
	lastUnion := make([]*UnionInfo, 0)
	for _, v := range unionList {
		_, ok := alreadyMap[v.Id]
		if ok {
			continue
		}
		lastUnion = append(lastUnion, v)
	}
	//if len(lastUnion)%2 > 0 {
	//	utils.LogDebug("公会战分组数据异常")
	//	return unionActivityFight, unionActivityMap
	//}
	sort.Slice(lastUnion, func(i, j int) bool {
		return lastUnion[i].Fight >= lastUnion[j].Fight
	})
	for attackIndex := 0; attackIndex < len(lastUnion); attackIndex += 2 {
		defenceIndex := attackIndex + 1
		startId++
		if defenceIndex != len(lastUnion) { //zy 2022.9.24
			unionFight := new(UnionFight)
			unionFight.Period = period
			unionFight.TeamId = startId
			unionFight.AttackSide = self.GetUnionActivitySide(lastUnion[attackIndex], false)
			unionFight.DefenceSide = self.GetUnionActivitySide(lastUnion[defenceIndex], false)
			unionActivityFight.Store(unionFight.TeamId, unionFight)
			unionActivityMap.Store(lastUnion[attackIndex].Id, unionFight.TeamId)
			unionActivityMap.Store(lastUnion[defenceIndex].Id, unionFight.TeamId)
			lastUnion[attackIndex].UnionActivityId = unionFight.TeamId
			lastUnion[defenceIndex].UnionActivityId = unionFight.TeamId

			unionFight.Encode()
			id := db.InsertTable(TABLE_NAME_UNIONACTIVITY_FIGHT, unionFight, 0, false)
			unionFight.Id = int(id)
			unionFight.Init(TABLE_NAME_UNIONACTIVITY_FIGHT, unionFight, false)
		}
	}

	return unionActivityFight, unionActivityMap
}

// GetUnionActivitySide ! 生成公会战，人数不足，由游戏服补齐，不在中心服补充机器人
func (self *UnionMgr) GetUnionActivitySide(data *UnionInfo, ignoreCheck bool) *UnionFightSide {
	unionFightSide := new(UnionFightSide)
	unionFightSide.UnionId = data.Id
	unionFightSide.UnionName = data.Unionname
	unionFightSide.UnionIcon = data.Icon
	unionFightSide.MasterName = data.Mastername
	unionFightSide.StarUpdate = model.TimeServer().Unix()
	nowIndex := 0
	unionFightSide.CanPlayList = make([]int64, 0)

	sort.Slice(data.member, func(i, j int) bool {
		return data.member[i].Fight > data.member[j].Fight
	})

	for _, v := range data.member {
		//判断是否有权限参与
		if !ignoreCheck {
			continue
		}
		unionFightSide.CanPlayList = append(unionFightSide.CanPlayList, v.Uid)

		if nowIndex >= core.UNIONACTIVITY_NODE_MAX_NUM {
			continue
		}
		node := new(UnionFightMemberNode)
		node.Uid = v.Uid
		node.BaseInfoSimple.Uid = node.Uid
		node.BaseInfoSimple.Name = v.Uname
		node.BaseInfoSimple.IconId = v.Iconid
		node.BaseInfoSimple.Portrait = v.Portrait
		node.Fight = v.Fight
		node.BaseInfoSimple.WarShip = v.WarShip
		nowIndex++
		node.Index = nowIndex
		unionFightSide.UnionFightMemberNode = append(unionFightSide.UnionFightMemberNode, node)
	}

	return unionFightSide
}
