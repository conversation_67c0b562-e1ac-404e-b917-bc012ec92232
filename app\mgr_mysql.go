package app

import (
	"errors"
	"fmt"
	"master/db"
	"master/utils"
	"os"
	"strings"
)

// 数据库字段检查
var mysqlMgr *MySqlMgr = nil

type MySqlMgr struct {
	tableCheck            []string
	logTableCheck         []string
	fieldCheck            [][2]string
	createTableStatements []string
	addField              [][3]string
	modifyField           [][4]string
	dropField             [][3]string
}

func GetSqlMgr() *MySqlMgr {
	if mysqlMgr == nil {
		mysqlMgr = &MySqlMgr{}
	}
	return mysqlMgr
}

func (self *MySqlMgr) initData() {
	self.tableCheck = []string{}

	self.fieldCheck = [][2]string{}

	self.logTableCheck = []string{}

	self.addField = [][3]string{
		{"tbl_crossarenarecord", "recordtype", "ALTER TABLE `tbl_crossarenarecord` ADD COLUMN `recordtype`  int(11) NOT NULL DEFAULT 0 COMMENT '战报类型' AFTER `fightid`;"},
		{"tbl_crossarenarecord", "updatetime", "ALTER TABLE `tbl_crossarenarecord` ADD COLUMN `updatetime`  int(11) NOT NULL DEFAULT 0 COMMENT '插入时间' AFTER `battlerecord`;"},
		{"tbl_rank", "uid", "ALTER TABLE `tbl_rank` ADD COLUMN `uid`  bigint(20) NOT NULL DEFAULT 0 COMMENT 'userId' AFTER `info`;"},
		{"tbl_user", "stripe", "ALTER TABLE `tbl_user` ADD COLUMN `stripe` text NOT NULL COMMENT '斑纹' AFTER `lifetree`;"},
		{"tbl_user", "spweapon", "ALTER TABLE `tbl_user` ADD COLUMN `spweapon` text NOT NULL COMMENT '专武' AFTER `stripe`;"},
		{"tbl_user", "futureweapons", "ALTER TABLE `tbl_user` ADD COLUMN `futureweapons` text NOT NULL COMMENT '剑术' AFTER `spweapon`;"},
	}

	self.modifyField = [][4]string{
		{"tbl_user", "data", "text", "ALTER TABLE `tbl_user` MODIFY COLUMN `data` mediumtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '基础数据' AFTER `lastupdate`;"},
		{"tbl_user", "fight", "int", "ALTER TABLE `tbl_user` MODIFY COLUMN `fight` bigint(20) NOT NULL COMMENT '战力' AFTER `level`;"},
	}

	self.createTableStatements = []string{
		self.General(),
		self.GeneralUser(),
		self.DimensionalUser(),
		self.ServerGroup(),
		self.ServerZone(),
		self.CrossArena(),
		self.CrossArenaRecord(),
		self.CrossArenaEx(),
		self.NewRank(),
		self.ServerGroupConfig(),
		self.ServerZoneConfig(),
		self.ServerGroupSet(),
		self.ConsumertopFirst(),
		self.DestroyMonster(),
		self.DestroyMonsterFirst(),
		self.CrossServerArena(),
		self.PeakArena(),
		self.ServerGroupHistory(),
		self.ServerZoneHistory(),
		self.MasterLog(),
		self.CrossServerLevelArena(),
		self.CosmicArena(),
		self.OfflineInfo(),
		self.UserRecord(),
		self.CityBrokenInfo(),
		self.CityBrokenConfig(),
		self.Champion(),
		self.Championeliminateinfo(),
		self.Championscoreinfo(),
		self.Fightinfochampion(),
		//self.FightMatchInfo(),
	}

	self.dropField = [][3]string{}
}

func (self *MySqlMgr) createTables() {
	for _, stmt := range self.createTableStatements {
		_, _, res := db.GetDBMgr().DBUser.Exec(stmt)
		if !res {
			os.Exit(1)
		}
	}
}

// 检查字段是否存在
func (self *MySqlMgr) CheckMysql() {
	self.initData()
	var checkErr error
	for _, filedName := range self.fieldCheck {
		checkErr = self.CheckFiled(filedName[0], filedName[1])
		if checkErr != nil {
			os.Exit(1)
		}
	}

	for index := range self.tableCheck {
		checkErr = self.CheckTable(self.tableCheck[index])
		if checkErr != nil {
			os.Exit(1)
		}
	}

	/*
		for index := range self.logTableCheck {
			checkErr = self.CheckLogTable(self.logTableCheck[index])
			if checkErr != nil {
				os.Exit(1)
			}
		}
	*/

	self.createTables()
	self.checkAddField()
	self.modifyColumnType()
}

func (self *MySqlMgr) CheckFiled(tableName string, filedName string) error {
	sql := "SELECT * FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'  AND COLUMN_NAME = '%s'"
	dbName := self.getDbName()
	if dbName == "" {
		return nil
	}
	sqlStr := fmt.Sprintf(sql, dbName, tableName, filedName)
	res := db.GetDBMgr().DBUser.Query(sqlStr)
	//LogDebug("sqlStr:", sqlStr, ", CheckFiledParam:", res)
	if res {
		return nil
	}
	return errors.New(tableName + " has no filed:" + filedName)
}

func (self *MySqlMgr) CheckTable(tableName string) error {
	sql := "SELECT * FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'"
	dbName := self.getDbName()
	if dbName == "" {
		return nil
	}
	sqlStr := fmt.Sprintf(sql, dbName, tableName)
	res := db.GetDBMgr().DBUser.Query(sqlStr)
	//LogDebug("sqlStr:", sqlStr, ", CheckFiledParam:", res)
	if res {
		return nil
	}
	return errors.New(tableName + " not exists!")
}

func (self *MySqlMgr) getDbName() string {
	sqlSplit1 := strings.Split(GetMasterApp().Conf.DBConf.DBUser, "?")
	if len(sqlSplit1) < 1 {
		return ""
	}
	sqlSplit2 := strings.Split(sqlSplit1[0], "/")
	if len(sqlSplit2) < 2 {
		return ""
	}
	return sqlSplit2[1]
}

func (self *MySqlMgr) getDbLogName() string {
	/*
		sqlSplit1 := strings.Split(db.GetServer().Con.DBCon.DBLog, "?")
		if len(sqlSplit1) < 1 {
			return ""
		}
		sqlSplit2 := strings.Split(sqlSplit1[0], "/")
		if len(sqlSplit2) < 2 {
			return ""
		}
		return sqlSplit2[1]
	*/
	return ""
}

func (self *MySqlMgr) CheckLogTable(tableName string) error {
	/*
		sql := "SELECT * FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'"
		dbName := self.getDbLogName()
		if dbName == "" {
			LogDebug("dbName is empty!")
			return nil
		}
		sqlStr := fmt.Sprintf(sql, dbName, tableName)
		res := GetServer().DBLog.Query(sqlStr)
		if res {
			return nil
		}
		return errors.New(tableName + " not exists!")
	*/
	return nil
}

func (self *MySqlMgr) checkAddField() {
	var checkErr error
	for _, stmt := range self.addField {
		checkErr = self.CheckFiled(stmt[0], stmt[1])
		if checkErr != nil { // 没有才插入
			_, _, res := db.GetDBMgr().DBUser.Exec(stmt[2])
			if !res {
				os.Exit(1)
			}
		}
	}
}

func (self *MySqlMgr) modifyColumnType() {
	for _, stmt := range self.modifyField {
		if len(stmt) != 4 {
			continue
		}

		isStmtOk := true
		for _, v := range stmt {
			if v == "" {
				isStmtOk = false
				break
			}
		}

		if !isStmtOk {
			break
		}

		sql := "SELECT DATA_TYPE FROM information_schema.COLUMNS WHERE TABLE_SCHEMA = '%s' AND TABLE_NAME = '%s'  AND COLUMN_NAME = '%s'"
		dbName := self.getDbName()
		if dbName == "" {
			utils.LogError("dbName is empty!")
			os.Exit(1)
		}

		sqlStr := fmt.Sprintf(sql, dbName, stmt[0], stmt[1])
		res, fieldName := db.GetDBMgr().DBUser.QueryColomn(sqlStr)
		if res && fieldName == stmt[2] {
			_, _, res := db.GetDBMgr().DBUser.Exec(stmt[3])
			if !res {
				utils.LogError("修改字段类型失败", stmt[3])
				os.Exit(1)
			} else {
				utils.LogDebug("修改字段类型成功!")
			}
		}
	}
}

func (self *MySqlMgr) General() string {
	return `CREATE TABLE IF NOT EXISTS tbl_general(
keyid bigint(20) NOT NULL COMMENT '唯一Id',
generalusertop text NOT NULL COMMENT '玩家信息',
PRIMARY KEY (keyid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) GeneralUser() string {
	return `CREATE TABLE IF NOT EXISTS tbl_generaluser(
        id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		uid bigint(20) NOT NULL COMMENT 'uid',
		keyid int(11) NOT NULL COMMENT '活动期数',
		svrid int(11) NOT NULL COMMENT '服务器ID',
        info text NOT NULL COMMENT '具体数据',
        PRIMARY KEY (id) USING BTREE,
		INDEX FIGHT_ID(uid,keyid) USING BTREE COMMENT ''
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) DimensionalUser() string {
	return `CREATE TABLE IF NOT EXISTS tbl_dimensionaluser(
		id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		uid bigint(20) NOT NULL COMMENT 'uid',
		keyid int(11) NOT NULL COMMENT '活动期数',
		svrid int(11) NOT NULL COMMENT '服务器ID',
		info text NOT NULL COMMENT '具体数据',
		PRIMARY KEY (id) USING BTREE,
		INDEX FIGHT_ID(uid,keyid) USING BTREE COMMENT ''
	) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) ServerGroup() string {
	return `CREATE TABLE IF NOT EXISTS tbl_servergroup(
serverid int(11) NOT NULL COMMENT '服务器ID',
groupid int(11) NOT NULL COMMENT '分组ID',
groupservers text NOT NULL COMMENT '分组信息',
lastgroupservers text NOT NULL COMMENT '上次分组信息',
param text NOT NULL COMMENT '服务器参数',
activityinfo text NOT NULL COMMENT '活动参数',
PRIMARY KEY (serverid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) ServerZone() string {
	return `CREATE TABLE IF NOT EXISTS tbl_serverzone(
serverid int(11) NOT NULL COMMENT '服务器ID',
zoneid int(11) NOT NULL COMMENT '分区ID',
zoneservers text NOT NULL COMMENT '分区信息',
lastzoneservers text NOT NULL COMMENT '上次分区信息',
param text NOT NULL COMMENT '服务器参数',
activityinfo text NOT NULL COMMENT '活动参数',
PRIMARY KEY (serverid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) CrossArena() string {
	return `CREATE TABLE IF NOT EXISTS tbl_crossarena(
		keyid bigint(20) NOT NULL COMMENT '唯一Id',
		crossarenausertop mediumtext NOT NULL COMMENT '排行信息',
		arenafights mediumtext NOT NULL COMMENT '玩家信息',
		PRIMARY KEY (keyid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) CrossArenaRecord() string {
	return `CREATE TABLE IF NOT EXISTS tbl_crossarenarecord(
		id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
  		fightid bigint(20) NULL DEFAULT NULL COMMENT '战斗ID',
		battleinfo mediumtext NOT NULL COMMENT '简报',
		battlerecord mediumtext NOT NULL COMMENT '详细战报',
		PRIMARY KEY (id) USING BTREE,
  		INDEX FIGHT_ID(fightid) USING BTREE COMMENT '战斗ID'
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) CrossArenaEx() string {
	return `CREATE TABLE IF NOT EXISTS tbl_crossarenaex(
        id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		uid bigint(20) NOT NULL COMMENT 'uid',
		keyid int(11) NOT NULL COMMENT '活动期数',
		svrid int(11) NOT NULL COMMENT '服务器ID',
        info text NOT NULL COMMENT '具体数据',
        PRIMARY KEY (id) USING BTREE,
		INDEX FIGHT_ID(uid,keyid) USING BTREE COMMENT ''
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) FightMatchInfo() string {
	return `CREATE TABLE IF NOT EXISTS tbl_fightmatchinfo(
        keyid int(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '赛区',
		period int(20) NOT NULL COMMENT '期数',
        PRIMARY KEY (keyid) USING BTREE,
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) NewRank() string {
	return `CREATE TABLE IF NOT EXISTS tbl_rank(
        id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		rankid int(11) NOT NULL COMMENT '',
		period int(11) NOT NULL COMMENT '',
		rankpos int(11) NOT NULL COMMENT '',
        info text NOT NULL COMMENT '具体数据',
        PRIMARY KEY (id) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) ServerGroupConfig() string {
	return `CREATE TABLE IF NOT EXISTS tbl_servergroupconfig(
id int(11) NOT NULL COMMENT 'ID',
periods int(11) NOT NULL COMMENT '期数',
starttime bigint(20) NOT NULL COMMENT '开始时间',
centretime bigint(20) NOT NULL COMMENT '中间期',
endtime bigint(20) NOT NULL COMMENT '结束时间',
crossserverplayconfig text NOT NULL COMMENT '玩法配置',
groupmax int(11) NOT NULL COMMENT '当前最大分组',
PRIMARY KEY (id) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) ServerZoneConfig() string {
	return `CREATE TABLE IF NOT EXISTS tbl_serverzoneconfig(
id int(11) NOT NULL COMMENT 'ID',
periods int(11) NOT NULL COMMENT '期数',
starttime bigint(20) NOT NULL COMMENT '开始时间',
centretime bigint(20) NOT NULL COMMENT '中间期',
endtime bigint(20) NOT NULL COMMENT '结束时间',
crossserverplayconfig text NOT NULL COMMENT '玩法配置',
groupmax int(11) NOT NULL COMMENT '当前最大分组',
PRIMARY KEY (id) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) ServerGroupSet() string {
	return `CREATE TABLE IF NOT EXISTS tbl_servergroupset(
serverid int(11) NOT NULL COMMENT '服务器ID',
groupid int(11) NOT NULL COMMENT '分组ID',
PRIMARY KEY (serverid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) CrossServerArena() string {
	return `CREATE TABLE IF NOT EXISTS tbl_crossserverarena(
        id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		uid bigint(20) NOT NULL COMMENT 'uid',
		periods int(11) NOT NULL COMMENT '期数',
		groupid int(11) NOT NULL COMMENT '分组',
		svrid int(11) NOT NULL COMMENT '服务器ID',
        info text NOT NULL COMMENT '比赛数据',
        fightinfos text NOT NULL COMMENT '防守阵容',
        PRIMARY KEY (id) USING BTREE,
		INDEX FIGHT_ID(uid,periods) USING BTREE COMMENT ''
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) PeakArena() string {
	return `CREATE TABLE IF NOT EXISTS tbl_peakarena(
        id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		uid bigint(20) NOT NULL COMMENT 'uid',
		periods int(11) NOT NULL COMMENT '期数',
		groupid int(11) NOT NULL COMMENT '分组',
		svrid int(11) NOT NULL COMMENT '服务器ID',
        info text NOT NULL COMMENT '比赛数据',
        fightinfos text NOT NULL COMMENT '防守阵容',
        PRIMARY KEY (id) USING BTREE,
		INDEX FIGHT_ID(uid,periods) USING BTREE COMMENT ''
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) ServerGroupHistory() string {
	return `CREATE TABLE IF NOT EXISTS tbl_servergrouphistory(
id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
serverid int(11) NOT NULL COMMENT '服务器ID',
groupid int(11) NOT NULL COMMENT '分组ID',
groupservers text NOT NULL COMMENT '分组信息',
lastgroupservers text NOT NULL COMMENT '上次分组信息',
param text NOT NULL COMMENT '服务器参数',
activityinfo text NOT NULL COMMENT '活动参数',
starttime bigint(20) NOT NULL COMMENT '开始时间',
endtime bigint(20) NOT NULL COMMENT '结束时间',
periods int(11) NOT NULL COMMENT '期数',
PRIMARY KEY (id)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) ServerZoneHistory() string {
	return `CREATE TABLE IF NOT EXISTS tbl_serverzonehistory(
id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
serverid int(11) NOT NULL COMMENT '服务器ID',
zoneid int(11) NOT NULL COMMENT '分组ID',
zoneservers text NOT NULL COMMENT '分组信息',
lastzoneservers text NOT NULL COMMENT '上次分组信息',
param text NOT NULL COMMENT '服务器参数',
activityinfo text NOT NULL COMMENT '活动参数',
starttime bigint(20) NOT NULL COMMENT '开始时间',
endtime bigint(20) NOT NULL COMMENT '结束时间',
periods int(11) NOT NULL COMMENT '期数',
PRIMARY KEY (id)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) MasterLog() string {
	return `CREATE TABLE IF NOT EXISTS tbl_masterlog(
id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
logid int(11) NOT NULL COMMENT 'LOGID',
info text NOT NULL COMMENT '记录',
PRIMARY KEY (id)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) CrossServerLevelArena() string {
	return `CREATE TABLE IF NOT EXISTS tbl_crossserverlevelarena(
        id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		uid bigint(20) NOT NULL COMMENT 'uid',
		periods int(11) NOT NULL COMMENT '期数',
		groupid int(11) NOT NULL COMMENT '分组',
		svrid int(11) NOT NULL COMMENT '服务器ID',
        info text NOT NULL COMMENT '比赛数据',
        fightinfos text NOT NULL COMMENT '防守阵容',
        PRIMARY KEY (id) USING BTREE,
		INDEX FIGHT_ID(uid,periods) USING BTREE COMMENT ''
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) CosmicArena() string {
	return `CREATE TABLE IF NOT EXISTS tbl_cosmicarena(
        id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		uid bigint(20) NOT NULL COMMENT 'uid',
		periods int(11) NOT NULL COMMENT '期数',
		groupid int(11) NOT NULL COMMENT '分组',
		svrid int(11) NOT NULL COMMENT '服务器ID',
        info text NOT NULL COMMENT '比赛数据',
        fightinfos text NOT NULL COMMENT '防守阵容',
        PRIMARY KEY (id) USING BTREE,
		INDEX FIGHT_ID(uid,periods) USING BTREE COMMENT ''
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) OfflineInfo() string {
	return `CREATE TABLE IF NOT EXISTS tbl_offlineinfo(
		uid bigint(20) NOT NULL COMMENT 'uid',
        info text NOT NULL COMMENT '数据',
        PRIMARY KEY (uid) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) CityBrokenInfo() string {
	return `CREATE TABLE IF NOT EXISTS tbl_citybrokenuser (
		citybrokenid bigint(20) NOT NULL COMMENT '城主编号',
		groupid int(11) NOT NULL COMMENT '分组',
		seasontime bigint(20) NOT NULL COMMENT '赛季刷新时间',
		fightinfo text NOT NULL COMMENT '战斗结构',
		starttime bigint(20) NOT NULL COMMENT '占领起始时间',
		attacktime bigint(20) NOT NULL COMMENT '攻击者的攻击时间',
		attackuid bigint(20) NOT NULL COMMENT '攻击者的uid',
		shieldtime bigint(20) NOT NULL COMMENT '护盾保护起始时间',
		PRIMARY KEY (citybrokenid,groupid)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) CityBrokenConfig() string {
	return `CREATE TABLE IF NOT EXISTS tbl_citybrokenconfig (
		id int(10) NOT NULL COMMENT 'ID',
		keyid int(10) NOT NULL COMMENT 'keyid', 
		starttime bigint(20) NOT NULL COMMENT '开始时间',
		adtime bigint(20) NOT NULL COMMENT '预选赛',
		simulatetime bigint(20) NOT NULL COMMENT '海选赛',
		fighttime bigint(20) NOT NULL COMMENT '总决赛',
		resulttime bigint(20) NOT NULL COMMENT '展示期',
		endtime bigint(20) NOT NULL COMMENT '结束时间',
		PRIMARY KEY (id) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) ConsumertopFirst() string {
	return `CREATE TABLE IF NOT EXISTS tbl_consumertopfirst(
        id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		uid bigint(20) NOT NULL COMMENT 'uid',
		keyid int(11) NOT NULL COMMENT '活动期数',
		svrid int(11) NOT NULL COMMENT '服务器ID',
        info text NOT NULL COMMENT '具体数据',
        PRIMARY KEY (id) USING BTREE,
		INDEX FIGHT_ID(uid,keyid) USING BTREE COMMENT ''
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) DestroyMonsterFirst() string {
	return `CREATE TABLE IF NOT EXISTS tbl_destroymonsterfirst(
        id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		uid bigint(20) NOT NULL COMMENT 'uid',
		keyid int(11) NOT NULL COMMENT '活动期数',
		svrid int(11) NOT NULL COMMENT '服务器ID',
        info text NOT NULL COMMENT '具体数据',
        PRIMARY KEY (id) USING BTREE,
		INDEX FIGHT_ID(uid,keyid) USING BTREE COMMENT ''
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) DestroyMonster() string {
	return `CREATE TABLE IF NOT EXISTS tbl_destroymonster(
        id bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		uid bigint(20) NOT NULL COMMENT 'uid',
		keyid int(11) NOT NULL COMMENT '活动期数',
		svrid int(11) NOT NULL COMMENT '服务器ID',
        info text NOT NULL COMMENT '具体数据',
        PRIMARY KEY (id) USING BTREE,
		INDEX FIGHT_ID(uid,keyid) USING BTREE COMMENT ''
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
func (self *MySqlMgr) UserRecord() string {
	return `CREATE TABLE IF NOT EXISTS tbl_user(
        uid bigint(20)  NOT NULL COMMENT '角色id',
		uname text NOT NULL COMMENT '昵称',
		level int(11) NOT NULL COMMENT '等级',
		fight int(11) NOT NULL COMMENT '战力',
		passid int(11) NOT NULL COMMENT '关卡id',
		serverid int(11) NOT NULL COMMENT '服务器id',
		regtime int(11) NOT NULL COMMENT '注册时间',
		logintime int(11) NOT NULL COMMENT '登录时间',
		lastupdate int(11) NOT NULL COMMENT '上次更新时间',
		data mediumtext NOT NULL COMMENT '基础数据',
		heros text NOT NULL COMMENT '阵容列表',
		equips text NOT NULL COMMENT '阵容装备列表',
		lifetree text NOT NULL COMMENT '生命树',
		stripe text NOT NULL COMMENT '斑纹',
		spweapon text NOT NULL COMMENT '专武',
		futureweapons text NOT NULL COMMENT '剑术',
        PRIMARY KEY (uid) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) Champion() string {
	return `CREATE TABLE IF NOT EXISTS tbl_champion(
        id int(11)  NOT NULL AUTO_INCREMENT COMMENT '角色id',
		period int(11) NOT NULL COMMENT '期数',
		acttype int(11) NOT NULL COMMENT '活动类型',
		zoneid int(11) NOT NULL COMMENT '组',
		stage int(11) NOT NULL COMMENT '阶段',
		signtime bigint(20) NOT NULL COMMENT '数据生成时间',
		scoretime bigint(20) NOT NULL COMMENT '积分赛开始时间',
		eliminatetime bigint(20) NOT NULL COMMENT '淘汰赛开始时间',
		endtime bigint(20) NOT NULL COMMENT '结束时间',
		nexttime bigint(20) NOT NULL COMMENT '清空数据时间',
		scoreinfo text  NOT NULL COMMENT '积分赛信息',
		eliminateinfo text  NOT NULL COMMENT '淘汰赛信息',
		betinfo text NOT NULL COMMENT '下注信息',
		toprank text NOT NULL COMMENT '排行信息',
		historyinfo text NOT NULL COMMENT '殿堂',
		nextsigntime bigint(20) NOT NULL COMMENT '展示预告时间',
		nextendtime bigint(20) NOT NULL COMMENT '展示预告时间',
        PRIMARY KEY (id) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) Championeliminateinfo() string {
	return `CREATE TABLE IF NOT EXISTS tbl_championeliminateinfo(
        id int(11)  NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		period int(11) NOT NULL COMMENT '期数',
		acttype int(11) NOT NULL COMMENT '活动类型',
		zoneid int(11) NOT NULL COMMENT '组',
		eliminateturnid int(11) NOT NULL COMMENT '轮次',
		eliminatematchstage int(11) NOT NULL COMMENT '当前阶段',
		betteamid int(11) NOT NULL COMMENT '下注id',
		betend bigint(20) NOT NULL COMMENT '下注结束时间',
		battleend bigint(20) NOT NULL COMMENT '战斗结束时间',
		eliminatematchteamstr text NOT NULL COMMENT '积分赛信息',
		eliminateuserstr text NOT NULL COMMENT '用户信息',
        PRIMARY KEY (id) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) Championscoreinfo() string {
	return `CREATE TABLE IF NOT EXISTS tbl_championscoreinfo(
        id int(11)  NOT NULL AUTO_INCREMENT COMMENT '唯一Id',
		period int(11) NOT NULL COMMENT '期数',
		acttype int(11) NOT NULL COMMENT '活动类型',
		zoneid int(11) NOT NULL COMMENT '组',
		scoreturnid int(11) NOT NULL COMMENT '轮次',
		scorematchstage int(11) NOT NULL COMMENT '当前阶段',
		betteamid int(11) NOT NULL COMMENT '下注id',
		betend bigint(20) NOT NULL COMMENT '下注结束时间',
		battleend bigint(20) NOT NULL COMMENT '战斗结束时间',
		userfight bigint(20) NOT NULL COMMENT '平均战力系数',
		scorematchteamstr text NOT NULL COMMENT '积分赛信息',
		scoreuserstr text NOT NULL COMMENT '用户信息',
        PRIMARY KEY (id) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}

func (self *MySqlMgr) Fightinfochampion() string {
	return `CREATE TABLE IF NOT EXISTS tbl_fightinfochampion(
		uid bigint(20) NOT NULL COMMENT '唯一Id',
		acttype int(11) NOT NULL COMMENT '活动类型',
		fightinfo mediumtext NOT NULL COMMENT '战斗结构',
        PRIMARY KEY (uid) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8;`
}
