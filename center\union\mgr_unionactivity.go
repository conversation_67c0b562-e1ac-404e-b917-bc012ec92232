package union

// 公会强交互玩法
import (
	"fmt"
	"master/center/battle"
	"master/center/crossserver"
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
	"net/http"
	"sort"
	"sync"
	"time"
)

const (
	TABLE_NAME_UNIONACTIVITY           = "san_unionactivity"        // 公会战-总表
	TABLE_NAME_UNIONACTIVITY_HISTORY   = "san_unionactivityhistory" // 公会战-历史记录
	TABLE_NAME_UNIONACTIVITY_FIGHTINFO = "san_fightinfouacache"     // 公会战-战斗缓存
	TABLE_NAME_UNIONACTIVITY_FIGHT     = "san_unionactivityfight"   // 公会战-对战信息
)

const (
	MSG_UNION_ACTIVITY_INFO              = "union_activity_info"         // 上线推送数据
	MSG_UNION_ACTIVITY_INFO_HISTORY      = "union_activity_info_history" // 上一期数据
	MSG_UNION_CHALLENGE_TRIAL_BEGIN      = "union_challenge_trial_begin" //
	MSG_UNION_CHALLENGE_TRIAL_END        = "union_challenge_trial_end"
	MSG_UNION_GET_RED_PACKET             = "union_get_red_packet"
	MSG_UNION_GET_TRIAL_AWARD            = "union_get_trial_award"
	MSG_UNION_ACTIVITY_GET_NODE          = "union_activity_get_node"      //定时请求新的对战数据
	MSG_UNION_ACTIVITY_ATTACK_START      = "union_activity_attack_start"  //战斗开始
	MSG_UNION_ACTIVITY_ATTACK_END        = "union_activity_attack_end"    //战斗结束
	MSG_UNION_ACTIVITY_SYN               = "union_activity_syn"           // 战斗阶段改变
	MSG_UNION_ACTIVITY_GET_UNION_SYN     = "union_activity_get_union_syn" //定时请求新的对战数据
	MSG_UNION_ACTIVITY_DECLARE           = "union_activity_declare"       //宣告数据
	MSG_UNION_ACTIVITY_ATTACK_KILL       = "union_activity_attack_kill"   //吊打
	MSG_UNION_ACTIVITY_LIKE              = "union_activity_like"          //点赞
	MSG_UNION_GET_ACTIVITY_MASTER_NOTICE = "union_get_activity_master_notice"
)

// UnionActivityMgr ! 公会战管理类
type UnionActivityMgr struct {
	UnionActivityInfo        *UnionActivityInfo //! 公会战对象
	UnionActivityFightInfo   *sync.Map          //! 参加战斗的对象信息 map[int64]*FightInfoCache
	Locker                   *sync.RWMutex      //! 操作锁
	UnionActivityInfoHistory *UnionActivityInfo //! 历史数据

	MapBattleGroup   *sync.Map //! map[int]*UnionActivityInfo, 公会战对象，按server group 分组
	MapBattleHistory *sync.Map //! map[int]*UnionActivityInfo, 历史数据，保存上一期的实时数据
	MapUnionId       *sync.Map //! map[int64]int，存储角色uid => unionid的数据
	Period           int       //! 当前期数，上一期 = -1
}

var s_unionbattlemgr *UnionActivityMgr = nil

func GetUnionActivityMgr() *UnionActivityMgr {
	if s_unionbattlemgr == nil {
		s_unionbattlemgr = new(UnionActivityMgr)
		s_unionbattlemgr.Locker = new(sync.RWMutex)
		s_unionbattlemgr.UnionActivityFightInfo = new(sync.Map) //make(map[int64]*FightInfoCache)
		s_unionbattlemgr.UnionActivityInfo = new(UnionActivityInfo)
		s_unionbattlemgr.UnionActivityInfo.UnionFight = new(sync.Map)
		s_unionbattlemgr.UnionActivityInfo.UnionMap = new(sync.Map)
		s_unionbattlemgr.UnionActivityInfoHistory = new(UnionActivityInfo)
		s_unionbattlemgr.UnionActivityInfoHistory.UnionFight = new(sync.Map)
		s_unionbattlemgr.UnionActivityInfoHistory.UnionMap = new(sync.Map)

		s_unionbattlemgr.MapBattleGroup = new(sync.Map)
		s_unionbattlemgr.MapBattleHistory = new(sync.Map)
		s_unionbattlemgr.MapUnionId = new(sync.Map)
	}
	return s_unionbattlemgr
}

func (self *UnionActivityMgr) Run() {
	ticker := time.NewTicker(time.Minute * 1)
	for {
		select {
		case <-ticker.C:
			self.OnTimer()
		}
	}
}

// GetUnionActivityData 数据初始化
func (self *UnionActivityMgr) GetUnionActivityData() {
	self.Locker.Lock()
	defer self.Locker.Unlock()

	self.LoadUnionActivity()
	//self.LoadFightInfo()
	self.LoadHistory()
	return
}

func (self *UnionActivityMgr) LoadUnionActivity() {
	queryStr := fmt.Sprintf("select * from `%s` ", TABLE_NAME_UNIONACTIVITY)
	var msg UnionActivityInfo
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	if len(res) > 0 {
		self.UnionActivityInfo = res[0].(*UnionActivityInfo)
		self.UnionActivityInfo.UnionFight = new(sync.Map)
		self.UnionActivityInfo.UnionMap = new(sync.Map)
	} else {
		self.UnionActivityInfo.Id = 1
		self.UnionActivityInfo.Encode()
		db.InsertTable(TABLE_NAME_UNIONACTIVITY, self.UnionActivityInfo, 0, false)
	}
	self.UnionActivityInfo.Init(TABLE_NAME_UNIONACTIVITY, self.UnionActivityInfo, false)

	//读取所有比赛
	period := self.UnionActivityInfo.Period
	var unionFight UnionFight
	sql := fmt.Sprintf("select * from `%s` where period=%d", TABLE_NAME_UNIONACTIVITY_FIGHT, period)
	resUnionFight := db.GetDBMgr().DBUser.GetAllData(sql, &unionFight)

	if self.UnionActivityInfo.UnionFight == nil {
		self.UnionActivityInfo.UnionFight = new(sync.Map)
	}

	if len(resUnionFight) > 0 {
		for i := 0; i < len(resUnionFight); i++ {
			if resUnionFight[i] == nil {
				continue
			}
			data := resUnionFight[i].(*UnionFight)
			data.Decode()
			if data.AttackSide == nil || data.DefenceSide == nil {
				continue
			}
			data.Init(TABLE_NAME_UNIONACTIVITY_FIGHT, data, false)
			self.UnionActivityInfo.UnionFight.Store(data.TeamId, data)
			self.UnionActivityInfo.UnionMap.Store(data.AttackSide.UnionId, data.TeamId)
			self.UnionActivityInfo.UnionMap.Store(data.DefenceSide.UnionId, data.TeamId)
		}
	}
}

// ! 加载公会战-战斗缓存数据
func (self *UnionActivityMgr) loadUserFightInfo(uid int64) (bool, *FightInfoCache) {
	var fightInfoCacheUB FightInfoCacheUB
	sql := fmt.Sprintf("select * from `%s` where uid = %d limit 1", TABLE_NAME_UNIONACTIVITY_FIGHTINFO, uid)
	res := db.GetDBMgr().DBUser.GetAllData(sql, &fightInfoCacheUB)
	for i := 0; i < len(res); i++ {
		data := res[i].(*FightInfoCacheUB)
		data.Decode()
		data.Init(TABLE_NAME_UNIONACTIVITY_FIGHTINFO, data, false)

		fightInfoCache := new(FightInfoCache)
		fightInfoCache.NeedSave = false
		fightInfoCache.LastLiveTime = model.TimeServer().Unix()
		fightInfoCache.FightInfoCacheDB = data
		self.UnionActivityFightInfo.Store(fightInfoCache.FightInfoCacheDB.Uid, fightInfoCache)
		return true, fightInfoCache
	}
	return false, nil
}

// GetUserUnionId ! 获得公会Id
func (self *UnionActivityMgr) GetUserUnionId(uid int64) int {
	if id, ok := self.MapUnionId.Load(uid); ok {
		return id.(int)
	}

	return 0
}

// LoadHistory 加载历史数据
// 只用加载上一期的历史数据
func (self *UnionActivityMgr) LoadHistory() {
	queryStrHistory := fmt.Sprintf("select * from `%s` order by id desc limit 1", TABLE_NAME_UNIONACTIVITY_HISTORY)
	var msgHistory UnionActivityInfo
	resHistory := db.GetDBMgr().DBUser.GetAllData(queryStrHistory, &msgHistory)

	if len(resHistory) == 0 {
		return
	}

	self.UnionActivityInfoHistory = resHistory[0].(*UnionActivityInfo)
	self.UnionActivityInfoHistory.Decode()
	self.UnionActivityInfoHistory.UnionFight = new(sync.Map)
	self.UnionActivityInfoHistory.UnionMap = new(sync.Map)

	//读取所有比赛
	period := self.UnionActivityInfoHistory.Period - 1
	var scoreMatchInfo UnionFight
	sql := fmt.Sprintf("select * from `%s` where period=%d", TABLE_NAME_UNIONACTIVITY_FIGHT, period)
	resUnionFight := db.GetDBMgr().DBUser.GetAllData(sql, &scoreMatchInfo)

	if len(resUnionFight) > 0 {
		for i := 0; i < len(resUnionFight); i++ {
			data := resUnionFight[i].(*UnionFight)
			data.Decode()
			data.Init(TABLE_NAME_UNIONACTIVITY_FIGHT, data, false)
			self.UnionActivityInfoHistory.UnionFight.Store(data.TeamId, data)
			self.UnionActivityInfoHistory.UnionMap.Store(data.AttackSide.UnionId, data.TeamId)
			self.UnionActivityInfoHistory.UnionMap.Store(data.DefenceSide.UnionId, data.TeamId)
		}
	}
	return
}

func (self *UnionActivityMgr) OnTimer() {
	self.UnionActivityInfo.OnTimer()
}

// Save ! 保存数据
func (self *UnionActivityMgr) Save() {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	self.UnionActivityInfo.Save()
	//for _, v := range self.UnionActivityFightInfo {
	//	v.Save()
	//}
	var limitTimeSecs int64 = core.MIN_SECS * 30
	now := model.TimeServer().Unix()
	self.UnionActivityFightInfo.Range(func(key, value interface{}) bool {
		info := value.(*crossserver.FightInfoCache)
		info.Save()
		if now > info.LastLiveTime && (now-info.LastLiveTime) > limitTimeSecs {
			self.UnionActivityFightInfo.Delete(key)
		}
		return true
	})
}

// ! 获得角色的战斗缓存数据
func (self *UnionActivityMgr) getFightInfoCache(uid int64) (*FightInfoCache, bool) {
	info, ok := self.UnionActivityFightInfo.Load(uid)
	if !ok {
		if _ok, _data := self.loadUserFightInfo(uid); _ok && _data != nil {
			return _data, true
		}
		return nil, false
	}
	info.(*FightInfoCache).LastLiveTime = model.TimeServer().Unix()
	return info.(*FightInfoCache), true
}

// ! 更新数据
func (self *UnionActivityMgr) setFightInfoCache(uid int64, info *FightInfoCache) bool {
	info.LastLiveTime = model.TimeServer().Unix()
	self.UnionActivityFightInfo.Store(uid, info)
	return true
}

// SendRankAward ! 发送公会战排行榜奖励
func (self *UnionActivityMgr) SendRankAward(sid int, uid int64, rankid int) {
	// TODO

}

// SendResultAward ! 发送公会战胜负奖励
func (self *UnionActivityMgr) SendResultAward(sid int, uid int64, result int) {
	// TODO

}

// GetUnionFightByUnionId ! 根据服务器Id，公会Id，获得公会对象JSON，同步给客户端
func (self *UnionActivityMgr) GetUnionFightByUnionId(servId int, unionId int) *JS_UnionFight {
	unionBattle := self.GetUnionBattle(servId)
	if unionBattle == nil {
		return nil
	}

	idData, ok := unionBattle.UnionMap.Load(unionId)
	if !ok {
		return nil
	}

	unionFightData, ok := unionBattle.UnionFight.Load(idData.(int))
	if !ok {
		return nil
	}

	unionFight := unionFightData.(*UnionFight)
	if unionFight == nil {
		return nil
	}

	self.UpdateUnionFightSide(unionFight.AttackSide)
	self.UpdateUnionFightSide(unionFight.DefenceSide)
	jsUnionFight := GetJsUnionFight(unionFight)
	return jsUnionFight
}

func (self *UnionActivityMgr) RCP_GetInfo(serverId int, unionId int) (int, string) {
	//! 根据服务器Id，获得区服所在的server group分组，然后获得UnionActivityInfo对象
	unionBattle := self.GetUnionBattle(serverId)
	if unionBattle != nil {
		idData, ok := unionBattle.UnionMap.Load(unionId)
		if !ok {
			return 0, ""
		}

		unionFightData, ok := unionBattle.UnionFight.Load(idData.(int))
		if !ok {
			return 0, ""
		}

		unionFight := unionFightData.(*UnionFight)
		if unionFight == nil {
			return 0, ""
		}
		self.UpdateUnionFightSide(unionFight.AttackSide)
		self.UpdateUnionFightSide(unionFight.DefenceSide)
		jsUnionFight := GetJsUnionFight(unionFight)
		return 1, utils.Lz4Encode(jsUnionFight)
	}
	return 0, ""
}

func (self *UnionActivityMgr) GetHistory(servId int, unionId int) (int, string) {
	unionFight := GetUnionActivityMgr().GetUnionFightByUnionId(servId, unionId)
	if unionFight == nil {
		return 0, ""
	}

	return 1, utils.HF_JtoA(unionFight)
}

// Declare ! 宣告目标
func (self *UnionActivityMgr) Declare(uid int64, servId int, unionId int, targetUid int64, action int) (int, string) {
	unionBattle := GetUnionActivityMgr().GetUnionBattle(servId)
	if unionBattle == nil {
		return 2, "STR_MGR_NO_NODE_INFO"
	}

	unionFight := unionBattle.GetUnionFight(unionId)
	if unionFight == nil {
		return 2, "STR_MGR_NO_NODE_INFO"
	}

	//判断自己的side
	side := unionFight.GetUnionSide(unionId)
	if side == core.POS_INVALID {
		return 2, "STR_MGR_NO_NODE_INFO"
	}

	//拿到对方数据
	targetNode, targetSide := unionFight.GetNode(targetUid)
	if targetNode == nil {
		return 2, "STR_MGR_NO_NODE_INFO"
	}
	if side == targetSide {
		return 2, "STR_MGR_DECLARE_SELF"
	}

	if action == core.LOGIC_TRUE {
		key := 1
		if len(targetNode.Declare) > 0 {
			key = targetNode.Declare[len(targetNode.Declare)-1].Key + 1
		}
		newDeclare := new(Declare)
		newDeclare.Key = key
		newDeclare.Uid = uid
		/*baseInfoSimple, ok := GetOfflineInfoMgr().GetUserBaseInfoSimple(newDeclare.Uid)
		if ok {
			newDeclare.BaseInfoSimple = baseInfoSimple
		}*/
		targetNode.Declare = append(targetNode.Declare, newDeclare)
	} else {
		newList := make([]*Declare, 0)
		for _, v := range targetNode.Declare {
			//if v.Key == msg.Key {
			//	continue
			//}
			newList = append(newList, v)
		}
		targetNode.Declare = newList
	}
	targetNode.Init()
	/*var msgRel protocol.S2C_UnionActivityDeclare
	msgRel.Cid = MSG_UNION_ACTIVITY_DECLARE
	msgRel.Action = msg.Action
	msgRel.UnionFightMemberNode = targetNode
	player.SendMsg(msgRel.Cid, utils.HF_JtoB(&msgRel))*/

	return 1, utils.HF_JtoA(unionFight)
}

func (self *UnionActivityMgr) SyncStatus(servid int) {
	unionBattle := self.GetUnionBattle(servid)
	var status RPC_UnionFightStatus
	status.Stage = unionBattle.Stage
	status.StartTime = unionBattle.StartTime
	status.AttackTime = unionBattle.AttackTime
	status.EndTime = unionBattle.EndTime

	core.GetCenterApp().AddEvent(servid, core.UNION_BATTLE_EVENT_SYN, 0, 0, 1,
		utils.HF_JtoA(&status))
}

// RPC_GetNode ! 获得对象的节点
func (self *UnionActivityMgr) RPC_GetNode(serverId int, unionId int, targetUid int64) (int, string, string) {
	unionFight := self.GetUnionFight(serverId, unionId)
	if unionFight == nil {
		return 2, "STR_MGR_NO_NODE_INFO", ""
	}

	//! 遍历 攻方，守方获得
	var retNode *UnionFightMemberNode = nil
	for i := 0; i < len(unionFight.AttackSide.UnionFightMemberNode); i++ {
		node := unionFight.AttackSide.UnionFightMemberNode[i]
		if node.Uid == targetUid {
			retNode = node
			break
		}
	}
	if retNode == nil {
		for i := 0; i < len(unionFight.DefenceSide.UnionFightMemberNode); i++ {
			node := unionFight.DefenceSide.UnionFightMemberNode[i]
			if node.Uid == targetUid {
				retNode = node
				break
			}
		}
	}

	fightinfo := self.GetFightInfo(targetUid)

	return 2, utils.HF_JtoA(retNode), utils.Lz4Encode(fightinfo)
}

// DoLike ! 点赞
func (self *UnionActivityMgr) DoLike(uid int64, serverid int, unionid int, targetuid int64) (int, string, string) {
	unionBattle := self.GetUnionBattle(serverid)
	if unionBattle == nil {
		return 2, "STR_DATA_EXCEPTION", ""
	}

	unionFight := self.GetUnionFight(serverid, unionid)
	if unionFight == nil {
		return 2, "STR_DATA_EXCEPTION", ""
	}

	if unionBattle.Stage != core.UNIONACTIVITY_STAGE_INVALID {
		return 2, "STR_MGR_ATTACKLIKE_NO", ""
	}

	side := unionFight.GetUnionSide(unionid)
	rankInfo := unionFight.GetUnionRank(side)
	//看是否点赞过
	isLike := false
	for _, v := range rankInfo {
		for _, beLikeUid := range v.GetLike {
			if beLikeUid == uid {
				isLike = true
				break
			}
		}
	}

	if isLike {
		return 0, "STR_MGR_ATTACKLIKE", ""
	}

	//点赞
	isDoLike := false
	var relRank *UnionActivityRank
	for _, v := range rankInfo {
		if v.Uid == targetuid {
			v.GetLike = append(v.GetLike, uid)
			v.Like++
			isDoLike = true
			relRank = v
			break
		}
	}

	rewardConfig := GetResultAwardConfigMap(4, 0)
	var getItem []model.PassItem
	if rewardConfig == nil {
		return 2, "STR_CONFIGURATION_EXCEPTION", ""
	} else {
		//发放进攻失败奖励
		//getItem = player.AddObjectLst(rewardConfig.ResultAward, rewardConfig.AwardNum, "工会战进攻", 0, 0, 0)
		for i := 0; i < len(rewardConfig.AwardNum); i++ {
			getItem = append(getItem, model.PassItem{ItemID: rewardConfig.ResultAward[i], Num: rewardConfig.AwardNum[i]})
		}
	}

	if !isDoLike {
		return 2, "STR_MGR_ATTACKLIKE", ""
	}

	if relRank == nil {
		relRank = new(UnionActivityRank)
		relRank.Init()
	}

	return 0, utils.HF_JtoA(relRank), utils.HF_JtoA(getItem)
}

// GetFightInfo ! 获得战斗信息
func (self *UnionActivityMgr) GetFightInfo(uid int64) *model.JS_FightInfo {
	//self.Locker.RLock()
	//defer self.Locker.RUnlock()
	info, ok := self.getFightInfoCache(uid)
	if !ok {
		//return GetOfflineInfoMgr().GetFightInfo(uid)
	}
	return info.FightInfoCacheDB.GetFightInfo()
}

func (self *UnionActivityMgr) GetFightInfoOutLock(uid int64) *model.JS_FightInfo {
	info, ok := self.getFightInfoCache(uid)
	if !ok {
		//return GetOfflineInfoMgr().GetFightInfo(uid)
	}
	return info.FightInfoCacheDB.GetFightInfo()
}

func (self *UnionActivityMgr) UpdateFightInfoCache(fightInfo *model.JS_FightInfo) int64 {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	if fightInfo == nil {
		return 0
	}

	info, ok := self.getFightInfoCache(fightInfo.Uid)
	if !ok {
		data := new(crossserver.FightInfoCache)
		data.NeedSave = false
		data.LastLiveTime = model.TimeServer().Unix()
		data.FightInfoCacheDB = new(crossserver.FightInfoCacheDB)
		data.FightInfoCacheDB.Uid = fightInfo.Uid
		data.FightInfoCacheDB.SetFightInfo(fightInfo)
		data.FightInfoCacheDB.Encode()
		db.InsertTable(TABLE_NAME_UNIONACTIVITY_FIGHTINFO, data.FightInfoCacheDB, 0, false)
		data.FightInfoCacheDB.Init(TABLE_NAME_UNIONACTIVITY_FIGHTINFO, data.FightInfoCacheDB, false)
		self.UnionActivityFightInfo.Store(fightInfo.Uid, data)
		return 0
	}

	//oldFight := info.FightInfoCacheDB.fightInfo.Deffight
	info.FightInfoCacheDB.SetFightInfo(fightInfo)
	info.NeedSave = true

	return 0
}

func (self *UnionActivityMgr) AttackEnd(uid int64, targetuid int64, servid int, result, star int,
	attack *model.JS_FightInfo, battleInfo *model.BattleInfo, fightID int64) (int, string) {
	unionBattle := self.GetUnionBattle(servid)
	if unionBattle == nil {
		return 2, "STR_MGR_ATTACKEND_ERR"
	}

	unionid := self.GetUserUnionId(uid)
	targetunionid := self.GetUserUnionId(targetuid)
	if unionBattle.IsSameUnionFight(unionid, targetunionid) == false {
		return 0, ""
	}

	unionFight := unionBattle.GetUnionFight(unionid)
	if unionFight == nil || unionFight.AttackSide == nil || unionFight.DefenceSide == nil {
		//player.SendErr(fmt.Sprintf(csvs.GetText("STR_MGR_NO_NODE_INFO", lang), csvs.MOD_UNION_ATTACK_END_ERROR_CODE))
		return 0, "STR_MGR_ATTACKEND_ERR"
	}

	//判断自己的side
	//selfUnionMod := player.GetModule(models.MOD_UNION).(*ModUnion)
	//unionid := GetUnionMgr().GetUnionPlayer(player.GetUid())
	//union := GetUnionMgr().GetUnion(unionid)
	side := unionFight.GetUnionSide(unionid)
	//拿到对方数据
	targetNode, targetSide := unionFight.GetNode(targetuid)
	if targetNode == nil {
		return 2, "csvs.MOD_UNION_ATTACK_END_ERROR_CODE"
	}
	if side == targetSide {
		return 2, "csvs.MOD_UNION_ATTACK_END_ERROR_CODE"
	}
	targetNowStar := core.UNIONACTIVITY_STAR_MAX - targetNode.StarLose
	if star > targetNowStar {
		return 2, "STR_MGR_ATTACKEND_MAX_STAR"
	}

	config := GetGuildWarConfigMap(targetNode.Index)
	if config == nil {
		return 2, "STR_CONFIGURATION_EXCEPTION"
	}
	//selfFightInfo := GetRobotMgr().GetPlayerFightInfoByPos(player, 0, 0, models.TEAMTYPE_UNION_ACTIVITY_ATTACK)
	/*info, err := GetUnionActivityMgr().getFightInfoCache(uid)
	if err == false {
		return 2, ""
	}*/
	selfFightInfo := attack
	targetFightInfo := self.GetFightInfoOutLock(targetuid)
	if selfFightInfo == nil || targetFightInfo == nil {
		//player.SendErr(fmt.Sprintf(csvs.GetText("STR_MGR_NO_NODE_INFO", lang), csvs.MOD_UNION_ATTACK_END_ERROR_CODE))
		return 2, "STR_MGR_NO_NODE_INFO"
	}

	//msg.BattleInfo.Id = battle.GetFightMgr().GetFightInfoID()
	//msg.BattleInfo.Param = msg.Star
	//msg.BattleInfo.Time = models.TimeServer().Unix()
	//msg.BattleInfo.UserInfo[models.POS_ATTACK].Name = utils.HF_CheckStringForSea(selfFightInfo.Uname)
	//msg.BattleInfo.UserInfo[models.POS_DEFENCE].Name = utils.HF_CheckStringForSea(targetFightInfo.Uname)
	//msg.BattleInfo.UserInfo[models.POS_ATTACK].UnionName = utils.HF_CheckStringForSea(selfFightInfo.UnionName)
	//msg.BattleInfo.UserInfo[models.POS_DEFENCE].UnionName = utils.HF_CheckStringForSea(targetFightInfo.UnionName)
	//msg.BattleInfo.UserInfo[models.POS_ATTACK].UnionId = selfFightInfo.UnionId
	//msg.BattleInfo.UserInfo[models.POS_DEFENCE].UnionId = targetFightInfo.UnionId
	//targetNode.BattleInfo = append(targetNode.BattleInfo, msg.BattleInfo.Id)
	//unionFight.BattleInfo = append(unionFight.BattleInfo, msg.BattleInfo.Id)
	//db.HMSetRedisEx(core.BATTLE_INFO_UNION_ACTIVITY, msg.BattleInfo.Id, &msg.BattleInfo, core.DAY_SECS*4)

	battleRecord := model.BattleRecord{}
	battleRecord.Time = model.TimeServer().Unix()
	//battleRecord.Id = msg.BattleInfo.Id
	//battleRecord.LevelID = msg.BattleInfo.LevelID
	//battleRecord.Result = msg.BattleInfo.Result
	battleRecord.Type = core.BATTLE_TYPE_PVP
	//battleRecord.RandNum = msg.BattleInfo.Random
	battleRecord.FightInfo[0] = selfFightInfo
	battleRecord.FightInfo[1] = targetFightInfo

	//db.HMSetRedisEx(core.BATTLE_RECORD_UNION_ACTIVITY, battleRecord.Id, &battleRecord, core.DAY_SECS*4)

	getItem := make([]model.PassItem, 0)
	if result != model.ATTACK_WIN {
		targetNode.DefenceWinTimes++
		//攻击失败
		//if selfUnionMod.Sql_UserUnionInfo.ActivityAttackProtectTimes < models.UNIONACTIVITY_PROTECT_MAX {
		//	selfUnionMod.Sql_UserUnionInfo.ActivityAttackProtectTimes++
		//} else {
		//selfUnionMod.Sql_UserUnionInfo.ActivityAttackTimes++
		//selfUnionMod.Sql_UserUnionInfo.ActivityScore += config.PointDefeated
		rewardConfig := GetResultAwardConfigMap(3, 2)
		if rewardConfig != nil {
			//发放进攻失败奖励
			//getItem = player.AddObjectLst(rewardConfig.ResultAward, rewardConfig.AwardNum, "工会战进攻", 0, 0, 0)
		}
		rankData := new(UnionActivityRank)
		rankData.Uid = uid
		//rankData.Star = selfUnionMod.Sql_UserUnionInfo.ActivityStarWin
		//rankData.Score = selfUnionMod.Sql_UserUnionInfo.ActivityScore
		//rankData.Like = GetOfflineInfoMgr().GetPublicLike(player.GetUid())
		/*baseInfoSimple, ok := GetOfflineInfoMgr().GetUserBaseInfoSimple(rankData.Uid)
		if ok {
			rankData.BaseInfoSimple = baseInfoSimple
		}*/
		if side == model.POS_ATTACK {
			unionFight.AttackSide.UnionActivityRank = append(unionFight.AttackSide.UnionActivityRank, rankData)
			sort.Sort(unionFight.AttackSide.UnionActivityRank)
			for index, v := range unionFight.AttackSide.UnionActivityRank {
				v.RankPos = index + 1
			}
		} else {
			unionFight.DefenceSide.UnionActivityRank = append(unionFight.DefenceSide.UnionActivityRank, rankData)
			sort.Sort(unionFight.DefenceSide.UnionActivityRank)
			for index, v := range unionFight.DefenceSide.UnionActivityRank {
				v.RankPos = index + 1
			}
		}
		//}
		//player.HandleTask(core.TASK_TYPE_UNION_ACTIVITY, 0, 0, 0)
	} else {
		//攻击成功
		//selfUnionMod.Sql_UserUnionInfo.ActivityAttackTimes++
		//selfUnionMod.Sql_UserUnionInfo.ActivityStarWin += msg.Star
		//targetNode.StarLose += msg.Star
		//targetNode.StarLoseTime = models.TimeServer().Unix()
		//增加积分
		//selfUnionMod.Sql_UserUnionInfo.ActivityScore += config.Point[msg.Star-1]
		rewardConfig := GetResultAwardConfigMap(3, 1)
		if rewardConfig != nil {
			//发放进攻失败奖励
			//getItem = player.AddObjectLst(rewardConfig.ResultAward, rewardConfig.AwardNum, "工会战进攻", 0, 0, 0)
			for i := 0; i < len(rewardConfig.AwardNum); i++ {
				getItem = append(getItem, model.PassItem{ItemID: rewardConfig.ResultAward[i], Num: rewardConfig.AwardNum[i]})
			}
		}

		rankData := new(UnionActivityRank)
		rankData.Uid = uid
		//rankData.Star = selfUnionMod.Sql_UserUnionInfo.ActivityStarWin
		//rankData.Score = selfUnionMod.Sql_UserUnionInfo.ActivityScore
		//rankData.Like = GetOfflineInfoMgr().GetPublicLike(player.GetUid())
		/*baseInfoSimple, ok := GetOfflineInfoMgr().GetUserBaseInfoSimple(rankData.Uid)
		if ok {
			rankData.BaseInfoSimple = baseInfoSimple
		}*/
		if side == model.POS_ATTACK {
			unionFight.AttackSide.Star += star
			unionFight.AttackSide.UnionActivityRank = append(unionFight.AttackSide.UnionActivityRank, rankData)
			sort.Sort(unionFight.AttackSide.UnionActivityRank)
			for index, v := range unionFight.AttackSide.UnionActivityRank {
				v.RankPos = index + 1
			}
		} else {
			unionFight.DefenceSide.Star += star
			unionFight.DefenceSide.UnionActivityRank = append(unionFight.DefenceSide.UnionActivityRank, rankData)
			sort.Sort(unionFight.DefenceSide.UnionActivityRank)
			for index, v := range unionFight.DefenceSide.UnionActivityRank {
				v.RankPos = index + 1
			}
		}
		//player.HandleTask(core.TASK_TYPE_UNION_ACTIVITY, 1, 0, 0)
	}

	/*csv, ok := CommunityConfigMap[union.Level]
	if ok {
		//公会贡献
		if selfUnionMod.Sql_UserUnionInfo.ActivityAttackTimes == 1 {
			union.UpdateContriButionMember(player, csv.WarDedication)
			//selfUnionMod.Sql_UserUnionInfo.Contribution += csv.ChallengeDedication
			//selfUnionMod.Sql_UserUnionInfo.WeekContribution += csv.ChallengeDedication
		}
	}*/

	/*var msgRel protocol.S2C_UnionActivityAttackEnd
	msgRel.Cid = MSG_UNION_ACTIVITY_ATTACK_END
	msgRel.UnionFight = unionFight
	msgRel.AttackTimes = player.GetModule(models.MOD_UNION).(*ModUnion).Sql_UserUnionInfo.ActivityAttackTimes
	msgRel.AttackProtectTimes = player.GetModule(models.MOD_UNION).(*ModUnion).Sql_UserUnionInfo.ActivityAttackProtectTimes
	msgRel.StarWin = player.GetModule(models.MOD_UNION).(*ModUnion).Sql_UserUnionInfo.ActivityStarWin
	msgRel.Score = player.GetModule(models.MOD_UNION).(*ModUnion).Sql_UserUnionInfo.ActivityScore
	msgRel.GetItems = getItem
	msgRel.Result = msg.BattleInfo.Result
	player.SendMsg(msgRel.Cid, utils.HF_JtoB(&msgRel)) */
	return 0, ""
}

func (self *UnionActivityMgr) AttackKill(uid int64, targetuid int64, unionid int, servid int) (int, string) {
	group := crossserver.GetServerGroupMgr().GetGroupId(servid)
	if group == 0 {
		return 2, ""
	}

	unionBattle := self.GetUnionBattle(servid)
	if unionBattle == nil {
		return 2, ""
	}

	unionFight := unionBattle.GetUnionFight(unionid)
	if unionFight == nil || unionFight.AttackSide == nil || unionFight.DefenceSide == nil {
		return 2, ""
	}

	selfFightInfo := self.GetFightInfoOutLock(uid)
	targetFightInfo := self.GetFightInfoOutLock(targetuid)
	if selfFightInfo == nil || targetFightInfo == nil {
		//player.SendErr(fmt.Sprintf(csvs.GetText("STR_MGR_NO_NODE_INFO", lang), csvs.MOD_UNION_ATTACK_KILL_ERROR_CODE))
		//player.SendErr(csvs.GetText("找不到会战数据"))
		return 2, "STR_MGR_NO_NODE_INFO"
	}

	// 判断自己的side
	side := unionFight.GetUnionSide(unionid)
	//拿到对方数据
	targetNode, targetSide := unionFight.GetNode(targetuid)
	if targetNode == nil {
		return 2, ""
	}

	if side == targetSide {
		return 2, ""
	}

	targetNowStar := core.UNIONACTIVITY_STAR_MAX - targetNode.StarLose
	if targetNowStar > 0 {
		return 2, ""
	}

	config := GetGuildWarConfigMap(targetNode.Index)
	if config == nil {
		//player.SendErr(fmt.Sprintf(csvs.GetText("STR_CONFIGURATION_EXCEPTION", lang), csvs.MOD_UNION_GUILD_WAR_CONFIG))
		//player.SendErr(csvs.GetText("配置不存在"))
		return 2, "STR_CONFIGURATION_EXCEPTION"
	}

	rewardConfig := GetResultAwardConfigMap(3, 3)
	if rewardConfig == nil {
		//player.SendErr(fmt.Sprintf(csvs.GetText("STR_CONFIGURATION_EXCEPTION", lang), csvs.MOD_UNION_RESULT_AWARD_CONFIG))
		//player.SendErr(csvs.GetText("配置不存在"))
		return 2, "STR_CONFIGURATION_EXCEPTION"
	}

	/*if selfUnionMod.Sql_UserUnionInfo.ActivityAttackTimes >= core.UNIONACTIVITY_ATTACK_MAX {
		//player.SendErr(fmt.Sprintf(csvs.GetText("STR_MGR_ATTACKEND", lang), csvs.MOD_UNION_ATTACK_KILL_ERROR_CODE))
		//player.SendErr(csvs.GetText("已攻击过"))
		return
	}

	selfUnionMod.Sql_UserUnionInfo.ActivityAttackTimes++*/

	targetNode.BeFightLevel++

	battleInfo := new(model.BattleInfo)
	battleInfo.Time = model.TimeServer().Unix()
	battleInfo.Id = battle.GetFightMgr().GetFightInfoID()
	battleInfo.Result = model.ATTACK_WIN

	var attackHeroInfo []*model.BattleHeroInfo
	for i, v := range selfFightInfo.Heroinfo {
		level, star, skin, exclusiveLv := 0, 0, 0, 0
		if i < len(selfFightInfo.Heroinfo) {
			level = selfFightInfo.Heroinfo[i].Levels
			star = selfFightInfo.Heroinfo[i].Stars
			skin = selfFightInfo.Heroinfo[i].Skin
			exclusiveLv = selfFightInfo.Heroinfo[i].HeroExclusiveLv
		}
		attackHeroInfo = append(attackHeroInfo, &model.BattleHeroInfo{HeroID: v.Heroid, HeroLv: level, HeroStar: star, HeroSkin: skin, ExclusiveLv: exclusiveLv})
	}
	var defendHeroInfo []*model.BattleHeroInfo
	for i, v := range targetFightInfo.Heroinfo {
		level, star, skin, exclusiveLv := 0, 0, 0, 0
		if i < len(targetFightInfo.Heroinfo) {
			level = targetFightInfo.Heroinfo[i].Levels
			star = targetFightInfo.Heroinfo[i].Stars
			skin = targetFightInfo.Heroinfo[i].Skin
			exclusiveLv = targetFightInfo.Heroinfo[i].HeroExclusiveLv
		}
		defendHeroInfo = append(defendHeroInfo, &model.BattleHeroInfo{HeroID: v.Heroid, HeroLv: level, HeroStar: star, HeroSkin: skin, ExclusiveLv: exclusiveLv})
	}

	battleInfo.UserInfo[model.POS_ATTACK] = crossserver.NewBattleUserInfo(selfFightInfo, attackHeroInfo)
	battleInfo.UserInfo[model.POS_DEFENCE] = crossserver.NewBattleUserInfo(targetFightInfo, defendHeroInfo)
	battleInfo.Time = model.TimeServer().Unix()
	targetNode.BattleInfo = append(targetNode.BattleInfo, battleInfo.Id)
	unionFight.BattleInfo = append(unionFight.BattleInfo, battleInfo.Id)
	db.HMSetRedisEx(core.BATTLE_INFO_UNION_ACTIVITY, battleInfo.Id, &battleInfo, core.DAY_SECS*4)

	//selfUnionMod.Sql_UserUnionInfo.ActivityScore += config.PointCrash
	//getItem := player.AddObjectLst(rewardConfig.ResultAward, rewardConfig.AwardNum, "工会战进攻", 0, 0, 0)

	rankData := new(UnionActivityRank)
	rankData.Uid = uid
	//rankData.Star = selfUnionMod.Sql_UserUnionInfo.ActivityStarWin
	//rankData.Score = selfUnionMod.Sql_UserUnionInfo.ActivityScore
	//baseInfoSimple, ok := GetOfflineInfoMgr().GetUserBaseInfoSimple(rankData.Uid)
	//if ok {
	//	rankData.BaseInfoSimple = baseInfoSimple
	//}
	if side == model.POS_ATTACK {
		unionFight.AttackSide.UnionActivityRank = append(unionFight.AttackSide.UnionActivityRank, rankData)
		sort.Sort(unionFight.AttackSide.UnionActivityRank)
		for index, v := range unionFight.AttackSide.UnionActivityRank {
			v.RankPos = index + 1
		}
	} else {
		unionFight.DefenceSide.UnionActivityRank = append(unionFight.DefenceSide.UnionActivityRank, rankData)
		sort.Sort(unionFight.DefenceSide.UnionActivityRank)
		for index, v := range unionFight.DefenceSide.UnionActivityRank {
			v.RankPos = index + 1
		}
	}

	/*var msgRel protocol.S2C_UnionActivityAttackKill
	msgRel.Cid = MSG_UNION_ACTIVITY_ATTACK_KILL
	msgRel.UnionFight = unionFight
	msgRel.AttackTimes = player.GetModule(models.MOD_UNION).(*ModUnion).Sql_UserUnionInfo.ActivityAttackTimes
	msgRel.AttackProtectTimes = player.GetModule(models.MOD_UNION).(*ModUnion).Sql_UserUnionInfo.ActivityAttackProtectTimes
	msgRel.StarWin = player.GetModule(models.MOD_UNION).(*ModUnion).Sql_UserUnionInfo.ActivityStarWin
	msgRel.Score = player.GetModule(models.MOD_UNION).(*ModUnion).Sql_UserUnionInfo.ActivityScore
	msgRel.GetItems = getItem
	player.SendMsg(msgRel.Cid, utils.HF_JtoB(&msgRel))*/
	return 0, ""
}

func (self *UnionActivityMgr) AttackStart(uid int64, targetuid int64, serverid, star int) (int, string) {
	UnionBattle := self.GetUnionBattle(serverid)

	if UnionBattle == nil || UnionBattle.Stage != core.UNIONACTIVITY_STAGE_ATTACK {
		return 0, "STR_MOD_ACTIVITY_FUND_TIME_ERROR"
	}

	unionId := self.GetUserUnionId(uid)
	targetUnionId := self.GetUserUnionId(targetuid)
	if UnionBattle.IsSameUnionFight(unionId, targetUnionId) == false {
		return 0, "STR_CANT"
	}

	member1 := UnionBattle.GetUnionMember(unionId, uid)
	if member1 == nil {
		return 0, "STR_UNION_STATE"
	}

	member2 := UnionBattle.GetUnionMember(targetUnionId, targetuid)
	if member2 == nil {
		return 0, "STR_UNION_STATE"
	}

	unionFight := UnionBattle.GetUnionFight(unionId)

	//判断自己的side
	side := unionFight.GetUnionSide(unionId)
	if side == core.POS_INVALID {
		return 2, "STR_MGR_NO_NODE_INFO"
	}

	//拿到对方数据
	targetNode, targetSide := unionFight.GetNode(targetuid)
	if targetNode == nil {
		return 2, "STR_MGR_NO_NODE_INFO"
	}

	if side == targetSide {
		return 2, "STR_MGR_ATTACKEND_SELF"
	}

	targetNowStar := core.UNIONACTIVITY_STAR_MAX - targetNode.StarLose
	if star > targetNowStar {
		return 2, "STR_MGR_ATTACKEND_MAX_STAR"
	}

	enemyFightInfo := self.GetFightInfoOutLock(targetNode.Uid)
	/*var msgRel protocol.S2C_UnionActivityAttackStart
	msgRel.Cid = MSG_UNION_ACTIVITY_ATTACK_START
	msgRel.FightId = battle.GetFightMgr().GetFightInfoID()
	selfFightInfo := GetRobotMgr().GetPlayerFightInfoByPos(player, 0, 0, models.TEAMTYPE_UNION_ACTIVITY_ATTACK)
	enemyFightInfo := self.GetFightInfoOutLock(targetNode.Uid)
	_attack, _defence := models.ReBuildJSFightInfoByCondition(selfFightInfo, enemyFightInfo)
	msgRel.SelfFightInfo = _attack
	if nil == msgRel.SelfFightInfo {
		msgRel.SelfFightInfo = new(models.JS_FightInfo)
		msgRel.SelfFightInfo.Init()
	}

	msgRel.EnemyFightInfo = _defence
	if nil == msgRel.EnemyFightInfo {
		msgRel.EnemyFightInfo = new(models.JS_FightInfo)
		msgRel.EnemyFightInfo.Init()
	}
	player.SendMsg(msgRel.Cid, utils.HF_JtoB(&msgRel))*/
	return 1, utils.Lz4Encode(enemyFightInfo)
}

func (self *UnionActivityMgr) GM_GetNowInfo(w http.ResponseWriter) {

	w.Write([]byte(fmt.Sprintf("开启时间:%d\n", self.UnionActivityInfo.StartTime)))
	w.Write([]byte(fmt.Sprintf("进攻时间:%d\n", self.UnionActivityInfo.AttackTime)))
	w.Write([]byte(fmt.Sprintf("结束时间:%d\n", self.UnionActivityInfo.EndTime)))

	switch self.UnionActivityInfo.Stage {
	case core.UNIONACTIVITY_STAGE_INVALID:
		w.Write([]byte(fmt.Sprintf("当前阶段:%s\n", "未开启")))
	case core.UNIONACTIVITY_STAGE_WAIT:
		w.Write([]byte(fmt.Sprintf("当前阶段:%s\n", "备战")))
		self.UnionActivityInfo.UnionFight.Range(func(key, unionFight interface{}) bool {
			v := unionFight.(*UnionFight)
			w.Write([]byte(fmt.Sprintf("攻击方:%s,防守方:%s\n", v.AttackSide.UnionName, v.DefenceSide.UnionName)))
			return true
		})
	case core.UNIONACTIVITY_STAGE_ATTACK:
		w.Write([]byte(fmt.Sprintf("当前阶段:%s\n", "进攻")))
		self.UnionActivityInfo.UnionFight.Range(func(key, unionFight interface{}) bool {
			v := unionFight.(*UnionFight)
			w.Write([]byte(fmt.Sprintf("攻击方:%s,防守方:%s\n", v.AttackSide.UnionName, v.DefenceSide.UnionName)))
			return true
		})
	}
}

// MakeHistory 发送奖励
func (self *UnionActivityMgr) MakeHistory() {
	if self == nil {
		return
	}
	self.Locker.Lock()
	defer self.Locker.Unlock()

	self.UnionActivityInfo.Encode()

	self.UnionActivityInfoHistory = new(UnionActivityInfo)
	self.UnionActivityInfoHistory.Period = self.UnionActivityInfo.Period
	self.UnionActivityInfoHistory.Stage = self.UnionActivityInfo.Stage
	self.UnionActivityInfoHistory.StartTime = self.UnionActivityInfo.StartTime
	self.UnionActivityInfoHistory.AttackTime = self.UnionActivityInfo.AttackTime
	self.UnionActivityInfoHistory.EndTime = self.UnionActivityInfo.EndTime
	self.UnionActivityInfoHistory.UnionFight = self.UnionActivityInfo.UnionFight
	self.UnionActivityInfoHistory.UnionMap = self.UnionActivityInfo.UnionMap

	db.InsertTable(TABLE_NAME_UNIONACTIVITY_HISTORY, self.UnionActivityInfoHistory, 0, false)
}

func (self *UnionActivityMgr) GetUnionBattle(serverid int) *UnionActivityInfo {
	group := crossserver.GetServerGroupMgr().GetGroupId(serverid)
	if group != 0 {
		if battle, ok := self.MapBattleGroup.Load(group); ok {
			return battle.(*UnionActivityInfo)
		}
	}

	return nil
}

func (self *UnionActivityMgr) GetUnionFight(serverid, unionid int) *UnionFight {
	group := crossserver.GetServerGroupMgr().GetGroupId(serverid)
	if group != 0 {
		if battle, ok := self.MapBattleGroup.Load(group); ok {
			unionFight := battle.(*UnionActivityInfo).GetUnionFight(unionid)
			return unionFight
		}
	}

	return nil
}

func (self *UnionActivityMgr) GetUnionFightHistoryByUnionId(unionId int) *JS_UnionFight {
	if self.UnionActivityInfoHistory == nil ||
		self.UnionActivityInfoHistory.UnionMap == nil ||
		self.UnionActivityInfoHistory.UnionFight == nil {
		return nil
	}
	idData, ok := self.UnionActivityInfoHistory.UnionMap.Load(unionId)
	if !ok {
		return nil
	}
	unionFightData, ok := self.UnionActivityInfoHistory.UnionFight.Load(idData.(int))
	if !ok {
		return nil
	}
	jsUnionFight := GetJsUnionFight(unionFightData.(*UnionFight))
	return jsUnionFight
}

func (self *UnionActivityMgr) UpdateUnionFightSide(unionFightSide *UnionFightSide) {
	if unionFightSide == nil {
		return
	}
	unionNow := GetUnionMgr().GetUnion(unionFightSide.UnionId)
	if unionNow == nil {
		return
	}
	unionFightSide.UnionName = unionNow.Unionname
	unionFightSide.UnionIcon = unionNow.Icon
	unionFightSide.MasterName = unionNow.Mastername

	/*for _, node := range unionFightSide.UnionFightMemberNode {
		info := GetOfflineInfoMgr().GetUserBaseInfo(node.Uid)
		if info == nil {
			continue
		}
		baseInfoSimple, ok := GetOfflineInfoMgr().GetUserBaseInfoSimple(node.Uid)
		if ok {
			node.BaseInfoSimple = baseInfoSimple
		}
	}
	for _, v := range unionFightSide.UnionActivityRank {
		baseInfoSimple, ok := GetOfflineInfoMgr().GetUserBaseInfoSimple(v.Uid)
		if ok {
			v.BaseInfoSimple = baseInfoSimple
		}
	}*/

	return
}
