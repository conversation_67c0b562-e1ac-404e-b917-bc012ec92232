package model

import (
	"errors"
	"fmt"
	"master/db"
	"master/utils"
	"net/http"
	"time"
)

const (
	LOGIC_FALSE = 0
	LOGIC_TRUE  = 1
)

const (
	AttrHp    = 1
	AttrFight = 99
	AttrEnd   = 64

	AttrDisExt = 500 //属性百分比的偏移值

	AttrType = 100
	PER_BIT  = 10000 //万分比
)

const (
	MAX_FIGHT_POS  = 5
	ENCHANTING_MAX = 5
)

const (
	ITEM_RUNE_MAKEDROP_NUM = 3
)

var ServerTimeOffset int64 = 0

func TimeServer() time.Time {
	return time.Unix(time.Now().Unix()+ServerTimeOffset, 0)
}

func AddServerTime(w http.ResponseWriter, r *http.Request) {
	if utils.GetLogMgr().IsOnline() {
		w.Write([]byte(fmt.Sprintf("当前模式不允许修改时间\n")))
		w.Write([]byte(fmt.Sprintf("当前系统时间:%s,unixtime:%d", TimeServer().String(), TimeServer().Unix())))
		return
	}

	timeParam := r.FormValue("time")
	param := utils.HF_AtoI64(timeParam)

	if param >= 2000000000 {
		w.Write([]byte(fmt.Sprintf("参数错误")))
		return
	} else if param >= 1000000000 {
		ServerTimeOffset = param - time.Now().Unix()
	} else if param >= 0 {
		ServerTimeOffset += param
	}

	w.Write([]byte(fmt.Sprintf("当前系统时间:%s,unixtime:%d", TimeServer().String(), TimeServer().Unix())))
}

const (
	POS_ATTACK  = 0
	POS_DEFENCE = 1

	ATTACK_WIN  = 1
	DEFENCE_WIN = 2
)

type JS_LifeTreeInfo struct {
	MainLevel int            `json:"mainlevel"`
	Info      []*JS_LifeTree `json:"info"`
}

type JS_LifeTree struct {
	Type  int `json:"type"`  // 类型
	Level int `json:"level"` // 等级
}

// 竞技场战报
type ArenaFight struct {
	FightId    int64  `json:"fight_id"`      // 战斗Id
	Side       int    `json:"side"`          // 1 进攻方 0 防守方
	Result     int    `json:"attack_result"` // 0 进攻方成功 其他防守方胜利
	Point      int    `json:"point"`         // 积分增减
	Uid        int64  `json:"uid"`           // Uid
	IconId     int    `json:"icon_id"`       // 头像Id
	Name       string `json:"name"`          // 名字
	Level      int    `json:"level"`         // 等级
	Fight      int64  `json:"fight"`         // 战力
	Time       int64  `json:"time"`          // 发生的时间
	Portrait   int    `json:"portrait"`      //! 头像框
	Subsection int    `json:"subsection"`    //! 大段位
	Class      int    `json:"class"`         //! 小段位
}

type JS_FightInfo struct {
	Rankid       int              `json:"rankid"`  // 类型
	Uid          int64            `json:"uid"`     // id
	Uname        string           `json:"uname"`   // 名字
	UnionName    string           `json:"union"`   //! 军团名字
	UnionId      int              `json:"unionid"` //! 军团id
	Iconid       int              `json:"iconid"`  // icon
	Title        int              `json:"title"`
	Camp         int              `json:"camp"`         // 阵营
	Level        int              `json:"level"`        // 等级
	Vip          int              `json:"vip"`          // Vip 等级
	Defhero      []int            `json:"defhero"`      // 出战武将
	Heroinfo     []JS_HeroInfo    `json:"heroinfo"`     // 各武将信息
	HeroParam    []JS_HeroParam   `json:"heroparam"`    // 各武将属性
	Deffight     int64            `json:"deffight"`     // 玩家总战力
	FightTeam    int              `json:"fightteam"`    // 出战兵营
	FightTeamPos TeamPos          `json:"fightteampos"` // 出战兵营
	Portrait     int              `json:"portrait"`     // 边框  20190412 by zy
	LifeTreeInfo *JS_LifeTreeInfo `json:"lifetreeinfo"`
	Server       int              `json:"server"`      //
	Score        int              `json:"score"`       //
	Param        int64            `json:"param"`       //
	WawShipInfo  []*JS_WarShip    `json:"wawshipinfo"` //战船
	FameId       int              `json:"fameid"`      //名望
}

type TeamPos struct {
	FightPos  [MAX_FIGHT_POS]int `json:"fightpos"`
	HydraId   int                `json:"hydraid"`
	WarShipId int                `json:"warshipid"`
}

func (self *TeamPos) AddFightPos(heroId int) error {
	//巨兽先屏蔽
	if heroId > 5000 {
		return nil
	}
	index := self.getEmptyFightPos()
	if index != -1 {
		self.FightPos[index] = heroId
	} else {
		return errors.New("no empty pos")
	}
	return nil
}

func (self *TeamPos) getEmptyFightPos() int {

	for i := 0; i < MAX_FIGHT_POS; i++ {
		if self.FightPos[i] == 0 {
			return i
		}
	}
	return -1
}

type JS_HeroParam struct {
	Heroid  int              `json:"heroid"` // 武将id
	Param   []float64        `json:"param"`  // 武将属性
	Hp      float64          `json:"hp"`     // 当前hp
	Energy  int              `json:"energy"` // 当前怒气
	Pos     int              `json:"pos"`
	ExtAttr []JS_HeroExtAttr `json:"ext"` //扩展属性
}

type JS_HeroExtAttr struct {
	Key   int     `json:"k"` // 属性类型
	Value float64 `json:"v"` // 属性值
}

type JS_HeroInfo struct {
	Heroid          int              `json:"heroid"`    // 武将id
	HeroKeyId       int              `json:"herokeyid"` // 武将keyid
	Color           int              `json:"color"`     //
	Stars           int              `json:"stars"`
	Levels          int              `json:"levels"`
	Soldiercolor    int              `json:"soldiercolor"` // 士兵星级
	Soldierid       int              `json:"soldierid"`    // 士兵id
	Skilllevel1     int              `json:"skilllevel1"`  // 技能等级
	Skilllevel2     int              `json:"skilllevel2"`
	Skilllevel3     int              `json:"skilllevel3"`
	Skilllevel4     int              `json:"skilllevel4"`
	Skilllevel5     int              `json:"skilllevel5"`
	Skilllevel6     int              `json:"skilllevel6"`
	Fervor1         int              `json:"fervor1"` // 战魂
	Fervor2         int              `json:"fervor2"`
	Fervor3         int              `json:"fervor3"`
	Fervor4         int              `json:"fervor4"`
	Fight           int64            `json:"fight"` // 战斗力
	ArmsSkill       []JS_ArmsSkill   `json:"armsskill"`
	FightSkill      *JS_FightSkill   `json:"fightskill"`  //传入战斗计算的技能
	TalentSkill     []Js_TalentSkill `json:"talentskill"` // 天赋技能
	Buff            []int            `json:"buff"`        // buff
	ArmyId          int              `json:"army_id"`
	MainTalent      int              `json:"maintalent"`      // 英雄主天赋等级
	HeroQuality     int              `json:"heroquality"`     //英雄品质  (不变的属性)
	HeroArtifactId  int              `json:"heroartifactid"`  //英雄神器等级 (不变的属性)
	HeroArtifactLv  int              `json:"heroartifactlv"`  //英雄神器等级 (不变的属性)
	HeroExclusiveLv int              `json:"heroexclusivelv"` //英雄专属等级 (不变的属性)
	Skin            int              `json:"skin"`            //皮肤
	ExclusiveUnLock int              `json:"exclusiveunlock"` //专属等级是否解锁
	IsArmy          int              `json:"isarmy"`          //是否是佣兵
	EquipIds        []*Equip         `json:"equipIds"`
	Talisman        []*Talisman      `json:"talisman"` // 护符
	Rune            *Js_Rune         `json:"rune"`
	IsResonance     int              `json:"isresonance"` //是否共鸣英雄    1是
	TrinketId       *Js_TrinketInfo  `json:"trinketid"`   //饰品
	Weapons         *JS_Weapons      `json:"weapons"`     //乌索普武器（宠物）
	Vehicle         *JS_Vehicle      `json:"vehicle"`     //弗兰奇科技（战灵）
	CapacityLevel   int              `json:"capacitylevel"`
	Race            int              `json:"race"`
	Element         int              `json:"element"`
	Surround        int              `json:"surround"`
	Modicon         int              `json:"modicon"`
	HeroIcon        int              `json:"hero_icon"`     // 英雄头像
	HeroPortrait    int              `json:"hero_portrait"` // 英雄头像框
	HeroTitle       int              `json:"hero_title"`    // 英雄称号
}

func (self *JS_HeroInfo) CountFight(ewvaluetype []int, ewvalue []float64) ([]float64, []JS_HeroExtAttr, int) {
	att, att_ext, energy := self.GetAtt(ewvaluetype, ewvalue)

	for i := 0; i < len(att_ext); i++ {
		if att_ext[i].Key == AttrFight {
			self.Fight = int64(att_ext[i].Value)
			break
		}
	}
	return att, att_ext, energy
}

func (self *JS_HeroInfo) ProcAtt(valuetype int, value float64, att []float64, att_per map[int]float64, att_ext map[int]float64) int {

	if valuetype < 0 || value == 0 {
		return 0
	}

	if valuetype == 2500 {
		return int(value)
	} else if valuetype >= 100 {
		att_per[valuetype] += value
	} else if valuetype >= len(att) {
		att_ext[valuetype] += value
	} else {
		att[valuetype] += value
	}

	return 0
}

func (self *JS_HeroInfo) GetAtt(ewvaluetype []int, ewvalue []float64) ([]float64, []JS_HeroExtAttr, int) {
	energy := 0
	att := make([]float64, 32)
	att_per := make(map[int]float64)
	att_ext := make(map[int]float64)

	// 额外属性加成
	for i := 0; i < len(ewvaluetype); i++ {
		if ewvaluetype[i] < 0 {
			continue
		}
		energy += self.ProcAtt(ewvaluetype[i], ewvalue[i], att, att_per, att_ext)
	}

	for i := 0; i < AttrEnd; i++ {
		value, ok := att_per[i+AttrDisExt+1]
		if !ok {
			continue
		}
		att[i] = att[i] * (1.0 + value/10000.0)
	}

	att_ext_ret := []JS_HeroExtAttr{}
	for k := range att_ext {
		value, ok := att_per[k+AttrDisExt]
		if ok {
			att_ext[k] += att_ext[k] * (1.0 + value/10000.0)
		}

		att_ext_ret = append(att_ext_ret, JS_HeroExtAttr{k, att_ext[k]})
	}

	return att, att_ext_ret, energy
}

type Js_Rune struct {
	Id        int                              `json:"id"` //! 装备配置Id
	MintIs    int                              `json:"mint_is"`
	HeroKeyId int                              `json:"herokeyid"` //! 装备拥有者
	MintClass int                              `json:"mintclass"` //符石阶级
	MintLevel int                              `json:"mintlevel"` //符石等级
	MintFront [ITEM_RUNE_MAKEDROP_NUM]MarkDrop `json:"mintfront"` //解印前
	MintAfter [ITEM_RUNE_MAKEDROP_NUM]MarkDrop `json:"mintafter"` //解印后(随机)
	MayNum    int                              `json:"maynum"`    //解印次数（计算保底）
	Resonance *Rune                            `json:"resonance"` // 共鸣对象
}

type MarkDrop struct {
	DId          int `json:"did"`          //对应表id
	Attribute    int `json:"attribute"`    //属性类型
	AttributeNum int `json:"attributenum"` //属性值
	MintScore    int `json:"mintscore"`    //评分值
	//IsLock       int `json:"islock"`       //1锁定 0取消锁定
}

type Rune struct {
	Id        int                              `json:"id"` //! 装备配置Id
	MintIs    int                              `json:"mint_is"`
	HeroKeyId int                              `json:"herokeyid"` //! 装备拥有者
	MintClass int                              `json:"mintclass"` //符石阶级
	MintLevel int                              `json:"mintlevel"` //符石等级
	MintFront [ITEM_RUNE_MAKEDROP_NUM]MarkDrop `json:"mintfront"` //解印前
	MintAfter [ITEM_RUNE_MAKEDROP_NUM]MarkDrop `json:"mintafter"` //解印后(随机)
	MayNum    int                              `json:"maynum"`    //解印次数（计算保底）
	Resonance int                              `json:"resonance"` // 共鸣对象
}

type Equip struct {
	KeyId     int `json:"keyid"`     //! 装备唯一Id
	Id        int `json:"id"`        //! 装备配置Id
	HeroKeyId int `json:"herokeyid"` //! 装备拥有者
	ForgeExp  int `json:"forgeexp"`  //锻造经验

	Exp         int               `json:"exp"`         //强化经验
	Lv          int               `json:"lv"`          //强化装备等级
	RefineLv    int               `json:"refinelv"`    //精炼等级
	CastLv      int               `json:"castlv"`      //铸魂等级
	Benediction map[int]*Blessing `json:"benediction"` //装备祝福

	Recast int `json:"recast"` //!(废弃)
	//AttrInfo  []*AttrInfo `json:"attrinfo"`  //! 属性信息
	IsUpGrade int `json:"isupgrade"` //! 是否升阶(废弃)
}

// Talisman 服务器存放的装备信息
type Talisman struct {
	KeyId        int   `json:"keyid"`        //! 装备唯一Id
	Id           int   `json:"id"`           //! 装备配置Id
	HeroKeyId    int   `json:"herokeyid"`    //! 装备拥有者
	Skills       []int `json:"skills"`       //! 技能
	RandemSkills []int `json:"randemskills"` //! 随机技能
	IsLock       int   `json:"islock"`       // 上锁状态
	//AttrInfo  []*AttrInfo `json:"attrinfo"`  //! 属性信息
	/*IsUpGrade int `json:"isupgrade"` //! 是否升阶(废弃)
	Lv        int `json:"lv"`        //!
	Exp       int `json:"exp"`       //!
	Recast    int `json:"recast"`    //!*/
	Star    int   `json:"star"`    //星级   拿到入场技能
	AttrId  []int `json:"attrid"`  // 初源属性ID
	SkillId []int `json:"skillid"` //组合技能
}
type Blessing struct {
	BenedictionExp      int   `json:"benedictionexp"`      //祝福经验
	BenedictionLv       int   `json:"benedictionlv"`       //祝福等级
	BenedictionState    int   `json:"benedictionstate"`    //祝福效果 10001
	RandomBlessingState []int `json:"randomblessingstate"` //随机祝福效果  最大6
}

type JS_ArmsSkill struct {
	Id    int `json:"id"`    // 兵法Id
	Level int `json:"level"` // 等级
}

// JS_FightSkill 传入战斗的技能
type JS_FightSkill struct {
	MainSkills    []int `json:"mainskills"`    // 大招
	ActiveSkills  []int `json:"activeskills"`  // 主动技能
	PassiveSkills []int `json:"passiveskills"` // 被动技能
}

type Js_TalentSkill struct {
	SkillId int `json:"skillid"` // 技能Id
	Level   int `json:"level"`   // 技能等级
}

type BattleInfo struct {
	Id         int64              `json:"id"`         //! 战报ID
	LevelID    int                `json:"levelid"`    //! 关卡id
	Type       int                `json:"type"`       //! 战报类型
	PlayType   int                `json:"playtype"`   //! 玩法类型
	Time       int64              `json:"time"`       // 发生的时间
	Result     int                `json:"result"`     // 结果
	Random     int64              `json:"random"`     // 随机数
	UserInfo   [2]*BattleUserInfo `json:"userinfo"`   // 玩家数据
	Weaken     []*WeakenInfo      `json:"weaken"`     // 压制
	Param      int                `json:"param"`      // 参数
	ResultType int                `json:"resulttype"` // 结果
	StageParam int                `json:"stageparam"`
}

func (self *BattleInfo) Init() {
	self.UserInfo = [2]*BattleUserInfo{}
	self.UserInfo[0] = new(BattleUserInfo)
	self.UserInfo[0].HeroInfo = make([]*BattleHeroInfo, 0)
	self.UserInfo[1] = new(BattleUserInfo)
	self.UserInfo[1].HeroInfo = make([]*BattleHeroInfo, 0)
	self.Weaken = make([]*WeakenInfo, 0)
}

type BattleUserInfo struct {
	Uid       int64             `json:"uid"`      //! 名字
	Name      string            `json:"name"`     //! 名字
	Icon      int               `json:"icon"`     //! 头像
	Portrait  int               `json:"portrait"` // 头像框
	Title     int               `json:"title"`    // 称号
	Fight     int64             `json:"fight"`    //
	ServerId  int               `json:"serverid"` // 服务器ID
	UnionId   int               `json:"unionid"`
	UnionName string            `json:"union_name"` //! 军团名字
	Level     int               `json:"level"`      //! 等级
	HeroInfo  []*BattleHeroInfo `json:"heroinfo"`   // 双方数据
	Score     int               `json:"score"`      //!
	Param1    int               `json:"param1"`     //!
}

type BattleHeroInfo struct {
	HeroID      int       `json:"heroid"`     //! 英雄id
	HeroLv      int       `json:"herolv"`     //! 英雄等级
	HeroStar    int       `json:"herostar"`   //! 英雄星级
	HeroSkin    int       `json:"skin"`       //! 英雄皮肤
	Hp          int       `json:"hp"`         // hp
	Energy      int       `json:"rage"`       // 怒气
	Damage      int64     `json:"damage"`     //! 伤害
	TakeDamage  int64     `json:"takedamage"` //! 承受伤害
	Healing     int64     `json:"healing"`    //! 治疗
	ArmyInfo    *ArmyInfo `json:"ownplayer"`
	ExclusiveLv int       `json:"exclusivelv"` //! 专属等级
	UseSkill    []int     `json:"skilltime"`   //! 使用的技能 pve专用  竞技场不使用
}

type ArmyInfo struct {
	Uid      int64  `json:"uid"`      //! uid
	Uname    string `json:"uname"`    //! 名字
	Iconid   int    `json:"iconid"`   //! 头像
	SelfKey  int    `json:"selfkey"`  //! 张三
	Face     int    `json:"face"`     //! 李四
	Portrait int    `json:"portrait"` //! 头像框
	Pos      int    `json:"pos"`      //! 队伍位置
}

type WeakenInfo struct {
	Att   int     `json:"att"`   //!
	Value float64 `json:"value"` //!
}

type UserShowInfo struct {
	Uid       int64  `json:"uid"`        //! 名字
	Name      string `json:"name"`       //! 名字
	Icon      int    `json:"icon"`       //! 头像
	Portrait  int    `json:"portrait"`   //! 头像框
	UnionId   int    `json:"unionid"`    //! 军团ID
	UnionName string `json:"union_name"` //! 军团名字
	Level     int    `json:"level"`      //! 等级
	Camp      int    `json:"camp"`       //! 阵营
	Fight     int64  `json:"fight"`      //! 战力
}

type JS_CrossArenaBattleInfo struct {
	Id           int    `json:"id"`           //! 自增Id
	FightId      int64  `json:"fightid"`      //! 战斗Id
	RecordType   int    `json:"recordtype"`   //! 战报类型
	BattleInfo   string `json:"battleinfo"`   //! 简报
	BattleRecord string `json:"battlerecord"` //! 详细战报
	UpdateTime   int64  `json:"updatetime"`   //! 插入时间

	db.DataUpdate
}

// ! 战报
type BattleRecord struct {
	Id        int64            `json:"id"`            //! 战报ID
	Type      int              `json:"type"`          //! 战报类型
	PlayType  int              `json:"playtype"`      //! 玩法类型
	Side      int              `json:"side"`          // 自己是 1 进攻方 0 防守方
	Result    int              `json:"attack_result"` // 0成功 其他失败
	Level     int              `json:"level"`         // 之前名次
	LevelID   int              `json:"levelid"`       //! 关卡id
	Time      int64            `json:"time"`          // 发生的时间
	RandNum   int64            `json:"rand_num"`      // 随机数
	Weaken    []*WeakenInfo    `json:"weaken"`        // 压制
	FightInfo [2]*JS_FightInfo `json:"fight_info"`    // 双方数据 一个是进攻 第二个是防守
}

// 天赋组
type StageTalent struct {
	Group    int                 `json:"group"`    //! 组别
	AllSkill []*StageTalentIndex `json:"allskill"` // 全部技能
}

// 单条的每一层
type StageTalentIndex struct {
	ID    int `json:"id"`    //! id
	Index int `json:"index"` //! 层
	Pos   int `json:"pos"`   //! 附带的选择技能
}

type PassItem struct {
	ItemID int `json:"itemid"` // 道具ID
	Num    int `json:"num"`    // 道具数量
}

type NoticeInfo struct {
	ServerId int        `json:"serverid"` //! 服务器id
	LevelID  int        `json:"levelid"`  //! 关卡id
	Name     string     `json:"name"`     //! 名字
	RankId   int        `json:"rankid"`   //! 玩法类型
	Time     int64      `json:"time"`     // 发生的时间
	GetItem  []PassItem `json:"getitem"`
}

// 竞技场战报
type CrossServerArenaFight struct {
	FightId    int64         `json:"fight_id"`      // 战斗Id
	Side       int           `json:"side"`          // 0 进攻方 1 防守方
	Result     int           `json:"attack_result"` // 1 进攻方成功 其他防守方胜利
	Point      int           `json:"point"`         // 积分增减
	Uid        int64         `json:"uid"`           // Uid
	IconId     int           `json:"icon_id"`       // 头像Id
	Portrait   int           `json:"portrait"`      //! 头像框
	Title      int           `json:"title"`         //! 称号
	Name       string        `json:"name"`          // 名字
	Level      int           `json:"level"`         // 等级
	Fight      int64         `json:"fight"`         // 战力
	Time       int64         `json:"time"`          // 发生的时间
	BattleInfo []*BattleInfo `json:"battleinfo"`
}

type PeakArenaFight struct {
	FightId    int64         `json:"fight_id"`      // 战斗Id
	Side       int           `json:"side"`          // 0 进攻方 1 防守方
	Result     int           `json:"attack_result"` // 1 进攻方成功 其他防守方胜利
	Point      int           `json:"point"`         // 积分增减
	Uid        int64         `json:"uid"`           // Uid
	IconId     int           `json:"icon_id"`       // 头像Id
	Portrait   int           `json:"portrait"`      //! 头像框
	Title      int           `json:"title"`         //! 称号
	Name       string        `json:"name"`          // 名字
	Level      int           `json:"level"`         // 等级
	Fight      int64         `json:"fight"`         // 战力
	Time       int64         `json:"time"`          // 发生的时间
	BattleInfo []*BattleInfo `json:"battleinfo"`
}

type RankInfo struct {
	Uid       int64  `json:"uid"`
	RankId    int    `json:"rankid"`
	Period    int    `json:"period"`
	SvrId     int    `json:"svrid"`
	SvrName   string `json:"svrname"`
	UName     string `json:"uname"`
	Level     int    `json:"level"`
	Vip       int    `json:"vip"`
	Icon      int    `json:"icon"`
	Portrait  int    `json:"portrait"`
	Title     int    `json:"title"`
	Fight     int64  `json:"fight"`
	Camp      int    `json:"camp"`
	Num       int64  `json:"num"`        //! 排行榜数值 1
	UnionName string `json:"union_name"` //! 军团名字
	Rank      int    `json:"rank"`       //!
	Time      int64  `json:"time"`       //3
	Param     int64  `json:"param"`      //附加数值  2
	LeftTime  int64  `json:"left_time"`  //新增剩余时间
	LevelId   int    `json:"levelid"`    //
	BeLike    int    `json:"belike"`     //! 被点赞
	HeroId    int    `json:"heroid"`     //! 给客户端显示半身像
	FameId    int    `json:"fameid"`     //! 名望
}

type HttpServerInfo struct {
	ServerId         int
	EventLen         int
	LastGetEventTime int64 //! 最后拿数据时间
	LastConnectTime  int64 //! 最后连接时间
}

// 竞技场战报
type CrossServerLevelArenaFight struct {
	FightId    int64       `json:"fight_id"`      // 战斗Id
	Side       int         `json:"side"`          // 0 进攻方 1 防守方
	Result     int         `json:"attack_result"` // 1 进攻方成功 其他防守方胜利
	Point      int         `json:"point"`         // 积分增减
	Uid        int64       `json:"uid"`           // Uid
	IconId     int         `json:"icon_id"`       // 头像Id
	Portrait   int         `json:"portrait"`      //! 头像框
	Title      int         `json:"title"`         //! 称号
	Name       string      `json:"name"`          // 名字
	Level      int         `json:"level"`         // 等级
	Fight      int64       `json:"fight"`         // 战力
	Time       int64       `json:"time"`          // 发生的时间
	BattleInfo *BattleInfo `json:"battleinfo"`
}

// 竞技场战报
type CosmicArenaFight struct {
	FightId    int64       `json:"fight_id"`      // 战斗Id
	Side       int         `json:"side"`          // 0 进攻方 1 防守方
	Result     int         `json:"attack_result"` // 1 进攻方成功 其他防守方胜利
	Point      int         `json:"point"`         // 积分增减
	Uid        int64       `json:"uid"`           // Uid
	IconId     int         `json:"icon_id"`       // 头像Id
	Portrait   int         `json:"portrait"`      //! 头像框
	Title      int         `json:"title"`         //! 称号
	Name       string      `json:"name"`          // 名字
	Level      int         `json:"level"`         // 等级
	Fight      int64       `json:"fight"`         // 战力
	Time       int64       `json:"time"`          // 发生的时间
	BattleInfo *BattleInfo `json:"battleinfo"`
}

// 战报信息
type FightResult struct {
	Id           int64            // 战斗id
	Result       int              // 结果[0无结果 1攻击方胜利 2防守方胜利]
	Fight        [2]*JS_FightInfo // 交战双方数据
	Info         [2][]FightHero   // 战斗结果数据
	Random       int              // 随机数
	Time         int64            // 交战时间
	ResultDetail *FightResultNode // 战斗结果详情
	CityId       int              // 战斗发起城池/或者矿点Id
	SecKill      int              // 是否秒杀, 0 不是秒杀 1是秒杀 [战力悬殊过大，直接秒杀]
	TeamA        int              // 军团战队伍1
	TeamB        int              // 军团战队伍2
	Group        int              // 分组
	IsSet        bool             // 是否通过SET拿到的战报结果
	FightType    int              // 玩法类型
	LevelID      int              // 关卡id
}

// 战斗武将信息
type FightHero struct {
	Heroid     int   `json:"id"`         // 武将id
	Hp         int   `json:"hp"`         // hp
	Energy     int   `json:"rage"`       // 怒气
	Damage     int64 `json:"damage"`     //! 伤害
	TakeDamage int64 `json:"takedamage"` //! 承受伤害
	Healing    int64 `json:"healing"`    //! 治疗
}

type FightResultNode struct {
	Fightid int64          `json:"fightid"`
	Info    [2][]FightHero `json:"info"`
	Time    int            `json:"time"`
	Winner  int            `json:"winner"`
	Record  []byte         `json:"record"`
}

type FightReplay struct {
	FightType int    `json:"fighttype"`
	Record    []byte `json:"record"`
	LevelID   int64  `json:"levelid"`
}

type ArenaFightList struct {
	Type    int           `json:"type"`
	FightId int64         `json:"fight_id"` // 战斗Id
	Random  int64         `json:"random"`
	Time    int64         `json:"time"`   // 发生的时间
	Attack  *JS_FightInfo `json:"attack"` // 攻击者
	Defend  *JS_FightInfo `json:"defend"` // 防御者
	BossId  int           `json:"bossid"` //
}

type JS_WarShip struct {
	Id               int            `json:"id"`
	Level            int            `json:"level"`
	MaxLv            int            `json:"maxlv"`
	Fight            int64          `json:"fight"`
	Skin             int            `json:"skin"`
	SkinStar         int            `json:"skinstar"`
	AllSkill         []int          `json:"allskill"`         //全局技能
	SkillSlot        []int          `json:"skill"`            //常规技能槽
	UnitSlot         []int          `json:"unitslot"`         //装载槽
	Param            []float64      `json:"param"`            //自己属性
	AllParam         []float64      `json:"allparam"`         //全局属性
	SkinPassiveSkill []int          `json:"skinpassiveskill"` //皮肤被动技能
	FightSkill       *JS_FightSkill `json:"fightskill"`       //传入战斗计算的技能
}

// ---------------------------------------
type CityBrokenRecordInfo struct {
	FightId int64           `json:"fightid"`
	Rand    int64           `json:"rand"`
	Result  int             `json:"result"`
	Time    int64           `json:"time"`
	Info    []*JS_FightInfo `json:"info"`
}

type CityBrokenSimpleRecord struct {
	FightId        int64  `json:"fightid"`
	Result         int    `json:"result"`
	EnemyName      string `json:"enemyname"`
	EnemyLevel     int    `json:"enemylevel"`
	EnemyIcon      int    `json:"enemyicon"`
	EnemyPortrait  int    `json:"enemyportrait"`
	EnemyUnionName string `json:"enemyunionname"`
	EnemyFight     int64  `json:"enemyfight"`
	EnemyVip       int    `json:"enemyvip"`
	FightTime      int64  `json:"time"`
	IsAttack       int    `json:"isattack"`
}

func GetFightHero(fightInfo *JS_FightInfo) []FightHero {
	rel := make([]FightHero, 0)
	if fightInfo == nil {
		return rel
	}
	for _, v := range fightInfo.Heroinfo {
		data := new(FightHero)
		data.Heroid = v.Heroid
		rel = append(rel, *data)
	}
	return rel
}
func (self *JS_FightInfo) Init() {
	self.Defhero = []int{}
	self.Heroinfo = make([]JS_HeroInfo, 0)
	self.HeroParam = make([]JS_HeroParam, 0)
	self.FightTeamPos = TeamPos{}
	self.LifeTreeInfo = new(JS_LifeTreeInfo)
	self.LifeTreeInfo.Init()
	self.WawShipInfo = make([]*JS_WarShip, 0)
}
func (self *JS_LifeTreeInfo) Init() {
	self.Info = make([]*JS_LifeTree, 0)
}
func (self *JS_FightInfo) Check() {
	if self.Defhero == nil {
		self.Defhero = []int{}
	}
	if self.Heroinfo == nil {
		self.Heroinfo = make([]JS_HeroInfo, 0)
	} else {
		for i, _ := range self.Heroinfo {
			self.Heroinfo[i].Check()
		}
	}

	if self.HeroParam == nil {
		self.HeroParam = make([]JS_HeroParam, 0)
	}
	if self.LifeTreeInfo == nil {
		self.LifeTreeInfo = new(JS_LifeTreeInfo)
		self.LifeTreeInfo.Init()
	}
	if self.WawShipInfo == nil {
		self.WawShipInfo = make([]*JS_WarShip, 0)
	}
}
func (self *JS_HeroInfo) Check() {
	if self.ArmsSkill == nil {
		self.ArmsSkill = make([]JS_ArmsSkill, 0)
	}

	if self.FightSkill == nil {
		self.FightSkill = new(JS_FightSkill)
	}
	self.FightSkill.Check()

	if self.TalentSkill == nil {
		self.TalentSkill = make([]Js_TalentSkill, 0)
	}

	if self.EquipIds == nil {
		self.EquipIds = make([]*Equip, 0)
	} else {
		for _, value := range self.EquipIds {
			value.Check()
		}
	}

	if self.Talisman == nil {
		self.Talisman = make([]*Talisman, 0)
	} else {
		for _, value := range self.Talisman {
			value.Check()
		}
	}

	if self.Rune == nil {
		self.Rune = new(Js_Rune)
		self.Rune.Init()
	}

	if self.TrinketId == nil {
		self.TrinketId = new(Js_TrinketInfo)
	}

	if self.Weapons == nil {
		self.Weapons = new(JS_Weapons)
	}

	if self.Vehicle == nil {
		self.Vehicle = new(JS_Vehicle)
	}

	//if self.DestinyInfo == nil {
	//	self.DestinyInfo = make([]*DestinyInfo, 0)
	//}
}
func (self *JS_FightSkill) Check() {
	if self.MainSkills == nil {
		self.MainSkills = make([]int, 0)
	}
	if self.ActiveSkills == nil {
		self.ActiveSkills = make([]int, 0)
	}
	if self.PassiveSkills == nil {
		self.PassiveSkills = make([]int, 0)
	}
}
func (self *Equip) Check() {
	if self.Benediction == nil {
		self.Benediction = make(map[int]*Blessing)
	}
}
func (self *Talisman) Check() {
	if self.Skills == nil {
		self.Skills = make([]int, 0)
	}
	if self.RandemSkills == nil {
		self.RandemSkills = make([]int, 0)
	}
	if self.AttrId == nil {
		self.AttrId = make([]int, 0)
	}
	if self.SkillId == nil {
		self.SkillId = make([]int, 0)
	}
}
func (self *Js_Rune) Init() {
	self.Resonance = new(Rune)
}

type Js_TrinketInfo struct {
	Id         int                 `json:"id"`         //! 装备配置Id
	HeroKeyId  int                 `json:"herokeyid"`  //! 装备拥有者
	Level      int                 `json:"level"`      //初始等级1
	Star       int                 `json:"star"`       //升级
	Enchanting [ENCHANTING_MAX]int `json:"enchanting"` //附魔
	//EnchantingNum    int                 `json:"enchantingnum"`    //附魔消耗道具
	ReconstructionLv int `json:"reconstructionlv"` //再造
	Resonance        int `json:"resonance"`        //联结 魔典ID
	SkillId          int `json:"skill_id"`         //技能id
}
type JS_Weapons struct {
	Id    int `json:"id"`
	Level int `json:"level"`
	Star  int `json:"star"`
}
type JS_Vehicle struct {
	Id               int `json:"id"`
	Level            int `json:"level"`
	Star             int `json:"star"`
	UnsealLv         int `json:"unseallv"`         //解封等级
	EnchantsLv       int `json:"enchantslv"`       //改造等级
	TrammelVehicleId int `json:"trammelvehicleid"` //羁绊战灵ID
	SkillId          int `json:"skill_id"`         //连接技能id
}

func (self *BattleInfo) Check() {
	if self.UserInfo[0] == nil {
		self.UserInfo[0] = new(BattleUserInfo)
		self.UserInfo[0].HeroInfo = make([]*BattleHeroInfo, 0)
	}
	if self.UserInfo[1] == nil {
		self.UserInfo[1] = new(BattleUserInfo)
		self.UserInfo[1].HeroInfo = make([]*BattleHeroInfo, 0)
	}
	if self.Weaken == nil {
		self.Weaken = make([]*WeakenInfo, 0)
	}
}
