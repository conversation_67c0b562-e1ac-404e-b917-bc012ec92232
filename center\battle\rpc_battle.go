package battle

import (
	"fmt"
	json "github.com/bytedance/sonic"
	"master/db"
	"master/model"
	"master/utils"
)

const (
	RETCODE_OK         = 0 //! 没有错误
	RETCODE_DATA_ERROR = 1 //! 数据异常
)

// ! 消息主体
type RPC_Battle struct {
}

type RPC_BattleReq struct {
	Type    int    `json:"type"`
	FightID int64  `json:"fightid"`
	LevelID int64  `json:"levelid"`
	Record  []byte `json:"record"`
}
type RPC_BattleRes struct {
	RetCode int    //! 返回结果
	Type    int    `json:"type"`
	FightID int64  `json:"fightid"`
	LevelID int64  `json:"levelid"`
	Record  []byte `json:"record"`
}

// 获取排行信息
func (self *RPC_Battle) SetReplay(req *RPC_BattleReq, res *RPC_BattleRes) error {
	value, flag, err := db.HGetRedisEx(BATTLE_REPLAY_RECORD, int64(req.FightID), fmt.Sprintf("%d", req.FightID))
	if err != nil || !flag {
		fightData := model.FightReplay{}
		fightData.Record = req.Record
		fightData.FightType = req.Type
		fightData.LevelID = req.LevelID
		db.HMSetRedisEx(BATTLE_REPLAY_RECORD, int64(req.FightID), &fightData, utils.HOUR_SECS*24)
		res.RetCode = RETCODE_OK
		res.Type = req.Type
		res.FightID = req.FightID
		res.LevelID = req.LevelID
		res.Record = req.Record
	} else {
		fightData := model.FightReplay{}
		if flag {
			err := json.Unmarshal([]byte(value), &fightData)
			if err != nil {
				res.RetCode = RETCODE_DATA_ERROR
				return nil
			}
		}
		res.RetCode = RETCODE_OK
		res.FightID = req.FightID
		res.Type = fightData.FightType
		res.LevelID = fightData.LevelID
		res.Record = fightData.Record
	}
	return nil
}

// 上传玩家信息
func (self *RPC_Battle) GetReplay(req *RPC_BattleReq, res *RPC_BattleRes) error {
	value, flag, err := db.HGetRedisEx(BATTLE_REPLAY_RECORD, req.FightID, fmt.Sprintf("%d", req.FightID))
	if err != nil || !flag {
		res.RetCode = RETCODE_DATA_ERROR
		return nil
	}

	fightData := model.FightReplay{}
	if flag {
		err := json.Unmarshal([]byte(value), &fightData)
		if err != nil {
			res.RetCode = RETCODE_DATA_ERROR
			return nil
		}
	}
	res.RetCode = RETCODE_OK
	res.FightID = req.FightID
	res.Type = fightData.FightType
	res.LevelID = fightData.LevelID
	res.Record = fightData.Record
	return nil
}
