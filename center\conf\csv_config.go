package conf

import (
	"master/utils"
	"math"
)

var (
	SimpleConfigMap       map[int]*SimpleConfig
	DanGradingConfigSlice []*DanGradingConfig
	DanGradingConfigMap   map[int]*DanGradingConfig
	ServerGroupingSlice   []*ServerGrouping
	ServerZoneSlice       []*ServerZone
	MapChampionConfig     map[int]*ChampionConfig

	ClanBattleConfigSlice []*DanGradingConfig
	ClanBattleConfigMap   map[int]*DanGradingConfig
)

// 通用配置
type SimpleConfig struct {
	Id   int    `json:"id"`
	Num  int    `json:"num"`
	Text string `json:"text"`
}

type DanGradingConfig struct {
	Id               int `json:"id"`
	DanIntegralmin   int `json:"dan_integralmin"`
	DanIntegralmax   int `json:"dan_integralmax"`
	DanExpected      int `json:"dan_expected"`
	DanWinParameter  int `json:"dan_winparameter"`
	DanLoseParameter int `json:"dan_loseparameter"`
	DanIntegral      int `json:"dan_integral"`
	DanRuank         int `json:"dan_ruank"`
	DanScore         int `json:"dan_score"`
}

type ServerGrouping struct {
	GroupingId  int `json:"groupingid"`
	GroupStar   int `json:"group_star"`
	GroupFinish int `json:"group_finish"`
	GroupNum    int `json:"group_num"`
}

type ServerZone struct {
	GroupingId  int `json:"groupingid"`
	GroupStar   int `json:"group_star"`
	GroupFinish int `json:"group_finish"`
	ZoneNum     int `json:"zone_num"`
}

type ChampionConfig struct {
	Id       int `json:"id"`
	Location int `json:"location"`
}

func init() {
	SimpleConfigMap = make(map[int]*SimpleConfig)
	utils.GetCsvUtilMgr().LoadCsv("Simple_Config", &SimpleConfigMap)

	utils.GetCsvUtilMgr().LoadCsv("Dan_grading", &DanGradingConfigSlice)
	DanGradingConfigMap = make(map[int]*DanGradingConfig)
	utils.GetCsvUtilMgr().LoadCsv("Dan_grading", &DanGradingConfigMap)

	utils.GetCsvUtilMgr().LoadCsv("Server_Grouping", &ServerGroupingSlice)
	utils.GetCsvUtilMgr().LoadCsv("Server_Zone", &ServerZoneSlice)

	MapChampionConfig = make(map[int]*ChampionConfig)
	utils.GetCsvUtilMgr().LoadCsv("Champion", &MapChampionConfig)

	utils.GetCsvUtilMgr().LoadCsv("Clan_battle", &ClanBattleConfigSlice)
	ClanBattleConfigMap = make(map[int]*DanGradingConfig)
	utils.GetCsvUtilMgr().LoadCsv("Clan_battle", &ClanBattleConfigMap)
	return
}

func GetInitNum(id int) int {
	initStar, ok := SimpleConfigMap[id]
	if !ok {
		return 0
	}
	return initStar.Num
}

func GetSimpleConfigInt(keyId int, defaultValue int) int {
	if v, ok := SimpleConfigMap[keyId]; ok {
		return v.Num
	}

	return defaultValue
}

//计算段位
func CalScoreLv(score int, rankPos int) int {
	scoreLv := 1
	for _, v := range DanGradingConfigSlice {
		if score >= v.DanIntegralmin && rankPos <= v.DanRuank {
			scoreLv = v.Id
		}
	}
	return scoreLv
}

//获得段位配置
func GetDanConfig(id int) *DanGradingConfig {
	return DanGradingConfigMap[id]
}

//计算段位
func CalCosmicScoreLv(score int, rankPos int) int {
	scoreLv := 1
	for _, v := range ClanBattleConfigSlice {
		if score >= v.DanIntegralmin && rankPos <= v.DanRuank {
			scoreLv = v.Id
		}
	}
	return scoreLv
}

//获得段位配置
func GetCosmicDanConfig(id int) *DanGradingConfig {
	return ClanBattleConfigMap[id]
}

//算分
func CountAddPoint(win bool, config *DanGradingConfig, myPoint int64, enemyPoint int64) int64 {
	addCount := int64(0)
	if config == nil {
		return addCount
	}

	if config.DanExpected <= 0 {
		return addCount
	}
	// 我方胜利敌方失败我方获得积分 = integral的值 *（winparameter的值/100 - 1/（1+10^((对手积分 - 我的积分）/expected的值）
	// 我方失败敌方胜利敌方获得积分 = integral的值 *（winparameter的值/100 - 1/（1+10^(( 我的积分-对手积分）/expected的值）
	min := float64(enemyPoint - myPoint)
	if !win {
		min = float64(myPoint - enemyPoint)
	}
	diff := min / float64(config.DanExpected)
	mult := float64(config.DanWinParameter)/100 - float64(1)/(math.Pow(10, diff)+float64(1))
	addCount = int64(float64(config.DanIntegral) * mult)
	return addCount
}
func CountMinPoint(win bool, config *DanGradingConfig, myPoint int64, enemyPoint int64) int64 {
	addCount := int64(0)
	if config == nil {
		return addCount
	}

	if config.DanExpected <= 0 {
		return 0
	}

	// 我方胜利敌方失败敌方减少积分 = integral的值 *（loseparameter的值/100 - 1/（1+10^((我的积分 - 对手积分）/expected的值）
	// 我方失败敌方胜利我方减少积分 = integral的值 *（loseparameter的值/100 - 1/（1+10^((对手积分 - 我的积分）/expected的值）
	min := float64(myPoint - enemyPoint)
	if !win {
		min = float64(enemyPoint - myPoint)
	}
	diff := min / float64(config.DanExpected)
	mult := float64(config.DanLoseParameter)/100 - float64(1)/(math.Pow(10, diff)+float64(1))
	addCount = int64(float64(config.DanIntegral) * mult)
	return addCount
}
