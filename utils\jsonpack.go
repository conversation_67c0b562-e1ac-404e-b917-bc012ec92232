package utils

import (
	"encoding/json"
	"fmt"
	"errors"
)

// JSONH 提供JSON HPack算法的压缩和解压缩功能
type JSONH struct{}

// UnpackCreateRow 根据键和值创建一个map，处理可能的nil值
func (j *JSONH) UnpackCreateRow(keys []string, values []interface{}) map[string]interface{} {
	result := make(map[string]interface{})
	for i, key := range keys {
		if i >= len(values) || values[i] == nil {
			result[key] = nil
			continue
		}
		result[key] = values[i]
	}
	return result
}

// Best 找出最佳的压缩级别
func (j *JSONH) Best(collection []map[string]interface{}) (int, error) {
	if len(collection) == 0 {
		return 0, errors.New("empty collection")
	}

	var cache [][]interface{}
	bestIndex := 0
	minLength := 0

	for i := 0; i < 4; i++ {
		packed, err := j.PackLevel(collection, i)
		if err != nil {
			return 0, fmt.Errorf("pack level %d failed: %w", i, err)
		}
		cache = append(cache, packed)

		jsonData, err := json.Marshal(packed)
		if err != nil {
			return 0, fmt.<PERSON><PERSON><PERSON>("marshal failed: %w", err)
		}
		length := len(jsonData)

		if i == 0 || length < minLength {
			minLength = length
			bestIndex = i
		}
	}

	return bestIndex, nil
}

// Pack 压缩JSON数据
func (j *JSONH) Pack(collection []map[string]interface{}) ([]interface{}, error) {
	result, err := j.PackLevel(collection, 0)
	if err != nil {
		return nil, fmt.Errorf("pack failed: %w", err)
	}
	return result, nil
}

// Pack 带压缩级别的压缩函数
func (j *JSONH) PackLevel(collection []map[string]interface{}, compression int) ([]interface{}, error) {
	if len(collection) == 0 {
		return nil, errors.New("empty collection")
	}

	if compression > 3 {
		bestIndex, err := j.Best(collection)
		if err != nil {
			return nil, fmt.Errorf("best compression level failed: %w", err)
		}
		return j.PackLevel(collection, bestIndex)
	}

	var result []interface{}
	var header []interface{}

	if len(collection) > 0 {
		first := collection[0]
		// 提取头部(键)
		for k := range first {
			header = append(header, k)
		}
		result = append(result, header)

		// 提取值，处理可能的nil值
		for _, item := range collection {
			var row []interface{}
			for _, k := range header {
				key := k.(string)
				if val, exists := item[key]; exists {
					row = append(row, val)
				} else {
					row = append(row, nil)
				}
			}
			result = append(result, row)
		}

		// 应用压缩策略
		if compression > 0 {
			// 处理嵌套的item数组
			itemIndex := getIndexOfKey("item", header)
			if itemIndex >= 0 {
				for i := 1; i < len(result); i++ {
					row, ok := result[i].([]interface{})
					if !ok {
						return nil, errors.New("invalid row type")
					}
					if row[itemIndex] != nil {
						if items, ok := row[itemIndex].([]interface{}); ok {
							processedItems, err := processNestedItems(items)
							if err != nil {
								return nil, fmt.Errorf("process nested items failed: %w", err)
							}
							row[itemIndex] = processedItems
						}
					}
				}
			}

			// 第一行用于类型检查
			firstRow, ok := result[1].([]interface{})
			if !ok {
				return nil, errors.New("invalid first row type")
			}

			for j := 0; j < len(header); j++ {
				// 检查是否为非数值类型
				if firstRow[j] == nil {
					continue // 跳过nil值
				}

				if _, ok := firstRow[j].(int); !ok {
					if _, ok := firstRow[j].(float64); !ok {
						// 创建缓存
						var cache []interface{}
						newHeader := []interface{}{header[j], cache}

						// 替换头部
						header[j] = newHeader

						// 处理每一行
						for i := 1; i < len(result); i++ {
							row, ok := result[i].([]interface{})
							if !ok {
								return nil, errors.New("invalid row type")
							}
							value := row[j]

							// 处理nil值
							if value == nil {
								row[j] = nil
								continue
							}

							// 查找值在缓存中的位置
							index := -1
							for k, v := range cache {
								if v == value {
									index = k
									break
								}
							}

							// 如果不在缓存中，添加并使用新索引
							if index == -1 {
								cache = append(cache, value)
								row[j] = len(cache) - 1
							} else {
								row[j] = index
							}
						}
					}
				}
			}

			// 更高级别的压缩策略
			if compression > 1 {
				// 计算半程长度
				halfLength := len(collection) - (len(collection) / 2)

				for j := 0; j < len(header); j++ {
					if h, ok := header[j].([]interface{}); ok {
						key := h[0].(string)
						cache := h[1].([]interface{})

						if compression > 2 {
							// 压缩级别3的策略
							values := []interface{}{}
							indexes := []interface{}{}

							for i := 1; i < len(result); i++ {
								row, ok := result[i].([]interface{})
								if !ok {
									return nil, errors.New("invalid row type")
								}
								// 处理nil值
								if row[j] == nil {
									values = append(values, nil)
									indexes = append(indexes, nil)
									continue
								}

								idx, ok := row[j].(int)
								if !ok {
									return nil, errors.New("invalid index type")
								}
								indexes = append(indexes, idx)
								values = append(values, cache[idx])
							}

							indexes = append(indexes, cache...)

							valuesJSON, err := json.Marshal(values)
							if err != nil {
								return nil, fmt.Errorf("marshal values failed: %w", err)
							}

							indexesJSON, err := json.Marshal(indexes)
							if err != nil {
								return nil, fmt.Errorf("marshal indexes failed: %w", err)
							}

							if len(valuesJSON) < len(indexesJSON) {
								for i := 1; i < len(result); i++ {
									// 处理nil值
									if values[i-1] == nil {
										result[i].([]interface{})[j] = nil
									} else {
										result[i].([]interface{})[j] = values[i-1]
									}
								}
								header[j] = key
							}
						} else if halfLength < len(cache) {
							// 压缩级别2的策略
							for i := 1; i < len(result); i++ {
								row, ok := result[i].([]interface{})
								if !ok {
									return nil, errors.New("invalid row type")
								}
								// 处理nil值
								if row[j] == nil {
									continue
								}

								idx := row[j].(int)
								row[j] = cache[idx]
							}
							header[j] = key
						}
					}
				}
			}

			// 重新组织头部
			newHeader := []interface{}{}
			for j := 0; j < len(header); j++ {
				if h, ok := header[j].([]interface{}); ok {
					newHeader = append(newHeader, h[0])
					newHeader = append(newHeader, h[1])
				} else if header[j] != nil {
					newHeader = append(newHeader, header[j])
				}
			}
			result[0] = newHeader
		}
	}

	return result, nil
}

// Unpack 解压缩JSON数据
func (j *JSONH) Unpack(collection []interface{}) ([]map[string]interface{}, error) {
	if len(collection) == 0 {
		return []map[string]interface{}{}, nil
	}

	header := collection[0].([]interface{})
	var keys []string
	result := make([]map[string]interface{}, 0, len(collection)-1)

	// 处理头部，提取键和缓存
	for i := 0; i < len(header); i++ {
		// 处理nil值
		if header[i] == nil {
			keys = append(keys, "")
			continue
		}

		keys = append(keys, header[i].(string))

		// 检查下一个元素是否为缓存
		if i+1 < len(header) && header[i+1] != nil {
			if cache, ok := header[i+1].([]interface{}); ok {
				// 应用缓存到每一行
				for j := 1; j < len(collection); j++ {
					row := collection[j].([]interface{})
					// 处理nil值
					if row[i] == nil {
						continue
					}

					index := row[i].(int)
					if index < 0 || index >= len(cache) {
						row[i] = nil // 索引越界，设为nil
					} else {
						row[i] = cache[index]
					}
				}
				i++ // 跳过缓存
			}
		}
	}

	// 创建结果映射
	for i := 1; i < len(collection); i++ {
		row := collection[i].([]interface{})
		// 确保值的数量与键的数量匹配
		if len(row) < len(keys) {
			// 补齐缺失的值为nil
			for len(row) < len(keys) {
				row = append(row, nil)
			}
		}

		// 处理item字段的嵌套结构
		itemIndex := getIndexOfKey("item", interface{}(keys).([]interface{}))
		if itemIndex >= 0 && itemIndex < len(row) && row[itemIndex] != nil {
			if items, ok := row[itemIndex].([]interface{}); ok {
				processedItems := unpackNestedItems(items)
				row[itemIndex] = processedItems
			}
		}

		result = append(result, j.UnpackCreateRow(keys, row))
	}

	return result, nil
}

// CompressJSON 压缩JSON字符串
func (j *JSONH) CompressJSON(jsonStr string, compression int) ([]byte, error) {
	// 尝试转换对象为数组
	convertedJSON, err := convertObjectToArray(jsonStr)
	if err != nil {
		return nil, fmt.Errorf("failed to convert object to array: %w", err)
	}

	// 解析JSON字符串
	var collection []map[string]interface{}
	if err := json.Unmarshal([]byte(convertedJSON), &collection); err != nil {
		return nil, fmt.Errorf("failed to parse JSON: %w", err)
	}

	// 压缩数据
	packed, err := j.PackLevel(collection, compression)
	if err != nil {
		return nil, fmt.Errorf("pack failed: %w", err)
	}

	// 序列化为JSON
	return json.Marshal(packed)
}

// DecompressJSON 解压缩JSON字符串
func (j *JSONH) DecompressJSON(jsonStr string) ([]byte, error) {
	// 解析压缩的JSON
	var packed []interface{}
	if err := json.Unmarshal([]byte(jsonStr), &packed); err != nil {
		return nil, fmt.Errorf("failed to parse compressed JSON: %w", err)
	}

	// 解压缩数据
	unpacked, err := j.Unpack(packed)
	if err != nil {
		return nil, fmt.Errorf("unpack failed: %w", err)
	}

	// 序列化为JSON
	return json.Marshal(unpacked)
}

// 辅助函数：查找键在头部中的索引
func getIndexOfKey(key string, header []interface{}) int {
	for i, h := range header {
		if hStr, ok := h.(string); ok && hStr == key {
			return i
		}
	}
	return -1
}

// 辅助函数：将对象转换为数组
func convertObjectToArray(jsonStr string) (string, error) {
	var data map[string]interface{}
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", err
	}

	var array []interface{}
	for _, v := range data {
		array = append(array, v)
	}

	result, err := json.Marshal(array)
	if err != nil {
		return "", err
	}

	return string(result), nil
}

// 处理嵌套的item数组
func processNestedItems(items []interface{}) ([]interface{}, error) {
	if len(items) == 0 {
		return items, nil
	}

	// 检查第一项是否为map
	first, ok := items[0].(map[string]interface{})
	if !ok {
		return items, nil
	}

	// 提取所有键
	var keys []string
	for k := range first {
		keys = append(keys, k)
	}

	// 创建新的结构
	var result []interface{}
	result = append(result, keys)

	// 处理每个item
	for _, item := range items {
		if item == nil {
			result = append(result, nil)
			continue
		}

		itemMap, ok := item.(map[string]interface{})
		if !ok {
			result = append(result, item)
			continue
		}

		var row []interface{}
		for _, key := range keys {
			row = append(row, itemMap[key])
		}
		result = append(result, row)
	}

	return result, nil
}

// 解包嵌套的item数组
func unpackNestedItems(items []interface{}) []interface{} {
	if len(items) == 0 {
		return items
	}

	// 假设所有item都有相同的结构
	if _, ok := items[0].([]interface{}); !ok {
		return items // 不是预期的嵌套结构，直接返回
	}

	// 定义可能的键（根据您的数据结构调整）
	keys := []string{"itemid", "num"} // 可能需要扩展

	result := make([]interface{}, len(items))
	for i, item := range items {
		if itemRow, ok := item.([]interface{}); ok {
			itemMap := make(map[string]interface{})
			for j, key := range keys {
				if j < len(itemRow) {
					itemMap[key] = itemRow[j]
				}
			}
			result[i] = itemMap
		} else {
			result[i] = item
		}
	}

	return result
}
