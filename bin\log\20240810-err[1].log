14:34:33.465223 mgr_tower.go:137: [error ] dial tcp 127.0.0.1:6379: connectex: No connection could be made because the target machine actively refused it. goroutine 75 [running]:
runtime/debug.Stack()
	D:/Program Files/Go1.21/src/runtime/debug/stack.go:24 +0x6b
master/center/tower.(*TowerMgr).RunMigrate.func1()
	E:/Work/TheService/DemonSlayer/master/center/tower/mgr_tower.go:137 +0x135
panic({0x18af200?, 0xc000690060?})
	D:/Program Files/Go1.21/src/runtime/panic.go:920 +0x290
master/db.NewPool.func1()
	E:/Work/TheService/DemonSlayer/master/db/mgr_redis.go:103 +0x345
github.com/garyburd/redigo/redis.(*Pool).get(0xc000118230, {0x0, 0x0})
	C:/Users/<USER>/go/pkg/mod/github.com/garyburd/redigo@v1.6.2/redis/pool.go:331 +0x64d
github.com/garyburd/redigo/redis.(*Pool).Get(0xc000118230)
	C:/Users/<USER>/go/pkg/mod/github.com/garyburd/redigo@v1.6.2/redis/pool.go:179 +0x48
master/db.(*RedisMgr).GetRedisConn(0xc00006d980)
	E:/Work/TheService/DemonSlayer/master/db/mgr_redis.go:91 +0x2d
master/db.(*RedisMgr).HLen(0xc00006d980, {0x19b9acc, 0x13})
	E:/Work/TheService/DemonSlayer/master/db/mgr_redis.go:339 +0x54
master/center/tower.(*TowerMgr).RunMigrate(0xc000078600)
	E:/Work/TheService/DemonSlayer/master/center/tower/mgr_tower.go:146 +0xdc
created by master/app.(*MasterApp).Init in goroutine 1
	E:/Work/TheService/DemonSlayer/master/app/app_master.go:131 +0x20d

16:28:30.279193 app_master.go:166: [fatal ] server shutdown
