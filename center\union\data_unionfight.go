package union

import (
	"fmt"
	json "github.com/bytedance/sonic"
	"master/center/crossserver"
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
)

// UnionFight ! 公会战-实例
type UnionFight struct {
	Id             int    // 流水号
	Period         int    // 期数
	TeamId         int    // 分组号
	AttackSideStr  string // 攻方信息
	DefenceSideStr string // 守方信息
	Result         int    // 会战结果
	BattleInfoStr  string // 战斗数据

	AttackSide  *UnionFightSide
	DefenceSide *UnionFightSide
	BattleInfo  []int64

	db.DataUpdate
}

func (self *UnionFight) Encode() {
	self.AttackSideStr = utils.HF_JtoA(&self.AttackSide)
	self.DefenceSideStr = utils.HF_JtoA(&self.DefenceSide)
	self.BattleInfoStr = utils.HF_JtoA(&self.BattleInfo)
}

func (self *UnionFight) Decode() {
	json.Unmarshal([]byte(self.AttackSideStr), &self.AttackSide)
	json.Unmarshal([]byte(self.DefenceSideStr), &self.DefenceSide)
	json.Unmarshal([]byte(self.BattleInfoStr), &self.BattleInfo)
}

func (self *UnionFight) GetNode(targetUid int64) (*UnionFightMemberNode, int) {
	if self == nil {
		return nil, 0
	}
	for _, v := range self.AttackSide.UnionFightMemberNode {
		if v.Uid == targetUid {
			return v, core.POS_ATTACK
		}
	}
	for _, v := range self.DefenceSide.UnionFightMemberNode {
		if v.Uid == targetUid {
			return v, core.POS_DEFENCE
		}
	}
	for _, v := range self.AttackSide.CanPlayList {
		if v == targetUid {
			return nil, core.POS_ATTACK
		}
	}
	for _, v := range self.DefenceSide.CanPlayList {
		if v == targetUid {
			return nil, core.POS_DEFENCE
		}
	}
	return nil, core.POS_INVALID
}

func (self *UnionFight) GetUnionSide(unionId int) int {

	if self.AttackSide != nil && self.AttackSide.UnionId == unionId {
		return core.POS_ATTACK
	}

	if self.DefenceSide != nil && self.DefenceSide.UnionId == unionId {
		return core.POS_DEFENCE
	}
	return core.POS_INVALID
}

func (self *UnionFight) GetUnionRank(side int) []*UnionActivityRank {
	if side == core.POS_ATTACK {
		if len(self.AttackSide.UnionActivityRank) >= core.UNIONACTIVITY_LIKE_RANK {
			return self.AttackSide.UnionActivityRank[0:core.UNIONACTIVITY_LIKE_RANK]
		}
		return self.AttackSide.UnionActivityRank
	} else if side == core.POS_DEFENCE {
		if len(self.DefenceSide.UnionActivityRank) >= core.UNIONACTIVITY_LIKE_RANK {
			return self.DefenceSide.UnionActivityRank[0:core.UNIONACTIVITY_LIKE_RANK]
		}
		return self.DefenceSide.UnionActivityRank
	}
	return nil
}

func (self *UnionFight) CalResult() {
	if self == nil {
		return
	}
	attackLoseStar := 0
	defenceLoseStar := 0
	attackLoseTime := int64(0)
	defenceLoseTime := int64(0)
	for _, v := range self.AttackSide.UnionFightMemberNode {
		attackLoseStar += v.StarLose
		if attackLoseTime < v.StarLoseTime {
			attackLoseTime = v.StarLoseTime
		}
	}
	for _, v := range self.DefenceSide.UnionFightMemberNode {
		defenceLoseStar += v.StarLose
		if defenceLoseTime < v.StarLoseTime {
			defenceLoseTime = v.StarLoseTime
		}
	}
	self.Result = core.DEFENCE_WIN
	if attackLoseStar == 0 && defenceLoseStar == 0 {
		self.Result = core.ALL_LOSE
	} else if attackLoseStar == defenceLoseStar {
		if attackLoseTime >= defenceLoseTime {
			self.Result = core.ATTACK_WIN
		}
	} else if attackLoseStar < defenceLoseStar {
		self.Result = core.ATTACK_WIN
	}
}

// UnionFightSide ! 公会战-数据存储
type UnionFightSide struct {
	UnionId              int                     `json:"unionid"`              //公会ID
	ServerId             int                     `json:"serverid"`             //! 区服Id，需要考虑合服后
	UnionName            string                  `json:"unionname"`            //公会名
	UnionIcon            int                     `json:"unionicon"`            //公会icon
	MasterName           string                  `json:"mastername"`           //会长名
	Star                 int                     `json:"star"`                 //公会获得的累积分数
	StarUpdate           int64                   `json:"starupdate"`           //分数更新时间
	UnionFightMemberNode []*UnionFightMemberNode `json:"unionfightmembernode"` //对战成员信息
	UnionActivityRank    ArrUnionActivityRank    `json:"unionactivityrank"`    //排行
	CanPlayList          []int64                 `json:"canplaylist"`          //有权限参加本次公会战的人
}

func (self *UnionFightSide) Init() {
	self.UnionFightMemberNode = make([]*UnionFightMemberNode, 0)
	self.CanPlayList = make([]int64, 0)
	self.UnionActivityRank = make([]*UnionActivityRank, 0)
}

func (self *UnionFightSide) Check() {
	if self.UnionFightMemberNode == nil {
		self.UnionFightMemberNode = make([]*UnionFightMemberNode, 0)
	}
	if self.CanPlayList == nil {
		self.CanPlayList = make([]int64, 0)
	}
	if self.UnionActivityRank == nil {
		self.UnionActivityRank = make([]*UnionActivityRank, 0)
	}
}

type UnionFightMemberNode struct {
	Uid             int64                `json:"uid"`
	Robot           int                  `json:"robot"`           //是否是机器人(客户端不用)
	Index           int                  `json:"index"`           //序号
	DefenceWinTimes int                  `json:"defencewintimes"` //成功防守次数
	StarLose        int                  `json:"starlose"`        //失去星数
	StarLoseTime    int64                `json:"starlosetime"`    //最后失去星数的时间
	Fight           int64                `json:"fight"`           //战力
	BattleInfo      []int64              `json:"battleinfo"`      //战报
	Declare         []*Declare           `json:"declare"`         //宣告
	BeFightLevel    int                  `json:"befightlevel"`    //吊打等级
	BaseInfoSimple  PlayerBaseInfoSimple `json:"baseinfosimple"`
}

func (self *UnionFightMemberNode) Init() {
	if self.BattleInfo == nil {
		self.BattleInfo = make([]int64, 0)
	}
	if self.Declare == nil {
		self.Declare = make([]*Declare, 0)
	}
}

type ArrUnionActivityRank []*UnionActivityRank

func (s ArrUnionActivityRank) Len() int      { return len(s) }
func (s ArrUnionActivityRank) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s ArrUnionActivityRank) Less(i, j int) bool {
	return s[i].Score > s[j].Score
}

type UnionActivityRank struct {
	Uid            int64                `json:"uid"`
	RankPos        int                  `json:"rankpos"`
	BaseInfoSimple PlayerBaseInfoSimple `json:"baseinfosimple"`
	Star           int                  `json:"star"`
	Score          int                  `json:"score"`
	GetLike        []int64              `json:"getlike"`
	Like           int                  `json:"like"`
}

func (self *UnionActivityRank) Init() {
	if self.GetLike == nil {
		self.GetLike = make([]int64, 0)
	}
}

type Declare struct {
	Key            int                  `json:"key"`
	Uid            int64                `json:"uid"`
	BaseInfoSimple PlayerBaseInfoSimple `json:"baseinfo"`
}

// PlayerBaseInfoSimple ! 简单的角色信息结构
type PlayerBaseInfoSimple struct {
	Uid      int64  `json:"uid"`
	Name     string `json:"uname"`
	IconId   int    `json:"iconid"`
	Portrait int    `json:"portrait"`
	Title    int    `json:"title"`
	Level    int    `json:"level"`   //! 玩家等级
	WarShip  int    `json:"warship"` //! 战船展示
	Vip      int    `json:"vip"`     //! vip
	FameId   int    `json:"fameid"`  //! 名望
}

func GetJsUnionFight(src *UnionFight) *JS_UnionFight {
	if src == nil {
		return nil
	}
	rel := new(JS_UnionFight)
	rel.Id = src.TeamId
	rel.AttackSide = src.AttackSide
	rel.DefenceSide = src.DefenceSide
	rel.Result = src.Result
	for _, v := range src.BattleInfo {
		battleInfo := GetBattleInfoRedis(v)
		if battleInfo == nil {
			continue
		}
		rel.BattleInfo = append(rel.BattleInfo, battleInfo)
	}
	return rel
}

func GetBattleInfoRedis(fightID int64) *model.BattleInfo {
	var battleInfo model.BattleInfo
	value, flag, err := db.HGetRedisEx(crossserver.BATTLE_INFO_CHAMPION, fightID, fmt.Sprintf("%d", fightID))
	if err != nil || !flag {
		return nil
	}
	if flag {
		err := json.Unmarshal([]byte(value), &battleInfo)
		if err != nil {
			return &battleInfo
		}
	}
	if battleInfo.Id != 0 {
		return &battleInfo
	}
	return nil
}

type JS_UnionFight struct {
	Id          int                 `json:"id"`          //流水号
	AttackSide  *UnionFightSide     `json:"attackside"`  //攻方信息
	DefenceSide *UnionFightSide     `json:"defenceside"` //守方信息
	Result      int                 `json:"result"`      //会战结果
	BattleInfo  []*model.BattleInfo `json:"battleinfo"`  //会战战报
}

func (self *JS_UnionFight) Init() {
	self.BattleInfo = make([]*model.BattleInfo, 0)
	self.AttackSide = new(UnionFightSide)
	self.AttackSide.Init()
	self.DefenceSide = new(UnionFightSide)
	self.DefenceSide.Init()
}

func (self *JS_UnionFight) Check() {
	if self.BattleInfo == nil {
		self.BattleInfo = make([]*model.BattleInfo, 0)
	}
	if self.AttackSide == nil {
		self.AttackSide = new(UnionFightSide)
		self.AttackSide.Init()
	} else {
		self.AttackSide.Check()
	}
	if self.DefenceSide == nil {
		self.DefenceSide = new(UnionFightSide)
		self.DefenceSide.Init()
	} else {
		self.DefenceSide.Check()
	}
}

// FightInfoCache ! 战斗信息结构
type FightInfoCache struct {
	NeedSave         bool              //! 是否需要保存
	LastLiveTime     int64             //! 上次更新时间
	FightInfoCacheDB *FightInfoCacheUB //! 数据保存结构
}

// FightInfoCacheUB ! UnionBattle战斗缓存信息
type FightInfoCacheUB struct {
	Uid       int64
	ActType   int    //! 活动类型
	UnionId   int    //! 公会Id
	BaseUser  string //! 角色基本信息
	FightInfo string //! 战斗数据

	fightInfo *model.JS_FightInfo         //! 通用的战斗结构,每次变更阵容都会修改
	baseUser  *model.PlayerBaseInfoSimple //! 简要角色数据
	db.DataUpdate
}

func (self *FightInfoCacheUB) GetFightInfo() *model.JS_FightInfo {
	return self.fightInfo
}

func (self *FightInfoCacheUB) SetFightInfo(info *model.JS_FightInfo) {
	self.fightInfo = info
}

func (self *FightInfoCacheUB) Encode() {
	self.FightInfo = utils.Lz4Encode(&self.fightInfo)
}

func (self *FightInfoCacheUB) Decode() {
	utils.Lz4Decode([]byte(self.FightInfo), &self.fightInfo)
	return
}

func (self *FightInfoCache) Save() {
	if self.NeedSave {
		self.FightInfoCacheDB.Encode()
		self.FightInfoCacheDB.Update(true, false)
		self.NeedSave = false
	}
}
