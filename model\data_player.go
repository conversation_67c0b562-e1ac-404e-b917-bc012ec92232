package model

// PlayerBaseInfoSimple 提炼出的较小的结构，这个结果不要随便扩展，有需求先联系 zy
type PlayerBaseInfoSimple struct {
	Uid      int64  `json:"uid"`
	Name     string `json:"uname"`
	IconId   int    `json:"iconid"`
	Portrait int    `json:"portrait"`
	Title    int    `json:"title"`
	Level    int    `json:"level"`   //! 玩家等级
	WarShip  int    `json:"warship"` //战船展示
	Vip      int    `json:"vip"`     //vip
	FameId   int    `json:"fameid"`  //名望
}
