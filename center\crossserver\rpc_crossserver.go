package crossserver

import (
	"errors"
	"master/center/conf"
	"master/model"
)

const (
	CROSS_SERVER_ACTION_GET_CONFIG        = 1
	CROSS_SERVER_ACTION_GET_INFO          = 2
	CROSS_SERVER_ACTION_GET_ENEMY         = 3
	CROSS_SERVER_ACTION_GET_ATTACK_END    = 4
	CROSS_SERVER_ACTION_GET_RANK          = 5
	CROSS_SERVER_ACTION_GET_BATTLE_RECORD = 6
	CROSS_SERVER_ACTION_LOOK              = 7
	CROSS_SERVER_ACTION_HALLOFGLORY       = 8
	CROSS_SERVER_ACTION_HALLOFGLORY_LIKE  = 9
	CROSS_SERVER_ACTION_UPDATE_INFO       = 10
	CROSS_SERVER_ACTION_GET_RANK_REWARD   = 11
)
const (
	CROSS_SERVER_ACTION_LEVEL_GET_INFO          = 1
	CROSS_SERVER_ACTION_LEVEL_FIGHT_START       = 2
	CROSS_SERVER_ACTION_LEVEL_RANK              = 3
	CROSS_SERVER_ACTION_LEVEL_HALLOFGLORY       = 4
	CROSS_SERVER_ACTION_LEVEL_HALLOFGLORY_LIKE  = 5
	CROSS_SERVER_ACTION_LEVEL_GET_BATTLE_RECORD = 6
	CROSS_SERVER_ACTION_LEVEL_LOOK              = 7
	CROSS_SERVER_ACTION_LEVEL_GET_REPLAY_RECORD = 8
)

const (
	EXPEDITION_TIME = 1
)

const (
	DEMONSLAYER_WORLD_ACTION_GET_CONFIG = 1
)

const (
	PEAK_ACTION_GET_CONFIG        = 1
	PEAK_ACTION_GET_INFO          = 2
	PEAK_ACTION_GET_ENEMY         = 3
	PEAK_ACTION_GET_ATTACK_END    = 4
	PEAK_ACTION_GET_RANK          = 5
	PEAK_ACTION_GET_BATTLE_RECORD = 6
	PEAK_ACTION_LOOK              = 7
	PEAK_ACTION_HALLOFGLORY       = 8
	PEAK_ACTION_HALLOFGLORY_LIKE  = 9
	PEAK_ACTION_UPDATE_INFO       = 10
	PEAK_ACTION_GET_RANK_REWARD   = 11
)

const (
	COSMIC_ACTION_GET_INFO          = 1
	COSMIC_ACTION_FIGHT_START       = 2
	COSMIC_ACTION_RANK              = 3
	COSMIC_ACTION_HALLOFGLORY       = 4
	COSMIC_ACTION_HALLOFGLORY_LIKE  = 5
	COSMIC_ACTION_GET_BATTLE_RECORD = 6
	COSMIC_ACTION_LOOK              = 7
	COSMIC_ACTION_GET_REPLAY_RECORD = 8
	COSMIC_ACTION_GET_CONFIG        = 9
)

const (
	CHAMPION_ZONE_ACTION_GET_INFO          = 1
	CHAMPION_ZONE_ACTION_GET_BETINFO       = 2
	CHAMPION_ZONE_ACTION_GET_DOLIKE        = 3
	CHAMPION_ZONE_ACTION_GET_BET           = 4
	CHAMPION_ZONE_ACTION_GET_TEAMINFO      = 5
	CHAMPION_ZONE_ACTION_GET_NOWTEAMID     = 6
	CHAMPION_ZONE_ACTION_GET_BATTLEINFOUID = 7
	CHAMPION_ZONE_ACTION_GET_BATTLERECORD  = 8
	CHAMPION_ZONE_ACTION_GET_PERIOD        = 9
	CHAMPION_ZONE_ACTION_GET_NOWTOP        = 10
	CHAMPION_ZONE_ACTION_GET_FIGHTINFO     = 11
	CHAMPION_ZONE_ACTION_GET_FIGHTUPDATE   = 12
	CHAMPION_ZONE_ACTION_GET_SIMPLEINFO    = 13
)

const (
	CENTER_CITYBROKEN_GET_ALL            = 1 //获取所有城主
	CENTER_CITYBROKEN_GET_SINGLE         = 2 //获取单个城主信息
	CENTER_CITYBROKEN_ATTACK_START       = 3 //战斗
	CENTER_CITYBROKEN_ATTACK_END         = 4 //战斗结束
	CENTER_CITYBROKEN_GET_CITY_OWNER     = 5 //获取自己的城主ID
	CENTER_CITYBROKEN_ATTACK_END_BY_PASS = 6 //通关后自动成为城主
	CENTER_CITYBROKEN_RECORD             = 7
	CENTER_CITYBROKEN_RECORD_BY_ID       = 8
	CENTER_CITYBROKEN_SET_CITY_SHIELD    = 9  //设置护盾
	CENTER_CITYBROKEN_GET_OWNER_POINT    = 10 //获取自己的积分
	CENTER_CITYBROKEN_UPDATE_TEAM        = 11 //更新队伍
	//CENTER_CITYBROKEN_POINT_UPDATE       = 10 //更新积分
)

type RPC_RegCrossServerReq struct {
	ServerId     int    //! 服务器Id
	OpenTime     int64  //! 开服时间
	MaxFightName string //! 战斗榜第一名的名字
}

type RPC_RegCrossServerRes struct {
	RetCode int //! 返回错误码
}

type RPC_UpdateActivityReq struct {
	ServerId   int //! 服务器Id
	ActivityId int //! 活动id
	StartTime  int64
	RewardTime int64
	EndTime    int64
	Periods    int
}

type RPC_UpdateActivityRes struct {
	RetCode int //! 返回错误码
}

type RPC_ExpeditionReq struct {
	Action int //!
}
type RPC_ExpeditionRes struct {
	RetCode int //! 返回结果
	Param1  string
	Param2  string
	Param3  string
}

// ! 事件返回
type RPC_CrossServerArenaReq struct {
	Uid        int64 //!
	Action     int   //!
	ServerId   int   //!
	FightInfos []*model.JS_FightInfo
	Attack     []*model.JS_FightInfo
	Defend     []*model.JS_FightInfo
	BattleInfo []*model.BattleInfo
	FightID    [CROSS_SERVER_ARENA_TEAM_MAX]int64
	Result     int
	Id         int64
	Param      string
}

type RPC_CrossServerArenaRes struct {
	RetCode int //! 返回结果
	Param1  string
	Param2  string
	Param3  string
	Param4  string
}

type RPC_UploadRankReq struct {
	RankId   int //! 排行类型类型
	RankInfo *model.RankInfo
	GetItem  []model.PassItem
	ServerId int
	Notice   int
}

type RPC_UploadRankRes struct {
	RetCode int //!
}

type RPC_GetRankReq struct {
	Uid      int64 //!
	RankId   int   //! 排行类型类型
	Period   int   //! 期数
	ServerId int   //! 服务器ID
}

type RPC_GetRankRes struct {
	RetCode  int //!
	RankInfo []*model.RankInfo
	SelfInfo *model.RankInfo
}

type RPC_GetNoticeReq struct {
	Uid      int64 //!
	NoticeId int   //! 排行类型类型
	Period   int   //! 期数
	ServerId int   //! 服务器ID
}

type RPC_GetNoticeRes struct {
	RetCode    int //!
	NoticeList []*model.NoticeInfo
}

type RPC_UploadRecordReq struct {
	RecordType   int //! 战报类型
	BattleInfo   *model.BattleInfo
	BattleRecord *model.BattleRecord
	Param        int
}

type RPC_UploadRecordRes struct {
	RetCode     int   //!
	NewBattleId int64 //!
}

type RPC_RankDoLikeReq struct {
	Uid       int64 //!
	RankId    int   //! 排行类型类型
	Period    int   //! 期数
	TargetUid int64 //! 点赞目标
	ServerId  int   //! 服务器ID
}

type RPC_RankDoLikeRes struct {
	RetCode    int //!
	TargetUid  int64
	TargetLike int
}

// ! 事件返回
type RPC_CrossServerLevelArenaReq struct {
	Uid        int64 //!
	Action     int   //!
	ServerId   int   //!
	TargetUid  int64 //!
	Id         int64 //!
	Type       int   //!
	FightInfos *model.JS_FightInfo
	Param      string
}
type RPC_CrossServerLevelArenaRes struct {
	RetCode int //! 返回结果
	Param1  string
	Param2  string
	Param3  string
	Param4  string
}

// ! 事件返回
type RPC_DemonslayerWorldReq struct {
	Uid      int64 //!
	Action   int   //!
	ServerId int   //!
}
type RPC_DemonslayerWorldRes struct {
	RetCode int //! 返回结果
	Param1  string
	Param2  string
	Param3  string
	Param4  string
}

type RPC_PeakArenaReq struct {
	Uid        int64 //!
	Action     int   //!
	ServerId   int   //!
	FightInfos []*model.JS_FightInfo
	Attack     []*model.JS_FightInfo
	Defend     []*model.JS_FightInfo
	BattleInfo []*model.BattleInfo
	FightID    [PEAK_ARENA_TEAM_MAX]int64
	Result     int
	Id         int64
	Param      string
}

type RPC_PeakArenaRes struct {
	RetCode int //! 返回结果
	Param1  string
	Param2  string
	Param3  string
	Param4  string
}

// ! 事件返回
type RPC_CosmicArenaReq struct {
	Uid        int64 //!
	Action     int   //!
	ServerId   int   //!
	TargetUid  int64 //!
	Type       int   //!
	Id         int64 //!
	Camp       int   //!
	FightInfos *model.JS_FightInfo
}
type RPC_CosmicArenaRes struct {
	RetCode int //! 返回结果
	Param1  string
	Param2  string
	Param3  string
	Param4  string
	Param5  string
}

type RPC_CityBrokenReq struct {
	Action           int
	CityBrokenId     int64 //! 城池ID
	ServerId         int
	KeyId            int
	TargetUid        int64
	Result           int
	FightInfo        *model.JS_FightInfo
	CityBrokenIdList []int64
	Rand             int64
	FightId          int64
	PointUpdate      string
}

type RPC_CityBrokenRes struct {
	CityBrokenId     int64 //! 城池ID
	FightInfoList    []*model.JS_FightInfo
	CityInfoList     []*JS_CityInfo
	CityBrokenServer map[int]*CityBrokenServer
	RetCode          int
}

type RPC_CityBrokenRecordReq struct {
	Action  int
	Uid     int64
	FightId int64
}

type RPC_CityBrokenRecordRes struct {
	Record           *model.CityBrokenRecordInfo
	SimpleRecordList []*model.CityBrokenSimpleRecord
	RetCode          int
}

type RPC_CityBrokenTeamUpdateReq struct {
	Action    int
	ServerId  int
	KeyId     int
	TargetUid int64
	FightInfo []*model.JS_FightInfo
}

type RPC_CityBrokenTeamUpdateRes struct {
	FightInfoList []*model.JS_FightInfo
	CityInfoList  []*JS_CityInfo
	RetCode       int
}

type RPC_ChampionZoneReq struct {
	Uid        int64 //!
	Action     int   //!
	ActType    int   //!
	ServerId   int   //!
	TargetUid  int64 //!
	Param      int
	FightInfos *model.JS_FightInfo
}

type RPC_ChampionZoneRes struct {
	RetCode int //! 返回结果
	Param1  string
	Param2  string
	Param3  string
}

const SIMPLE_CONFIG_TYPE_RUPDATE_ACTIVITY_SERVER = 272

// 苍穹战场 决战空岛 全力对决
// ! 消息主体
type RPC_CrossServer struct {
}

func (self *RPC_CrossServer) UploadRecord(req *RPC_UploadRecordReq, res *RPC_UploadRecordRes) error {
	if !GetRankMgr().InitOk {
		return nil
	}
	GetRecordMgr().UploadRecord(req, res)
	return nil
}

func (self *RPC_CrossServer) UploadRank(req *RPC_UploadRankReq, res *RPC_UploadRankRes) error {
	if !GetRankMgr().InitOk {
		return nil
	}
	GetRankMgr().UploadRank(req, res)
	if req.Notice == model.LOGIC_TRUE {
		GetRankMgr().UploadNotice(req, res)
	}
	return nil
}

func (self *RPC_CrossServer) GetRank(req *RPC_GetRankReq, res *RPC_GetRankRes) error {
	if !GetRankMgr().InitOk {
		return nil
	}
	GetRankMgr().GetRank(req, res)
	return nil
}
func (self *RPC_CrossServer) GetRankReward(req *RPC_GetRankReq, res *RPC_GetRankRes) error {
	if !GetRankMgr().InitOk {
		return nil
	}
	GetRankMgr().GetRankReward(req, res)
	return nil
}

func (self *RPC_CrossServer) GetNotice(req *RPC_GetNoticeReq, res *RPC_GetNoticeRes) error {
	if !GetRankMgr().InitOk {
		return nil
	}
	GetRankMgr().GetNotice(req, res)
	return nil
}

func (self *RPC_CrossServer) UploadServer(req *RPC_RegCrossServerReq, res *RPC_RegCrossServerRes) error {
	GetServerGroupMgr().UploadServer(req, res)
	return nil
}
func (self *RPC_CrossServer) UpdateActivity(req *RPC_UpdateActivityReq, res *RPC_UpdateActivityRes) error {
	serverid := conf.GetSimpleConfigInt(SIMPLE_CONFIG_TYPE_RUPDATE_ACTIVITY_SERVER, 14)
	if req.ServerId != serverid {
		return nil
	}
	GetServerGroupMgr().UpdateActivity(req, res)
	return nil
}
func (self *RPC_CrossServer) GetExpedition(req *RPC_ExpeditionReq, res *RPC_ExpeditionRes) error {
	switch req.Action {
	case EXPEDITION_TIME:
		res.Param1 = GetCrossServerArenaMgr().GetGroupConfig()
	}
	return nil
}
func (self *RPC_CrossServer) ArenaAction(req *RPC_CrossServerArenaReq, res *RPC_CrossServerArenaRes) error {
	switch req.Action {
	case CROSS_SERVER_ACTION_GET_CONFIG:
		res.Param1, res.Param2, res.Param3, res.Param4 = GetCrossServerArenaMgr().GetConfig(req.ServerId)
	case CROSS_SERVER_ACTION_GET_INFO:
		res.RetCode, res.Param1, res.Param2, res.Param3 = GetCrossServerArenaMgr().GetInfo(req.Uid, req.ServerId, req.FightInfos)
	case CROSS_SERVER_ACTION_GET_ENEMY:
		res.RetCode, res.Param1, res.Param2 = GetCrossServerArenaMgr().GetEnemy(req.Uid, req.ServerId)
	case CROSS_SERVER_ACTION_GET_ATTACK_END:
		res.RetCode, res.Param1, res.Param2, res.Param3 = GetCrossServerArenaMgr().AttackEnd(req.Uid, req.ServerId, req.Attack, req.Defend, req.BattleInfo, req.Result, req.FightID)
	case CROSS_SERVER_ACTION_GET_RANK:
		res.RetCode, res.Param1, res.Param2 = GetCrossServerArenaMgr().GetRank(req.Uid, req.ServerId, req.FightInfos)
	case CROSS_SERVER_ACTION_GET_BATTLE_RECORD:
		res.RetCode, res.Param1 = GetCrossServerArenaMgr().GetRecord(req.Uid, req.ServerId, req.Id)
	case CROSS_SERVER_ACTION_LOOK:
		res.RetCode, res.Param1, res.Param2 = GetCrossServerArenaMgr().Look(req.Uid, req.ServerId, req.Id)
	case CROSS_SERVER_ACTION_HALLOFGLORY:
		res.RetCode, res.Param1 = GetServerGroupMgr().HallOfGlory(req.ServerId)
	case CROSS_SERVER_ACTION_HALLOFGLORY_LIKE:
		res.RetCode, res.Param1 = GetServerGroupMgr().HallOfGloryLike(req.ServerId, req.Id)
	case CROSS_SERVER_ACTION_UPDATE_INFO:
		res.RetCode, res.Param1 = GetCrossServerArenaMgr().UpdateInfo(req.Uid, req.ServerId, req.FightInfos)
	case CROSS_SERVER_ACTION_GET_RANK_REWARD:
		GetCrossServerArenaMgr().GameServerGetRankReward(req.ServerId, req.Param)
	}
	return nil
}
func (self *RPC_CrossServer) RankDoLike(req *RPC_RankDoLikeReq, res *RPC_RankDoLikeRes) error {
	if !GetRankMgr().InitOk {
		return nil
	}
	GetRankMgr().RankDoLike(req, res)
	return nil
}
func (self *RPC_CrossServer) DeletePlayerRecord(req *RPC_RankDoLikeReq, res *RPC_RankDoLikeRes) error {
	//跨服排行榜——跨服战力排行榜  //跨服排行榜——伙伴战力排行榜  //港口——紧急悬赏伤害榜
	GetRankMgr().DeletePlayerRecord(req)
	//顶上战争——决战空岛积分榜
	GetCrossServerArenaMgr().DeletePlayerRecord(req)
	//跨服段位赛清理
	GetCrossServerLevelArenaMgr().DeletePlayerRecord(req)
	return nil
}
func (self *RPC_CrossServer) LevelArenaAction(req *RPC_CrossServerLevelArenaReq, res *RPC_CrossServerLevelArenaRes) error {
	switch req.Action {
	case CROSS_SERVER_ACTION_LEVEL_GET_INFO:
		res.RetCode, res.Param1 = GetCrossServerLevelArenaMgr().GetInfo(req.Uid, req.ServerId, req.FightInfos)
	case CROSS_SERVER_ACTION_LEVEL_FIGHT_START:
		res.RetCode, res.Param1, res.Param2 = GetCrossServerLevelArenaMgr().FightStart(req.Uid, req.ServerId, req.FightInfos)
		GetCrossServerLevelArenaMgr().CheckRoll()
	case CROSS_SERVER_ACTION_LEVEL_RANK:
		res.RetCode, res.Param1, res.Param2, res.Param3, res.Param4 = GetCrossServerLevelArenaMgr().GetRank(req.Uid, req.ServerId, req.FightInfos)
	case CROSS_SERVER_ACTION_LEVEL_HALLOFGLORY:
		res.RetCode, res.Param1, res.Param2 = GetServerGroupMgr().LevelHallOfGlory(req.ServerId)
	case CROSS_SERVER_ACTION_LEVEL_HALLOFGLORY_LIKE:
		res.RetCode, res.Param1, res.Param2 = GetServerGroupMgr().LevelHallOfGloryLike(req.ServerId, req.TargetUid, req.Type)
	case CROSS_SERVER_ACTION_LEVEL_GET_BATTLE_RECORD:
		res.RetCode, res.Param1 = GetCrossServerLevelArenaMgr().GetRecord(req.Uid, req.ServerId, req.Id)
	case CROSS_SERVER_ACTION_LEVEL_LOOK:
		res.RetCode, res.Param1, res.Param2 = GetCrossServerLevelArenaMgr().Look(req.Uid, req.ServerId, req.TargetUid)
	case CROSS_SERVER_ACTION_LEVEL_GET_REPLAY_RECORD:
		res.RetCode, res.Param1 = GetCrossServerLevelArenaMgr().GetReplay(req.Uid, req.ServerId, req.Id)
	}
	return nil
}

func (self *RPC_CrossServer) DemonslayerWorldAction(req *RPC_DemonslayerWorldReq, res *RPC_DemonslayerWorldRes) error {
	switch req.Action {
	case DEMONSLAYER_WORLD_ACTION_GET_CONFIG:
		res.Param1 = GetDemonslayerWorldMgr().GetConfig(req.ServerId)
	}
	return nil
}

func (self *RPC_CrossServer) PeakArenaAction(req *RPC_PeakArenaReq, res *RPC_PeakArenaRes) error {
	switch req.Action {
	case PEAK_ACTION_GET_CONFIG:
		res.Param1, res.Param2, res.Param3, res.Param4 = GetPeakArenaMgr().GetConfig(req.ServerId)
	case PEAK_ACTION_GET_INFO:
		res.RetCode, res.Param1, res.Param2, res.Param3 = GetPeakArenaMgr().GetInfo(req.Uid, req.ServerId, req.FightInfos)
	case PEAK_ACTION_GET_ENEMY:
		res.RetCode, res.Param1, res.Param2 = GetPeakArenaMgr().GetEnemy(req.Uid, req.ServerId)
	case PEAK_ACTION_GET_ATTACK_END:
		res.RetCode, res.Param1, res.Param2, res.Param3 = GetPeakArenaMgr().AttackEnd(req.Uid, req.ServerId, req.Attack, req.Defend, req.BattleInfo, req.Result, req.FightID)
	case PEAK_ACTION_GET_RANK:
		res.RetCode, res.Param1, res.Param2, res.Param3 = GetPeakArenaMgr().GetRank(req.Uid, req.ServerId, req.FightInfos)
	case PEAK_ACTION_GET_BATTLE_RECORD:
		res.RetCode, res.Param1 = GetPeakArenaMgr().GetRecord(req.Uid, req.ServerId, req.Id)
	case PEAK_ACTION_LOOK:
		res.RetCode, res.Param1, res.Param2 = GetPeakArenaMgr().Look(req.Uid, req.ServerId, req.Id)
	//case PEAK_ACTION_HALLOFGLORY:
	//	res.RetCode, res.Param1 = GetServerGroupMgr().HallOfGlory(req.ServerId)
	//case PEAK_ACTION_HALLOFGLORY_LIKE:
	//	res.RetCode, res.Param1 = GetServerGroupMgr().HallOfGloryLike(req.ServerId, req.Id)
	case PEAK_ACTION_UPDATE_INFO:
		res.RetCode, res.Param1 = GetPeakArenaMgr().UpdateInfo(req.Uid, req.ServerId, req.FightInfos)
		//case PEAK_ACTION_GET_RANK_REWARD:
		//GetPeakArenaMgr().GameServerGetRankReward(req.ServerId, req.Param)
	}
	return nil
}

func (self *RPC_CrossServer) CosmicArenaAction(req *RPC_CosmicArenaReq, res *RPC_CosmicArenaRes) error {
	switch req.Action {
	case COSMIC_ACTION_GET_INFO:
		res.RetCode, res.Param1 = GetCosmicArenaMgr().GetInfo(req.Uid, req.ServerId, req.FightInfos, req.Camp)
	case COSMIC_ACTION_FIGHT_START:
		res.RetCode, res.Param1, res.Param2 = GetCosmicArenaMgr().FightStart(req.Uid, req.ServerId, req.FightInfos, req.Camp)
		GetCosmicArenaMgr().CheckRoll()
	case COSMIC_ACTION_RANK:
		res.RetCode, res.Param1, res.Param2, res.Param3, res.Param4 = GetCosmicArenaMgr().GetRank(req.Uid, req.ServerId, req.FightInfos, req.Camp)
	//case COSMIC_ACTION_HALLOFGLORY:
	//	res.RetCode, res.Param1, res.Param2 = GetServerGroupMgr().LevelHallOfGlory(req.ServerId)
	//case COSMIC_ACTION_HALLOFGLORY_LIKE:
	//	res.RetCode, res.Param1, res.Param2 = GetServerGroupMgr().LevelHallOfGloryLike(req.ServerId, req.TargetUid, req.Type)
	case COSMIC_ACTION_GET_BATTLE_RECORD:
		res.RetCode, res.Param1 = GetCosmicArenaMgr().GetRecord(req.Uid, req.ServerId, req.Id)
	case COSMIC_ACTION_LOOK:
		res.RetCode, res.Param1, res.Param2 = GetCosmicArenaMgr().Look(req.Uid, req.ServerId, req.TargetUid)
	case COSMIC_ACTION_GET_REPLAY_RECORD:
		res.RetCode, res.Param1 = GetCosmicArenaMgr().GetReplay(req.Uid, req.ServerId, req.Id)
	case COSMIC_ACTION_GET_CONFIG:
		res.Param1, res.Param2, res.Param3, res.Param4, res.Param5 = GetCosmicArenaMgr().GetConfig(req.ServerId)
	}
	return nil
}

func (self *RPC_CrossServer) CityBrokenAction(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) error {
	if GetCityBrokenMgr().CityBrokenInfo.CityBrokenConfig == nil ||
		GetCityBrokenMgr().CityBrokenInfo.CityBrokenConfig.EndTime == 0 {
		res.RetCode = model.LOGIC_TRUE
		return errors.New("activity not open")
	}
	switch req.Action {
	case CENTER_CITYBROKEN_GET_ALL:
		GetCityBrokenMgr().GetCityBrokenAll(req, res)
	case CENTER_CITYBROKEN_GET_SINGLE:
		GetCityBrokenMgr().GetCityBrokenFightInfo(req, res)
	case CENTER_CITYBROKEN_ATTACK_START:
		GetCityBrokenMgr().AttackCityBrokenStart(req, res)
	case CENTER_CITYBROKEN_ATTACK_END:
		GetCityBrokenMgr().AttackCityBrokenEnd(req, res)
	case CENTER_CITYBROKEN_GET_CITY_OWNER:
		GetCityBrokenMgr().GetCityOwner(req, res)
	case CENTER_CITYBROKEN_ATTACK_END_BY_PASS:
		GetCityBrokenMgr().AttackCityBrokenEndByPass(req, res)
	case CENTER_CITYBROKEN_SET_CITY_SHIELD:
		GetCityBrokenMgr().SetCityBrokenShield(req, res)
	//case CENTER_CITYBROKEN_POINT_UPDATE:
	//	GetCityBrokenMgr().UpdateCityBrokenPoint(req, res)
	case CENTER_CITYBROKEN_GET_OWNER_POINT:
		GetCityBrokenMgr().GetOwnerPoint(req, res)

	}
	return nil
}

func (self *RPC_CrossServer) CityBrokenUpdate(req *RPC_CityBrokenTeamUpdateReq, res *RPC_CityBrokenTeamUpdateRes) error {
	switch req.Action {
	case CENTER_CITYBROKEN_UPDATE_TEAM:
		GetCityBrokenMgr().UpdateTeam(req, res)
	}
	return nil
}

func (self *RPC_CrossServer) CityBrokenRecordAction(req *RPC_CityBrokenRecordReq, res *RPC_CityBrokenRecordRes) error {
	switch req.Action {
	case CENTER_CITYBROKEN_RECORD:
		GetCityBrokenMgr().GetCityBrokenFightRecord(req, res)
	case CENTER_CITYBROKEN_RECORD_BY_ID:
		GetCityBrokenMgr().GetCityBrokenFightRecordById(req, res)
	}
	return nil
}

func (self *RPC_CrossServer) ChampionZoneAction(req *RPC_ChampionZoneReq, res *RPC_ChampionZoneRes) error {
	switch req.Action {
	case CHAMPION_ZONE_ACTION_GET_INFO:
		res.RetCode, res.Param1 = GetChampionAllMgr().GetInfo(req.ActType, req.ServerId)
	case CHAMPION_ZONE_ACTION_GET_BETINFO:
		res.RetCode, res.Param1 = GetChampionAllMgr().GetBetInfo(req.Uid, req.ActType, req.ServerId)
	case CHAMPION_ZONE_ACTION_GET_DOLIKE:
		res.RetCode = GetChampionAllMgr().DoLike(req.ActType, req.ServerId, req.TargetUid)
	case CHAMPION_ZONE_ACTION_GET_BET:
		res.RetCode, res.Param1 = GetChampionAllMgr().Bet(req.Uid, req.ActType, req.ServerId, req.TargetUid, req.Param)
	case CHAMPION_ZONE_ACTION_GET_TEAMINFO:
		res.RetCode, res.Param1, res.Param2, res.Param3 = GetChampionAllMgr().GetTeamInfo(req.Uid, req.ActType, req.ServerId, req.Param)
	case CHAMPION_ZONE_ACTION_GET_NOWTEAMID:
		res.RetCode, res.Param1 = GetChampionAllMgr().GetNowTeamId(req.ActType, req.ServerId, req.Param)
	case CHAMPION_ZONE_ACTION_GET_BATTLEINFOUID:
		res.RetCode, res.Param1 = GetChampionAllMgr().GetBattleInfoUid(req.Uid, req.ActType, req.ServerId)
	case CHAMPION_ZONE_ACTION_GET_BATTLERECORD:
		res.RetCode, res.Param1 = GetChampionAllMgr().GetBattleRecord(req.ActType, req.ServerId, req.TargetUid)
	case CHAMPION_ZONE_ACTION_GET_PERIOD:
		res.RetCode, res.Param1 = GetChampionAllMgr().GetPeriod(req.ActType, req.ServerId)
	case CHAMPION_ZONE_ACTION_GET_NOWTOP:
		res.RetCode, res.Param1 = GetChampionAllMgr().GetNowTop(req.ActType, req.ServerId)
	case CHAMPION_ZONE_ACTION_GET_FIGHTINFO:
		res.RetCode, res.Param1 = GetChampionAllMgr().GetFightInfo(req.ActType, req.ServerId, req.TargetUid)
	case CHAMPION_ZONE_ACTION_GET_FIGHTUPDATE:
		res.RetCode, res.Param1 = GetChampionAllMgr().GetFightUpdate(req.ActType, req.ServerId, req.TargetUid)
	case CHAMPION_ZONE_ACTION_GET_SIMPLEINFO:
		res.RetCode, res.Param1 = GetChampionAllMgr().GetSimpleInfo(req.ActType, req.ServerId)
	}
	return nil
}
