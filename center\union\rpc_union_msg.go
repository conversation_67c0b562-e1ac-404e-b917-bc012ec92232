package union

// RPC_CrossUnion 公会远程模块
// ! 消息主体，游戏服从这里获得数据进行处理，结果发到游戏服进行处理
// ! 战斗在游戏服计算，从游戏服发送结果到中心服
type RPC_CrossUnion struct {
}

func (self *RPC_CrossUnion) UnionBattleAction(req *RPC_UnionBattleReq, res *RPC_UnionBattleRes) error {
	switch req.Action {
	case UNION_BATTLE_ACTION_GET_INFO:
		res.RetCode, res.Param1 = GetUnionActivityMgr().RCP_GetInfo(req.ServerId, req.Param)
	case UNION_BATTLE_ACTION_DOLIKE:
		res.RetCode, res.Param1, res.Param2 = GetUnionActivityMgr().DoLike(req.Uid, req.ServerId, req.UnionId, req.TargetUid)
	case UNION_BATTLE_ACTION_GET_HISTORY:
		res.RetCode, res.Param1 = GetUnionActivityMgr().GetHistory(req.ServerId, req.UnionId)
	case UNION_BATTLE_ACTION_GET_NODE:
		res.RetCode, res.Param1, res.Param2 = GetUnionActivityMgr().RPC_GetNode(req.ServerId, req.UnionId, req.TargetUid)
	case UNION_BATTLE_ACTION_ATTACK_START:
		res.RetCode, res.Param1 = GetUnionActivityMgr().AttackStart(req.Uid, req.TargetUid, req.ServerId, req.Param)
	case UNION_BATTLE_ACTION_ATTACK_END:
		res.RetCode, res.Param1 = GetUnionActivityMgr().AttackEnd(req.Uid, req.TargetUid, req.ServerId, req.Param, req.Param1,
			req.FightInfos, req.BattleInfo, req.FightId)
	case UNION_BATTLE_ACTION_DECLARE:
		res.RetCode, res.Param1 = GetUnionActivityMgr().Declare(req.Uid, req.ServerId, req.UnionId, req.TargetUid, req.Action)
	}
	return nil
}

// FetchUnionActivityInfo ! 获取公会战信息
func (self *RPC_CrossUnion) FetchUnionActivityInfo(req RPC_ReqUnionFightInfo, res *RPC_GetUnionFightInfo) {

	res.UnionFight = GetUnionActivityMgr().GetUnionFightByUnionId(req.serverId, req.unionId)
	unionmember := GetUnionMgr().GetUnionMember(req.unionId, req.uid)
	unionFight := GetUnionActivityMgr().UnionActivityInfo

	//var msgRel RPC_UnionActivityInfo
	//msg_ret.Cid = MSG_UNION_ACTIVITY_INFO_HISTORY
	res.Period = unionFight.Period
	res.Stage = unionFight.Stage
	res.StartTime = unionFight.StartTime
	res.AttackTime = unionFight.AttackTime
	res.EndTime = unionFight.EndTime

	res.AttackTimes = unionmember.ActivityAttackTimes
	res.AttackProtectTimes = unionmember.ActivityAttackProtectTimes
	res.StarWin = unionmember.ActivityStarWin
	res.Score = unionmember.ActivityScore

	//msgRel.UnionFight = self.GetUnionFightHistoryByUnionId(player.GetDataInt(core.PLAYER_DATA_UNION_ID))
	//msgRel.AttackTimes = player.GetModule(model.MOD_UNION).(*ModUnion).Sql_UserUnionInfo.ActivityAttackTimes
	//msgRel.AttackProtectTimes = player.GetModule(model.MOD_UNION).(*ModUnion).Sql_UserUnionInfo.ActivityAttackProtectTimes
	//msgRel.StarWin = player.GetModule(model.MOD_UNION).(*ModUnion).Sql_UserUnionInfo.ActivityStarWin
	//msgRel.Score = player.GetModule(model.MOD_UNION).(*ModUnion).Sql_UserUnionInfo.ActivityScore
	/*if nil == msgRel.UnionFight {
	  	msgRel.UnionFight = new(model.JS_UnionFight)
	  	msgRel.UnionFight.Init()
	  } else {
	  	msgRel.UnionFight.Check()
	  }*/
}
