package match

import (
	"fmt"
	json "github.com/bytedance/sonic"
	"master/center/crossserver"
	"master/db"
	"master/model"
	"master/utils"
	"net/http"
	"sort"
	"sync"
)

type Js_GeneralUser struct {
	Uid     int64  `json:"uid"`
	SvrId   int    `json:"svrid"`
	SvrName string `json:"svrname"`
	UName   string `json:"uname"`
	Level   int    `json:"level"`
	Vip     int    `json:"vip"`
	Icon    int    `json:"icon"`
	Point   int    `json:"point"`
	Rank    int    `json:"rank"`
	KeyId   int    `json:"keyid"`
	Time    int64  `json:"time"`
	OpenDay int    `json:"open_day"`
}
type Js_GeneralUserDB struct {
	Uid   int64  `json:"uid"`
	KeyId int    `json:"keyid"`
	SvrId int    `json:"svrid"`
	Info  string `json:"info"`

	info *Js_GeneralUser
	db.DataUpdate
}

func (self *Js_GeneralUserDB) Encode() {
	self.Info = utils.HF_JtoA(self.info)
}

func (self *Js_GeneralUserDB) Decode() {
	json.Unmarshal([]byte(self.Info), &self.info)
}

type GeneralRecord struct {
	Uid        int64  `json:"uid"`
	SvrId      int    `json:"svrid"`
	UName      string `json:"uname"`
	Item       int    `json:"item"`
	Num        int    `json:"num"`
	Time       int64  `json:"time"`
	RecordType int    `json:"recordtype"`
}

type GeneralUserArr []*Js_GeneralUser

func (s GeneralUserArr) Len() int      { return len(s) }
func (s GeneralUserArr) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s GeneralUserArr) Less(i, j int) bool {
	if s[i].Point == s[j].Point {
		return s[i].Time < s[j].Time
	}
	return s[i].Point > s[j].Point
}

type GeneralInfo struct {
	KeyId int `json:"keyid"`

	Mu                    *sync.RWMutex
	generalUserTop        map[int64]*Js_GeneralUserDB
	generalUserTopNodeArr map[int]GeneralUserArr
	generalRecord         map[int][]*GeneralRecord
}

type GeneralMgr struct {
	Locker      *sync.RWMutex
	GeneralInfo map[int]*GeneralInfo
}

var generalMgr *GeneralMgr = nil

func GetGeneralMgr() *GeneralMgr {
	if generalMgr == nil {
		generalMgr = new(GeneralMgr)
		generalMgr.GeneralInfo = make(map[int]*GeneralInfo)
		generalMgr.Locker = new(sync.RWMutex)
	}

	return generalMgr
}

// 存储数据库
func (self *GeneralMgr) OnSave() {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	for _, v := range self.GeneralInfo {
		v.Save()
	}
}

func (self *GeneralInfo) Save() {
	self.Mu.Lock()
	defer self.Mu.Unlock()
	for _, v := range self.generalUserTop {
		v.Encode()
		v.UpdateEx("keyid", v.KeyId)
	}
}

func (self *GeneralMgr) NewGeneralInfo(KeyId int) *GeneralInfo {
	data := new(GeneralInfo)
	data.KeyId = KeyId
	data.Mu = new(sync.RWMutex)
	data.generalUserTop = make(map[int64]*Js_GeneralUserDB)
	data.generalUserTopNodeArr = make(map[int]GeneralUserArr)
	data.generalRecord = make(map[int][]*GeneralRecord)
	return data
}

func (self *GeneralMgr) GetAllData() {
	self.LoadGeneral()
}

func (self *GeneralMgr) LoadGeneral() {
	self.Locker.Lock()
	defer self.Locker.Unlock()

	queryStr := fmt.Sprintf("select uid,keyid,svrid,info from `tbl_generaluser`;")
	var msg Js_GeneralUserDB
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	for i := 0; i < len(res); i++ {
		data := res[i].(*Js_GeneralUserDB)
		if data.KeyId > 0 {
			_, ok := self.GeneralInfo[data.KeyId]
			if !ok {
				self.GeneralInfo[data.KeyId] = self.NewGeneralInfo(data.KeyId)
			}

			if self.GeneralInfo[data.KeyId] == nil {
				continue
			}
			data.Decode()
			if data.info == nil {
				continue
			}

			data.Init("tbl_generaluser", data, true)
			if self.GeneralInfo[data.KeyId].generalUserTop == nil {
				self.GeneralInfo[data.KeyId].generalUserTop = make(map[int64]*Js_GeneralUserDB)
			}
			_, ok3 := self.GeneralInfo[data.KeyId].generalUserTop[data.Uid]
			if !ok3 {
				self.GeneralInfo[data.KeyId].generalUserTop[data.Uid] = data
			}
		}
	}
	for _, data := range self.GeneralInfo {
		data.MakeArr()
	}
}

func (self *GeneralMgr) GetUserGroupSafe(serverId int, openday int, keyid int) int {
	if keyid < 10000 && openday > 0 {
		return openday
	} else {
		if serverId <= 14 {
			return 1
		} else {
			return 2
		}
	}

	//return serverId
}

func (self *GeneralMgr) GetUserGroupNoSafe(serverId int, openday int, keyid int) int {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	if keyid < 10000 && openday > 0 {
		return openday
	} else {
		if serverId <= 14 {
			return 1
		} else {
			return 2
		}
	}

	//return serverId
}

func (self *GeneralMgr) GetUserZoneSafe(serverId int) int {
	return crossserver.GetServerGroupMgr().GetZoneId(serverId)
	//return serverId
}

func (self *GeneralMgr) GetUserZoneNoSafe(serverId int) int {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	return crossserver.GetServerGroupMgr().GetZoneId(serverId)
	//return serverId
}

func (self *GeneralInfo) MakeArr() {
	self.Mu.Lock()
	defer self.Mu.Unlock()

	self.generalUserTopNodeArr = make(map[int]GeneralUserArr, 0)
	for _, v := range self.generalUserTop {
		group := GetGeneralMgr().GetUserGroupSafe(v.SvrId, v.info.OpenDay, self.KeyId)
		self.generalUserTopNodeArr[group] = append(self.generalUserTopNodeArr[group], v.info)
	}
	for _, v := range self.generalUserTopNodeArr {
		sort.Sort(v)
	}

	for _, v := range self.generalUserTopNodeArr {
		for i := 0; i < len(v); i++ {
			v[i].Rank = i + 1
		}
	}
}

func (self *GeneralInfo) MakeArrSafe() {
	self.generalUserTopNodeArr = make(map[int]GeneralUserArr, 0)
	for _, v := range self.generalUserTop {
		group := GetGeneralMgr().GetUserGroupSafe(v.SvrId, v.info.OpenDay, self.KeyId)
		self.generalUserTopNodeArr[group] = append(self.generalUserTopNodeArr[group], v.info)
	}
	for _, v := range self.generalUserTopNodeArr {
		sort.Sort(v)
	}

	for _, v := range self.generalUserTopNodeArr {
		for i := 0; i < len(v); i++ {
			v[i].Rank = i + 1
		}
	}
}

func (self *GeneralMgr) UpdatePoint(req *RPC_GeneralActionReq, res *RPC_GeneralActionRes) {
	if req.SelfInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.Locker.Lock()
	info, ok := self.GeneralInfo[req.SelfInfo.KeyId]
	if !ok {
		info = self.NewGeneralInfo(req.SelfInfo.KeyId)
		self.GeneralInfo[info.KeyId] = info
	}
	self.Locker.Unlock()

	info.UpdatePoint(req, res)
}

func (self *GeneralInfo) UpdatePoint(req *RPC_GeneralActionReq, res *RPC_GeneralActionRes) {
	if req.SelfInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	group := GetGeneralMgr().GetUserGroupSafe(req.SelfInfo.SvrId, req.SelfInfo.OpenDay, self.KeyId)

	self.Mu.Lock()
	defer self.Mu.Unlock()

	info, ok := self.generalUserTop[req.SelfInfo.Uid]
	if ok {
		info.info.UName = req.SelfInfo.UName
		info.info.Level = req.SelfInfo.Level
		info.info.Vip = req.SelfInfo.Vip
		info.info.Icon = req.SelfInfo.Icon
		info.info.SvrId = req.SelfInfo.SvrId
		info.info.SvrName = req.SelfInfo.SvrName
		// 补丁 修复generalUserTopNodeArr没有玩家数据的问题
		if info.info.OpenDay != req.SelfInfo.OpenDay {
			find := false
			for _, value := range self.generalUserTopNodeArr[group] {
				if value.Uid == info.Uid {
					find = true
					break
				}
			}
			//if !find {
			//	self.generalUserTopNodeArr[group] = append(self.generalUserTopNodeArr[group], info.info)
			//}
			//oldgroup := GetGeneralMgr().GetUserGroupSafe(req.SelfInfo.SvrId, info.info.OpenDay, self.KeyId)
			//for i, value := range self.generalUserTopNodeArr[oldgroup] {
			//	if value.Uid == info.Uid {
			//		self.generalUserTopNodeArr[oldgroup] = append(self.generalUserTopNodeArr[oldgroup][:i], self.generalUserTopNodeArr[oldgroup][i+1:]...)
			//		break
			//	}
			//}
			info.info.OpenDay = req.SelfInfo.OpenDay
			if !find {
				self.MakeArrSafe()
			}
		}

		if info.info.Point > req.SelfInfo.Point {
			return
		}
		info.info.Point = req.SelfInfo.Point
		info.info.Time = model.TimeServer().Unix()
	} else {
		dbData := new(Js_GeneralUserDB)
		dbData.info = req.SelfInfo
		dbData.KeyId = self.KeyId
		dbData.SvrId = req.SelfInfo.SvrId
		dbData.Uid = req.SelfInfo.Uid
		dbData.info.Rank = len(self.generalUserTopNodeArr[group]) + 1
		dbData.Encode()
		db.InsertTable("tbl_generaluser", dbData, 0, true)
		dbData.Init("tbl_generaluser", dbData, true)
		self.generalUserTop[req.SelfInfo.Uid] = dbData
		self.generalUserTopNodeArr[group] = append(self.generalUserTopNodeArr[group], dbData.info)
		info = dbData
	}

	for i := info.info.Rank - 2; i >= 0 && i < self.generalUserTopNodeArr[group].Len(); i-- {
		if info.info.Point > self.generalUserTopNodeArr[group][i].Point {
			self.generalUserTopNodeArr[group][i].Rank++
			info.info.Rank--
			self.generalUserTopNodeArr[group].Swap(info.info.Rank-1, self.generalUserTopNodeArr[group][i].Rank-1)
		} else {
			break
		}
	}

	self.generalRecord[group] = append(self.generalRecord[group], req.GeneralRecord...)
	size := len(self.generalRecord[group])
	if size > RECORD_MAX {
		self.generalRecord[group] = self.generalRecord[group][size-RECORD_MAX:]
	}

	res.RetCode = RETCODE_OK
	if len(self.generalUserTopNodeArr[group]) > 50 {
		res.RankInfo = self.generalUserTopNodeArr[group][:50]
	} else {
		res.RankInfo = self.generalUserTopNodeArr[group]
	}
	res.SelfInfo = self.generalUserTop[req.Uid].info
	res.GeneralRecord = self.generalRecord[group]
	return
}

func (self *GeneralMgr) GetAllRank(req *RPC_GeneralActionReqAll, res *RPC_GeneralActionRes) {
	self.Locker.RLock()
	info, ok := self.GeneralInfo[req.KeyId]
	self.Locker.RUnlock()
	if ok {
		info.GetAllRank(req, res)
	}
}

func (self *GeneralInfo) GetAllRank(req *RPC_GeneralActionReqAll, res *RPC_GeneralActionRes) {
	res.RetCode = RETCODE_OK
	group := GetGeneralMgr().GetUserGroupNoSafe(req.ServerId, req.OpenDay, self.KeyId)

	self.Mu.RLock()
	defer self.Mu.RUnlock()
	_, ok := self.generalUserTopNodeArr[group]
	if ok {
		res.RankInfo = self.generalUserTopNodeArr[group]
		res.GeneralRecord = self.generalRecord[group]
	}
}

func (self *GeneralMgr) GmSortRank(w http.ResponseWriter, r *http.Request) {
	self.Locker.Lock()
	defer self.Locker.Unlock()

	stringkeyid := r.FormValue("keyid")
	keyid := utils.HF_Atoi(stringkeyid)

	for _, data := range self.GeneralInfo {
		if keyid != 0 && data.KeyId == keyid {
			data.MakeArr()
			break
		} else if keyid == 0 {
			data.MakeArr()
		}
	}
}
