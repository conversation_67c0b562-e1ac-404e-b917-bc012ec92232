package utils

import (
	"bytes"
	"encoding/base64"
	"encoding/binary"
	"fmt"
	json "github.com/bytedance/sonic"
	"github.com/pierrec/lz4/v4"
)

const (
	LZ4_HEAD      = "lz4 " //这里有个空格  凑4位
	LZ4_HEAD_SIZE = 4      //
	LZ4_LEN_SIZE  = 4      //GetBytesByInt  位数对应
)

func Lz4Encode(v interface{}) string {
	toCompress := HF_JtoB(v)
	compressed := make([]byte, len(toCompress))
	var c lz4.Compressor
	l, err := c.CompressBlock(toCompress, compressed)
	if err != nil {
		fmt.Println(err)
		return string(toCompress)
	}
	if l >= len(toCompress) {
		fmt.Printf("`%s` is not compressible", string(toCompress))
		return string(toCompress)
	}
	if l == 0 {
		return string(toCompress)
	}
	headBytes := GetLz4Head()
	allSize := len(toCompress)
	lenBytes, err := GetBytesByInt(allSize)
	if err != nil {
		LogError("Lz4Encode error:GetBytesByInt")
		return ""
	}

	result := append(headBytes, lenBytes...)
	result = append(result, compressed[:l]...)
	rel := base64.StdEncoding.EncodeToString(result)

	compressed = make([]byte, 0)
	result = make([]byte, 0)
	return rel
}

func GetBytesByInt(value int) ([]byte, error) {
	tmp := int32(value)
	bytesBuffer := bytes.NewBuffer([]byte{})
	err := binary.Write(bytesBuffer, binary.BigEndian, &tmp)
	return bytesBuffer.Bytes(), err
}
func GetIntByBytes(data []byte) (int, error) {
	tmp := int32(0)
	bytesBuffer := bytes.NewBuffer(data)
	err := binary.Read(bytesBuffer, binary.BigEndian, &tmp)
	return int(tmp), err
}

func GetLz4Head() []byte {
	return []byte(LZ4_HEAD)
}

func Lz4Decode(dataBase64 []byte, v interface{}) {
	//判断是否需要解lz4
	if len(dataBase64) <= LZ4_HEAD_SIZE+LZ4_LEN_SIZE {
		json.Unmarshal(dataBase64, v)
		return
	}
	data, err := base64.StdEncoding.DecodeString(string(dataBase64))
	if err != nil {
		json.Unmarshal(dataBase64, v)
		return
	}

	headBytes := data[:LZ4_HEAD_SIZE]
	if string(headBytes) != LZ4_HEAD {
		err := json.Unmarshal(data, v)
		if err != nil {

		}
		return
	}
	lenBytes := data[LZ4_HEAD_SIZE : LZ4_HEAD_SIZE+LZ4_HEAD_SIZE]
	dataBytes := data[LZ4_HEAD_SIZE+LZ4_HEAD_SIZE:]
	//LogDebug("headBytes:", string(headBytes))

	value, err := GetIntByBytes(lenBytes)
	if err != nil {
		json.Unmarshal(data, v)
		return
	}
	decompressed := make([]byte, value)
	_, err = lz4.UncompressBlock(dataBytes, decompressed)
	if err != nil {
		json.Unmarshal(data, v)
		return
	}
	json.Unmarshal([]byte(decompressed), v)
	decompressed = make([]byte, 0)
	return
}
