package csvs

import "master/utils"

var (
	TimeResetConfigArr []*TimeResetConfig
)

type TimeResetConfig struct {
	Id       int     `json:"id"`
	System   int     `json:"system"`
	TimeType int     `json:"timetype"`
	Continue int64   `json:"continue"`
	Cd       int64   `json:"cd"`
	Time     []int64 `json:"time"`
}

type JJCRobotConfig struct {
	Id         int     `json:"id"`
	Type       int     `json:"type"`
	Jjcscore   int     `json:"jjcscore"`
	Jjcclass   int     `json:"jjcclass"`
	Jjcdan     int     `json:"jjcdan"`
	Category   int     `json:"jjccategory"`
	Teamnum    int     `json:"teamnum"`
	Rank1      int     `json:"jjcrankmin" trim:"0"`
	Rank2      int     `json:"jjcrankmax" trim:"0"`
	Name       string  `json:"name"`
	Head       int     `json:"head"`
	MonsterId  int     `json:"Monsterid"`
	NpcLv      []int   `json:"herolv"`
	Level      int     `json:"robotlevel"`
	NpcQuality int     `json:"npcquality"`
	NpcStar    []int   `json:"robotstar"`
	Fight      []int64 `json:"showfight"`
	//MaxFight   int    `json:"showfight1"`
	Hero []int `json:"optionhero"`
	//Arms       []int     `json:"armtype"`
	Hydra      int       `json:"hydraoption"` //! 巨兽
	Rage       int       `json:"rageoption"`
	BaseTypes  []int     `json:"base_type"`
	BaseValues []float64 `json:"base_value"`
}

type RobotInfo struct {
	Hero    int `json:"optionhero"`
	NpcLv   int `json:"herolv"`
	NpcStar int `json:"robotstar"`
}

// 得到一个随机数组
func HF_GetRandomArr(arr []*RobotInfo, num int) []*RobotInfo {
	if len(arr) <= num {
		return arr
	}
	lst := make([]*RobotInfo, 0)
	for len(arr) > 0 && len(lst) < num {
		index := utils.HF_GetRandom(len(arr))
		lst = append(lst, arr[index])
		copy(arr[index:], arr[index+1:])
		arr = arr[:len(arr)-1]
	}
	return lst
}
