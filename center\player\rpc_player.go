/*
@Time : 2020/5/10 11:10
<AUTHOR> 96121
@File : proto_player
@Software: GoLand
*/
package player

import (
	"errors"
	"fmt"
	"master/core"
	"master/model"
	"master/utils"
)

// ! 角色消息主体
type RPC_Player struct {
}

type JS_LookStruct struct {
	Stripe        []*HeroStripe    //! 斑纹
	SpWeapon      []*SpWeaponInfo  //! 专武
	FutureWeapons []*FutureWeapons //! 剑术
}

// ! RPC 角色数据
type RPC_PlayerData_Req struct {
	UId        int64                  //! 角色ID
	Online     int                    //! 在线状态
	Data       *JS_PlayerData         //! 基础数据
	Heros      []*JS_PlayerHero       //! 武将列表
	Equips     [][]*JS_HeroEquip      //! 装备列表，最大5个，可为空
	LifeTree   *model.JS_LifeTreeInfo //! 生命树
	LookStruct string
	//Stripe        []*HeroStripe    //! 斑纹
	//SpWeapon      []*SpWeaponInfo  //! 专武
	//FutureWeapons []*FutureWeapons //! 剑术
}

type RPC_PlayerData_Res struct {
	RetCode int //! 操作结果
	Data    string
}

type RPC_RegInviteCodeReq struct {
	Uid      int64  //! 角色ID
	ServerId int    //! 服务器ID
	Code     string //! 邀请码
	Account  string //! 账号
}

type RPC_RegInviteCodeRes struct {
	RetCode int //! 操作结果
}

type RPC_SetInviteCodeReq struct {
	Code    string //! 邀请码
	Uid     int64  //! 角色ID
	Account string //! 账号
}

type RPC_SetInviteCodeRes struct {
	RetCode  int    //! 操作结果
	Uid      int64  //! 角色ID
	ServerId int    //! 服务器ID
	Code     string //! 邀请码
	Account  string //! 账号
}

// ! 注册用户
func (self *RPC_Player) RegPlayer(req *RPC_PlayerData_Req, res *RPC_PlayerData_Res) error {
	utils.LogDebug("Reg Player :", req.UId, req.Data.UName)
	needSave := false
	player := GetPlayerMgr().GetPlayer(req.UId, true)
	if !utils.IsNil(player) {
		if player.Data.UName == "" {
			needSave = true
		}
		GetDataFromGameServer(player.Data.data, req.Data)
		player.Online = req.Online
		player.Data.UId = req.Data.UId
		player.Data.Level = req.Data.Level
		player.Data.PassId = req.Data.PassId
		player.Data.ServerId = req.Data.ServerId
		player.Data.UName = req.Data.UName
		player.Data.Fight = req.Data.Fight
		player.Data.LoginTime = req.Data.LoginTime
		player.Data.RegTime = req.Data.RegTime
		player.Data.LastUpdate = model.TimeServer().Unix()
		player.Data.data = req.Data
		player.Data.heros = req.Heros
		player.Data.equips = req.Equips
		player.Data.lifetree = req.LifeTree
		data := new(JS_LookStruct)
		utils.Lz4Decode([]byte(req.LookStruct), data)
		player.Data.stripe = data.Stripe
		player.Data.spWeapon = data.SpWeapon
		player.Data.futureWeapons = data.FutureWeapons

		if needSave == false {
			player.Save()
		} else {
			player.onSave(needSave)
		}

		//player.DataFriend.UpdateData()
	}
	return nil
}

// ! 载入新玩家
func (self *RPC_Player) GetPlayer(uid int64, res *RPC_PlayerData_Req) error {
	utils.LogDebug("Get Player :", uid)

	player := GetPlayerMgr().GetPlayer(uid, true)
	if !utils.IsNil(player) && player.Data.data != nil {
		if player.Data.data.LastUpdate == 0 {
			player.Data.data.LastUpdate = model.TimeServer().Unix()
		}
		res.UId = player.GetUid()
		res.Online = player.Online
		res.Data = player.Data.data
		res.Heros = player.Data.heros
		res.Equips = player.Data.equips
		res.LifeTree = player.Data.lifetree
		data := new(JS_LookStruct)
		data.Stripe = player.Data.stripe
		data.SpWeapon = player.Data.spWeapon
		data.FutureWeapons = player.Data.futureWeapons
		res.LookStruct = utils.Lz4Encode(data)

		// 公会数据
		//myunion := union.GetUnionMgr().GetUnion(player.Data.data.UnionId)
		//if myunion != nil {
		//	member := myunion.GetMember(player.GetUid())
		//	if nil != member {
		//		res.Data.UnionName = myunion.Unionname
		//		res.Data.Position = member.Position
		//		res.Data.BraveHand = member.BraveHand
		//	}
		//}

		return nil
	}

	return errors.New(fmt.Sprintf("Player [%d] is nil .", uid))
}

// ! 载入新玩家
func (self *RPC_Player) GetPlayerArena(uid int64, res *RPC_PlayerData_Res) error {
	utils.LogDebug("GetPlayerArena :", uid)

	GetPlayerMgr().GetPlayerArena(uid, res)
	if res.RetCode != RETCODE_OK {
		return errors.New(fmt.Sprintf("GetPlayerArena .", uid))
	}

	return nil
}

// ! 载入新玩家
func (self *RPC_Player) RegInviteCode(req *RPC_RegInviteCodeReq, res *RPC_RegInviteCodeRes) error {
	ret, err := GetReturnEventMgr().RegInviteCode(req)
	res.RetCode = ret
	return err
}

func (self *RPC_Player) SetInviteCode(req *RPC_SetInviteCodeReq, res *RPC_SetInviteCodeRes) error {
	err, data := GetReturnEventMgr().GetInviteCode(req.Code)
	if err != nil {
		res.RetCode = 4
		return err
	}

	if data == nil {
		res.RetCode = 4
		return err
	}

	if data.Account == req.Account {
		res.RetCode = 5
		return err
	}

	core.GetCenterApp().AddEvent(data.ServerId, core.CROSS_SERVER_RETURN_EVENT_LINK, data.Uid, req.Uid, 0, "")
	res.RetCode = 0
	res.Uid = data.Uid
	res.ServerId = data.ServerId
	res.Code = data.Code
	res.Account = data.Account
	return nil
}

// 玩家数据同步，因为部分逻辑改到中心服，所以需要筛选出可同步数据
func GetDataFromGameServer(oldData *JS_PlayerData, srcData *JS_PlayerData) {
	oldData.UId = srcData.UId
	oldData.UName = srcData.UName
	oldData.Level = srcData.Level
	oldData.Fight = srcData.Fight
	oldData.PassId = srcData.PassId
	oldData.ServerId = srcData.ServerId
	oldData.Sex = srcData.Sex
	oldData.IconId = srcData.IconId
	oldData.Portrait = srcData.Portrait
	oldData.Vip = srcData.Vip
	oldData.RegTime = srcData.RegTime
	oldData.LoginTime = srcData.LoginTime
	oldData.LastUpdate = srcData.LastUpdate
	oldData.FightInfo = srcData.FightInfo
	oldData.Position = srcData.Position
	oldData.BraveHand = srcData.BraveHand
	oldData.UserSignature = srcData.UserSignature
	oldData.UserID = srcData.UserID
}
