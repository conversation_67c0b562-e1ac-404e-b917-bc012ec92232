// Code generated by protoc-gen-go.
// source: msgbase.proto
// DO NOT EDIT!

package network

import proto "github.com/golang/protobuf/proto"
import fmt "fmt"
import math "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

type MsgBase struct {
	Msghead          *string `protobuf:"bytes,1,req,name=msghead" json:"msghead,omitempty"`
	Msgtime          *int64  `protobuf:"varint,2,opt,name=msgtime" json:"msgtime,omitempty"`
	Msgsign          *string `protobuf:"bytes,3,opt,name=msgsign" json:"msgsign,omitempty"`
	Msgdata          []byte  `protobuf:"bytes,4,opt,name=msgdata" json:"msgdata,omitempty"`
	XXX_unrecognized []byte  `json:"-"`
}

func (m *MsgBase) Reset()                    { *m = MsgBase{} }
func (m *MsgBase) String() string            { return proto.CompactTextString(m) }
func (*MsgBase) ProtoMessage()               {}
func (*MsgBase) Descriptor() ([]byte, []int) { return fileDescriptor2, []int{0} }

func (m *MsgBase) GetMsghead() string {
	if m != nil && m.Msghead != nil {
		return *m.Msghead
	}
	return ""
}

func (m *MsgBase) GetMsgtime() int64 {
	if m != nil && m.Msgtime != nil {
		return *m.Msgtime
	}
	return 0
}

func (m *MsgBase) GetMsgsign() string {
	if m != nil && m.Msgsign != nil {
		return *m.Msgsign
	}
	return ""
}

func (m *MsgBase) GetMsgdata() []byte {
	if m != nil {
		return m.Msgdata
	}
	return nil
}

func init() {
	proto.RegisterType((*MsgBase)(nil), "protobuf.MsgBase")
}

var fileDescriptor2 = []byte{
	// 104 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x09, 0x6e, 0x88, 0x02, 0xff, 0xe2, 0xe2, 0xcd, 0x2d, 0x4e, 0x4f,
	0x4a, 0x2c, 0x4e, 0xd5, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0xe2, 0x00, 0x53, 0x49, 0xa5, 0x69,
	0x4a, 0xbe, 0x5c, 0xec, 0xbe, 0xc5, 0xe9, 0x4e, 0x40, 0x29, 0x21, 0x7e, 0x2e, 0x76, 0xa0, 0xaa,
	0x8c, 0xd4, 0xc4, 0x14, 0x09, 0x46, 0x05, 0x26, 0x0d, 0x4e, 0xa8, 0x40, 0x49, 0x66, 0x6e, 0xaa,
	0x04, 0x93, 0x02, 0xa3, 0x06, 0x33, 0x54, 0xa0, 0x38, 0x33, 0x3d, 0x4f, 0x82, 0x19, 0x28, 0x00,
	0x53, 0x91, 0x92, 0x58, 0x92, 0x28, 0xc1, 0x02, 0x14, 0xe0, 0x01, 0x04, 0x00, 0x00, 0xff, 0xff,
	0x25, 0xcf, 0x6a, 0x6a, 0x68, 0x00, 0x00, 0x00,
}
