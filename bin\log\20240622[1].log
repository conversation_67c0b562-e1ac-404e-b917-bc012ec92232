19:06:58.968804 app_master.go:113: [debug ] 连接数据库...
19:06:58.979804 dbserver.go:60: [debug ] db connect! ds_master_001
19:06:58.980804 dbserver.go:60: [debug ] db connect! ds_master_log_001
19:06:59.016538 app_master.go:117: [debug ] 启动逻辑处理...
19:06:59.017051 app_master.go:128: [debug ] 注册并启动服务...
19:06:59.018079 mgr_tower.go:150: [info  ] 迁移数据： san_towerbattleinfo 0
19:06:59.018614 mgr_tower.go:177: [info  ] san_towerbattleinfo 迁移数据OK
19:08:00.196392 mgr_gamedata.go:93: [debug ] SQL BASE EXEC: update `tbl_servergroup` set `activityinfo`='{"hallofglorys":[],"levelarenahallofglorys":null,"levelarenahallofglorysself":null}' where `serverid`=1 limit 1
19:08:00.198977 mgr_gamedata.go:93: [debug ] SQL BASE EXEC: update `tbl_servergroupconfig` set `starttime`=1717984800,`centretime`=1718571600,`endtime`=1719176400,`crossserverplayconfig`='{"ArenaConfig":{"StartTime":1718589600,"RewardTime":1719176400,"EndTime":1719176400,"Reward":0,"Periods":3},"LevelArenaConfig":{"StartTime":1718589600,"RewardTime":1719176400,"EndTime":1719176400,"Reward":0,"Periods":3}}' where `id`=1 limit 1
