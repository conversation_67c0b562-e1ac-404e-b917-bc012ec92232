package union

import "master/model"

const (
	UNION_BATTLE_ACTION_GET_INFO      = 1  //! 获得数据
	UNION_BATTLE_ACTION_DOLIKE        = 2  //! 点赞
	UNION_BATTLE_ACTION_GET_HISTORY   = 3  //! 点赞
	UNION_BATTLE_ACTION_GET_NODE      = 4  //! 获取节点
	UNION_BATTLE_ACTION_ATTACK_START  = 5  //! 攻击开始
	UNION_BATTLE_ACTION_ATTACK_END    = 7  //! 攻击结束
	UNION_BATTLE_ACTION_DECLARE       = 8  //! 宣告
	UNION_BATTLE_ACTION_ATTACK_KILL   = 9  //! 吊打
	UNION_BATTLE_ACTION_MASTER_NOTICE = 10 //! 会长-公会战公告
	UNION_BATTLE_ACTION_UPLOAD_Member = 11 //! 上传公会角色数据
	UNION_BATTLE_ACTION_UPDATE_NODE   = 12 //! 更新节点数据
)

type RPC_ReqUnionFightInfo struct {
	unionId  int   `json:"unionid""`
	serverId int   `json:"serverid"`
	uid      int64 `json:"uid"`
}

type RPC_UnionFightStatus struct {
	Stage      int   `json:"stage"`      //工会战阶段
	StartTime  int64 `json:"starttime"`  //开始时间
	AttackTime int64 `json:"attacktime"` //进攻时间
	EndTime    int64 `json:"endtime"`    //结束时间
}

type RPC_GetUnionFightInfo struct {
	Cid                string         `json:"cid"`
	Period             int            `json:"period"`             //场次
	Stage              int            `json:"stage"`              //工会战阶段
	StartTime          int64          `json:"starttime"`          //开始时间
	AttackTime         int64          `json:"attacktime"`         //进攻时间
	EndTime            int64          `json:"endtime"`            //结束时间
	UnionFight         *JS_UnionFight `json:"unionfight"`         //公会战信息
	AttackTimes        int            `json:"attacktimes"`        //已攻击次数
	AttackProtectTimes int            `json:"attackprotecttimes"` //已使用保护次数
	StarWin            int            `json:"starwin"`            //个人获取星数
	Score              int            `json:"score"`              //个人积分
}

// RPC_UnionBattleReq ! 公会战事件请求
type RPC_UnionBattleReq struct {
	Uid        int64 //!
	Action     int   //!
	ActType    int   //! 活动Id
	ServerId   int   //! 服务器Id
	UnionId    int   //! 公会Id
	TargetUid  int64 //! 目标UID
	Param      int   //! 默认参数
	Param1     int   //! 扩展参数
	FightId    int64 //! 战报Id
	FightInfos *model.JS_FightInfo
	BattleInfo *model.BattleInfo
}

// RPC_UnionBattleRes ! 公会战事件返回
type RPC_UnionBattleRes struct {
	RetCode int //! 返回结果
	Param1  string
	Param2  string
	Param3  string
}
