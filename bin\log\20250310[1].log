20:00:39.595508 app_master.go:119: [debug ] 连接数据库...
20:00:39.598275 dbserver.go:60: [debug ] db connect! ds_master_001
20:00:39.599040 dbserver.go:60: [debug ] db connect! ds_master_log_001
20:00:39.640338 app_master.go:123: [debug ] 启动逻辑处理...
20:00:39.640338 app_master.go:134: [debug ] 注册并启动服务...
20:00:39.640857 mgr_tower.go:150: [info  ] 迁移数据： san_towerbattleinfo 0
20:00:39.640857 mgr_tower.go:177: [info  ] san_towerbattleinfo 迁移数据OK
20:01:40.868222 mgr_gamedata.go:93: [debug ] SQL BASE EXEC: update `tbl_serverarea` set `areaid`=1 where `serverid`=1 limit 1
20:01:40.869778 mgr_gamedata.go:93: [debug ] SQL BASE EXEC: update `tbl_servergroupconfig` set `starttime`=1740949200,`centretime`=1741536000,`endtime`=1742140800,`crossserverplayconfig`='{"ArenaConfig":{"StartTime":1741554000,"RewardTime":1742140800,"EndTime":1742140800,"Reward":0,"Periods":4},"LevelArenaConfig":{"StartTime":1741554000,"RewardTime":1742140800,"EndTime":1742140800,"Reward":0,"Periods":4}}' where `id`=1 limit 1
20:01:40.871863 mgr_gamedata.go:93: [debug ] SQL BASE EXEC: update `tbl_serverareaconfig` set `starttime`=1740949200,`endtime`=1742140800,`areamax`=1 where `id`=1 limit 1
20:02:41.813330 mgr_gamedata.go:93: [debug ] SQL BASE EXEC: update `tbl_serverarea` set `areaservers`='{"IsAuto":0,"ServerId":[1]}',`lastareaservers`='null',`param`='{"opentime":1681160400,"activecount":0,"maxfightname":"3234"}',`areaactivityinfo`='{}' where `serverid`=1 limit 1
