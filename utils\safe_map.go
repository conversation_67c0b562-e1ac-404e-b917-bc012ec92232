package utils

import (
	json "github.com/bytedance/sonic"
	"sync"
)

type SafeMapInt struct {
	locker sync.RWMutex
	data   map[int]*interface{}
}

func NewSafeMapInt() *SafeMapInt {
	return &SafeMapInt{
		data: make(map[int]*interface{}),
	}
}

func (self *SafeMapInt) Load(key int) (interface{}, bool) {
	if v, ok := self.data[key]; ok {
		return v, ok
	}

	return nil, false
}

func (self *SafeMapInt) Store(key int, value interface{}) {
	self.locker.Lock()
	defer self.locker.Unlock()

	self.data[key] = &value
}

func (self *SafeMapInt) Range(f func(key, value interface{}) bool) {
	self.locker.RLock()
	defer self.locker.RUnlock()

	for k, v := range self.data {
		if !f(k, v) {
			break
		}
	}
}

func (self *SafeMapInt) Decode(data string) bool {
	self.locker.Lock()
	defer self.locker.Unlock()

	err := json.Unmarshal([]byte(data), &self.data)
	if err != nil {
		return false
	}
	return true
}

func (self *SafeMapInt) Encode() ([]byte, error) {
	self.locker.Lock()
	defer self.locker.Unlock()

	return json.Marshal(self.data)
}

type SafeMapInt64 struct {
	locker sync.RWMutex
	data   map[int64]interface{}
}

func NewSafeMapInt64() *SafeMapInt64 {
	return &SafeMapInt64{
		data: make(map[int64]interface{}),
	}
}

func (self *SafeMapInt64) Load(key int64) (interface{}, bool) {
	if v, ok := self.data[key]; ok {
		return v, ok
	}

	return nil, false
}

func (self *SafeMapInt64) Store(key int64, value interface{}) {
	self.locker.Lock()
	defer self.locker.Unlock()

	self.data[key] = value
}

func (self *SafeMapInt64) Range(f func(key, value interface{}) bool) {
	self.locker.RLock()
	defer self.locker.RUnlock()

	for k, v := range self.data {
		if !f(k, v) {
			break
		}
	}
}

func (self *SafeMapInt64) Decode(data string) bool {
	self.locker.Lock()
	defer self.locker.Unlock()

	err := json.Unmarshal([]byte(data), &self.data)
	if err != nil {
		return false
	}
	return true
}

type SafeMapIntInt struct {
	locker sync.RWMutex
	data   map[int]int
}

func NewSafeMapIntInt() *SafeMapIntInt {
	return &SafeMapIntInt{
		data: make(map[int]int),
	}
}

func (self *SafeMapIntInt) Load(key int) (interface{}, bool) {
	if v, ok := self.data[key]; ok {
		return v, ok
	}

	return nil, false
}

func (self *SafeMapIntInt) Store(key int, value int) {
	self.locker.Lock()
	defer self.locker.Unlock()

	self.data[key] = value
}

func (self *SafeMapIntInt) Range(f func(key, value int) bool) {
	self.locker.RLock()
	defer self.locker.RUnlock()

	for k, v := range self.data {
		if !f(k, v) {
			break
		}
	}
}

func (self *SafeMapIntInt) Decode(data string) bool {
	self.locker.Lock()
	defer self.locker.Unlock()

	err := json.Unmarshal([]byte(data), &self.data)
	if err != nil {
		return false
	}
	return true
}

func (self *SafeMapIntInt) Encode() ([]byte, error) {
	self.locker.Lock()
	defer self.locker.Unlock()

	return json.Marshal(self.data)
}
