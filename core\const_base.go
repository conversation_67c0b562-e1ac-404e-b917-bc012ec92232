package core

const (
	TIME_RESET_SYSTEM_NEWPIT        = 26 //
	TIME_RESET_SYSTEM_UNIONACTIVITY = 74 //
	TIME_RESET_SYSTEM_CHAMPION      = 80 //
	TIME_RESET_SYSTEM_NEWBOSS       = 85 //
	TIME_RESET_SYSTEM_NEWBOSS2      = 88 //
)

// 其他
const (
	DATEFORMAT    = "2006-01-02 15:04:05" // 时间格式化
	POWERMAX      = 1500                  // 最大体力限制
	SKILLPOINTMAX = 100                   // 最大技能点限制
	LEVELMAX      = 220                   // 最大等级限制80-90
	LEVELVIPMAX   = 17                    // vip最大等级
	ADDPOWERTIME  = 300                   // 体力回复时间(秒)
	ADDSPTIME     = 20                    // 技能点回复时间(秒)

	DEFAULT_JJC_WORSHIP_MONEY = 2000

	DAY_SECS      = 86400
	HALF_DAY_SECS = 43200
	HOUR_SECS     = 3600
	MIN_SECS      = 60

	CAMP_SHU        = 1 // 帝国
	CAMP_WEI        = 2 // 联邦
	CAMP_WU         = 3 // 圣堂
	CAMP_QUN        = 4
	CITY_SHU        = 1104
	CITY_WEI        = 1059
	CITY_WU         = 1018
	FIGHTTYPE_DEF   = -200    // 城防军
	FIGHTTYPE_ROBOT = -500    // 机器人
	GM_ACCOUNT_ID   = 1000000 // 正常GM账号 = 10000，压测帐号放开限制

	SHOP_GENERAL     = 1  // 杂货-勋章
	SHOP_UNION       = 2  // 军团贡献
	SHOP_BOX         = 5  // 宝箱-斗技 神器
	SHOP_HONOR       = 6  // 荣誉 功勋 国库 商行
	SHOP_MAGICALSHOP = 7  // 无双商店
	SHOP_EXPEDITION  = 9  // 远征 秘境
	SHOP_TOWER       = 10 // 镇魂塔
	SHOP_DINIVITY    = 12 // 神格商店

	SHOP_NEW_NORMAL           = 1  //普通商店
	SHOP_NEW_PVP              = 2  //高阶竞技场商店
	SHOP_NEW_HERO             = 3  //英雄商店
	SHOP_NEW_RUINS            = 4  //遗迹商店
	SHOP_NEW_UNION            = 5  //公会商店
	SHOP_NEW_ABYSS            = 6  //深渊商店
	SHOP_NEW_SECRET_TERRITORY = 7  //秘境商店
	SHOP_NEW_SEGMENT_POSITION = 8  //段位
	SHOP_NEW_CROSS_SERVER     = 9  //跨服
	SHOP_NEW_KING             = 10 //王者
	SHOP_NEW_OVERARCHING_SKY  = 11 //苍穹
	SHOP_NEW_CAMP             = 12 //阵营
	SHOP_NEW_ECONOMICS        = 13 //勇者
	SHOP_NEW_EXPEDITION_1     = 14 //
	SHOP_NEW_EXPEDITION_2     = 15 //
	SHOP_NEW_PIT              = 16 //
	SHOP_NEW_FUTURE           = 17
	SHOP_NEW_ROYAL            = 18 //贵族商店
	SHOP_NEW_CROSS_ARENA      = 19 //擂台商店
	SHOP_NEW_GLORY            = 20 //荣耀商店
	SHOP_NEW_CITYBROKEN       = 21 //千城破
	SHOP_NEW_PIT_PROCESS      = 99 //远征商店 提审

	MAX_HORSE_AWAKEN_LEVEL = 7 // 最高等级

	ACTIVITY_STATUS_CLOSED = 0 // 活动关闭
	ACTIVITY_STATUS_OPEN   = 1 // 活动开启
	ACTIVITY_STATUS_SHOW   = 2 // 活动结束，可领奖，不可完成, 发送奖励

	MAX_UNION_DONATION = 6000 // 军团相关

	MAX_GENERAL_RANK = 50 // 最大神将排名

	MAX_CITY_POWER      = 150 // 军令上限
	MAX_CITY_POWER_TIME = 720 // 军令恢复时间

	MaxRankNum           = 1000 // 排行榜最大数量
	MaxRankShowNum       = 50   // 排行榜显示数量
	MaxAcientRankShowNum = 100
	ActRankNum           = 20 // 排行榜活动显示数量

	GVGNEED_ITEM_NUM  = 5
	GVGNEED_ITEM_DESC = "GVG消耗"

	FightAtt = 99

	DEFAULT_HEAD_ICON         = 1000
	DEFAULT_HEAD_ICON_PROCESS = 9100001

	MaxOnlineRankNum = 9 // 排行榜中在线的最大数量
)

const (
	LOGIC_FALSE = 0 //未开启自动分解
	LOGIC_TRUE  = 1
	LOGIC_ALL   = 2
)

const (
	ATTACK_WIN  = 1
	DEFENCE_WIN = 2
	ALL_LOSE    = 3 //公会战中存在双方都判定为输的情况
)

const (
	POS_ATTACK  = 0
	POS_DEFENCE = 1

	POS_INVALID = -1
)

const (
	UNIONACTIVITY_NODE_MAX_NUM = 20 //无效状态
	UNIONACTIVITY_STAR_MAX     = 3
	UNIONACTIVITY_PROTECT_MAX  = 1
	UNIONACTIVITY_ATTACK_MAX   = 1 //
	UNIONACTIVITY_LIKE_RANK    = 3 //前三名可以被点赞
)

const (
	UNIONACTIVITY_STAGE_INVALID = 0 //无效状态
	UNIONACTIVITY_STAGE_WAIT    = 1 //备战阶段
	UNIONACTIVITY_STAGE_ATTACK  = 2 //进攻阶段
	UNIONACTIVITY_STAGE_RESULT  = 3 //结算
)

const (
	BATTLE_INFO_ARENA          = "san_arenabattleinfo"
	BATTLE_INFO_CHAMPION       = "san_arenabattleinfo"
	BATTLE_INFO_UNION_ACTIVITY = "san_arenabattleinfo"
	BATTLE_INFO_TOWER          = "san_arenabattleinfo"
	BATTLE_INFO_TEAMSLAY       = "san_arenabattleinfo"
)

const (
	BATTLE_TYPE_PVP  = 1 //
	BATTLE_TYPE_PVE  = 2 //
	BATTLE_TYPE_BOSS = 3 //
)

type PassItem struct {
	ItemID int `json:"itemid"` // 道具ID
	Num    int `json:"num"`    // 道具数量
}

// ! 角色属性
const (
	_                    = iota
	PLAYER_DATA_UID      //! 角色Id
	PLAYER_DATA_LEVEL    //! 等级
	PLAYER_DATA_EXP      //! 角色经验
	PLAYER_DATA_ICONID   //! 头像
	PLAYER_DATA_GOLD     //! 金币
	PLAYER_DATA_GEM      //! 元宝
	PLAYER_DATA_UNAME    //! 昵称
	PLAYER_DATA_VIP      //! VIP等级
	PLAYER_DATA_VIP_EXP  //! VIP经验
	PLAYER_DATA_ENERGY   //! 体力
	PLAYER_DATA_PORTRAIT //! 头像框
	PLAYER_DATA_FIGHT    //! 战力
	PLAYER_DATA_UNION_ID
	PLAYER_DATA_UNION_NAME
	PLAYER_DATA_SERVER_ID
	PLAYER_DATA_REG_TIME
	PLAYER_DATA_CHECK_CODE
	PLAYER_DATA_RELOGIN_TIMES //! 重复登陆测试
	PLAYER_DATA_CAMP          //！阵营
	PLAYER_DATA_PASSMAX       //！
	PLAYER_DATA_FACE
	PLAYER_DATA_LASTLOGINTIME
	PLAYER_DATA_USERSIGNATURE      //签名
	PLAYER_DATA_USERID             //sdk ID
	PLAYER_DATA_ACCOUNT            //
	PLAYER_DATA_IP                 //
	PLAYER_DATA_LASTLIVETIME       //
	PLAYER_DATA_ISGAG              //
	PLAYER_DATA_NOTICECHATTIME     //
	PLAYER_DATA_PAYGEM             //! 点劵，付费元宝
	PLAYER_DATA_TITLE              //! 称号
	PLAYER_DATA_UNION_CONTRIBUTION //公会贡献值
	PLAYER_DATA_UNION_WEEK_CONTRIBUTION
	PLAYER_DATA_LANGUAGE        //! 语言
	PLAYER_DATA_LASTCHECKINTIME //
	PLAYER_DATA_WARSHIP         //战船
	PLAYER_DATANEED_UPDATE      //更新战力
	PLAYER_DATA_FAME_ID         //名望
	PLAYER_DATA_CHAT_FRAME      //聊天框
)
