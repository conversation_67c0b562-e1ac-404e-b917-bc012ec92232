package crossserver

import (
	"fmt"
	"log"
	"master/center/server"
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
	"sort"
	"sync"
	"time"
)

const (
	TABLE_PEAK_ARENA             = "tbl_peakarena"
	PEAK_ARENA_SCORE_START       = 1000
	PEAK_ARENA_TEAM_MAX          = 3
	PEAK_ARENA_ENEMY_CHOOSE_LIST = 10  //对手备选数量，提高效率
	PEAK_ARENA_RANK_MAX          = 100 //
)

type lstPeakArenaRank []*PeakArenaUser

func (s lstPeakArenaRank) Len() int      { return len(s) }
func (s lstPeakArenaRank) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s lstPeakArenaRank) Less(i, j int) bool {
	if s[i].IsSign == s[j].IsSign {
		if s[i].Score == s[j].Score {
			return s[i].Uid < s[j].Uid
		} else {
			return s[i].Score >= s[j].Score
		}
	}
	return s[i].IsSign >= s[j].IsSign
}

type lstPeakArenaServerRank []*PeakArenaServer

func (s lstPeakArenaServerRank) Len() int      { return len(s) }
func (s lstPeakArenaServerRank) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s lstPeakArenaServerRank) Less(i, j int) bool {
	if s[i].Score == s[j].Score {
		return s[i].SvrId < s[j].SvrId
	}
	return s[i].Score > s[j].Score
}

type PeakArenaDB struct {
	Id         int    `json:"id"`
	Uid        int64  `json:"uid"`
	Periods    int    `json:"periods"`
	GroupId    int    `json:"groupid"`
	SvrId      int    `json:"svrid"`
	Info       string `json:"info"`
	FightInfos string `json:"fightinfos"`

	info *PeakArenaUser
	//fightInfos []*core.JS_FightInfo  //优化 这个字段干掉 节省内存空间
	db.DataUpdate
}

type PeakArenaUser struct {
	Uid        int64                   `json:"uid"`
	SvrId      int                     `json:"svrid"`
	SvrName    string                  `json:"svrname"`
	UName      string                  `json:"uname"`
	UnionName  string                  `json:"unionname"`
	Score      int                     `json:"score"`
	ScoreLv    int                     `json:"scorelv"`
	RankPos    int                     `json:"rankpos"`
	Level      int                     `json:"level"`
	Vip        int                     `json:"vip"`
	Icon       int                     `json:"icon"`
	Portrait   int                     `json:"portrait"`
	Title      int                     `json:"title"`
	Fight      int64                   `json:"fight"`
	Robot      int                     `json:"robot"`
	ArenaFight []*model.PeakArenaFight `json:"arenafight"` //战报集
	CantList   map[int64]int           `json:"-"`
	IsSign     int                     `json:"issign"` //是否正式参赛
	HeroId     int                     `json:"heroid"` //! 给客户端显示半身像
}

type PeakArenaServer struct {
	SvrId   int    `json:"svrid"`
	SvrName string `json:"svrname"`
	Score   int    `json:"score"`
	RankPos int    `json:"rankpos"`
}

type PeakArenaMgr struct {
	Mu             *sync.RWMutex
	PeakArenaInfo  map[int]*PeakArenaInfo //key: group
	JJCRobotConfig []*JJCRobotConfig
	RecordId       int64
}

type PeakArenaInfo struct {
	groupId    int
	periods    int
	rankInfo   []*PeakArenaUser       //排行数据    key:rank
	serverInfo []*PeakArenaServer     //排行数据    key:rank
	db_list    map[int64]*PeakArenaDB //数据存储
	Locker     *sync.RWMutex
}

var peakArenaMgr *PeakArenaMgr = nil

// 巅峰竞技场 copy的苍穹战场 改成3x5v 3x5
func GetPeakArenaMgr() *PeakArenaMgr {
	if peakArenaMgr == nil {
		peakArenaMgr = new(PeakArenaMgr)
		peakArenaMgr.PeakArenaInfo = make(map[int]*PeakArenaInfo)
		peakArenaMgr.Mu = new(sync.RWMutex)
		peakArenaMgr.LoadCsv()
	}
	return peakArenaMgr
}

func GetRankInfoPeakArena(data *PeakArenaUser) *model.RankInfo {
	rel := new(model.RankInfo)
	if data == nil {
		return rel
	}
	rel.Uid = data.Uid
	rel.SvrId = data.SvrId
	rel.UName = data.UName
	rel.Level = data.Level
	rel.Vip = data.Vip
	rel.Icon = data.Icon
	rel.Portrait = data.Portrait
	rel.Title = data.Title
	rel.Fight = data.Fight
	rel.Num = int64(data.Score)
	rel.UnionName = data.UnionName
	if data.IsSign == model.LOGIC_TRUE {
		rel.Rank = data.RankPos
	}
	rel.HeroId = data.HeroId
	return rel
}

func (self *PeakArenaInfo) GetRobotUser(config *JJCRobotConfig) *PeakArenaUser {
	data := new(PeakArenaUser)
	data.Uid = int64(config.Jjcscore)
	return data
}

func (self *PeakArenaMgr) Run() {
	//! 定时逻辑
	ticker := time.NewTicker(time.Hour * 1)
	for {
		<-ticker.C
		self.OnTimer()
	}

	ticker.Stop()
}

func (self *PeakArenaMgr) OnTimer() {
	self.Mu.Lock()
	defer self.Mu.Unlock()
	for _, serverGroup := range self.PeakArenaInfo {
		serverGroup.CountAllScore(false)
	}
}

func (self *PeakArenaMgr) LoadCsv() {
	JJCRobotConfigTemp := make([]*JJCRobotConfig, 0)
	utils.GetCsvUtilMgr().LoadCsv("Jjc_Robot", &JJCRobotConfigTemp)
	for _, v := range JJCRobotConfigTemp {
		if v.Type != 7 {
			continue
		}
		self.JJCRobotConfig = append(self.JJCRobotConfig, v)
	}
	return
}

// Lz4...
func (self *PeakArenaDB) Encode() {
	//self.Info = utils.HF_JtoA(self.info)
	self.Info = utils.Lz4Encode(self.info)
	//self.FightInfos = utils.HF_JtoA(self.fightInfos)
	//self.FightInfos = utils.Lz4Encode(&self.fightInfos)
}

func (self *PeakArenaDB) Decode() {
	//json.Unmarshal([]byte(self.Info), &self.info)
	utils.Lz4Decode([]byte(self.Info), &self.info)
	//json.Unmarshal([]byte(self.FightInfos), &self.fightInfos)
	return
}

// 存储数据库
func (self *PeakArenaMgr) OnSave() {
	self.Mu.Lock()
	defer self.Mu.Unlock()
	for _, serverGroup := range self.PeakArenaInfo {
		serverGroup.Locker.RLock()
		for _, user := range serverGroup.db_list {
			user.info = serverGroup.rankInfo[user.info.RankPos-1]
			user.Encode()
			user.UpdateEx("periods", user.Periods)
		}
		serverGroup.Locker.RUnlock()
	}
}

func (self *PeakArenaMgr) GetAllData() {
	core.GetMasterApp().StartWait()
	self.Mu.Lock()
	defer self.Mu.Unlock()

	periods := GetServerGroupMgr().GetPeakPeriods()
	serverGroupMap := GetServerGroupMgr().GetServerZoneMap()

	startTime := model.TimeServer().UnixMilli()

	queryStr := fmt.Sprintf("select * from `%s` where periods = %d;", TABLE_PEAK_ARENA, periods)
	var msg PeakArenaDB
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	dBEndTime := model.TimeServer().UnixMilli()

	resCorrect := make(map[int64]*PeakArenaDB)
	for i := 0; i < len(res); i++ {
		data := res[i].(*PeakArenaDB)
		//看看是否匹配分组
		groupId, groupOk := serverGroupMap[data.SvrId]
		if groupOk && groupId != data.GroupId {
			continue
		}
		data.Decode()
		if data.info == nil {
			continue
		}
		oldInfo, ok := resCorrect[data.Uid]
		if ok {
			if oldInfo.info.Score > data.info.Score {
				continue
			}
		}
		resCorrect[data.Uid] = data
	}

	for _, data := range resCorrect {
		_, ok := self.PeakArenaInfo[data.GroupId]
		if !ok {
			self.PeakArenaInfo[data.GroupId] = self.NewPeakArenaInfo(data.GroupId, periods)
		}
		if data.info == nil {
			data.info = new(PeakArenaUser)
			data.info.Uid = data.Uid
			data.info.SetScore(PEAK_ARENA_SCORE_START)
			continue
		} else {
			data.info.ScoreLv = GetScoreLv(data.info.Score)
		}
		data.Init(TABLE_PEAK_ARENA, data, false)
		self.PeakArenaInfo[data.GroupId].db_list[data.Uid] = data
	}
	dataEndTime := model.TimeServer().UnixMilli()
	//排序，生成初始信息
	for _, peakArenaInfo := range self.PeakArenaInfo {
		peakArenaInfo.InitRank()
		peakArenaInfo.CountAllScore(false)
	}
	sortEndTime := model.TimeServer().UnixMilli()
	core.GetMasterApp().StartDone()
	log.Println("顶上战争startTime:", startTime)
	log.Println("顶上战争dBEndTime:", dBEndTime)
	log.Println("顶上战争dataEndTime:", dataEndTime)
	log.Println("顶上战争sortEndTime:", sortEndTime)
	return
}
func (self *PeakArenaUser) SetScore(score int) {
	self.Score = PEAK_ARENA_SCORE_START
	self.ScoreLv = GetScoreLv(PEAK_ARENA_SCORE_START)
}
func (self *PeakArenaInfo) InitRank() {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	self.rankInfo = make([]*PeakArenaUser, 0)
	for _, v := range self.db_list {
		self.rankInfo = append(self.rankInfo, v.info)
	}
	sort.Sort(lstPeakArenaRank(self.rankInfo))
	for index, v := range self.rankInfo {
		v.RankPos = index + 1
	}
	return
}
func (self *PeakArenaMgr) NewPeakArenaInfo(groupId int, periods int) *PeakArenaInfo {
	data := new(PeakArenaInfo)
	data.groupId = groupId
	data.periods = periods
	data.rankInfo = make([]*PeakArenaUser, 0)
	data.serverInfo = make([]*PeakArenaServer, 0)
	data.db_list = make(map[int64]*PeakArenaDB)
	data.Locker = new(sync.RWMutex)
	return data
}
func (self *PeakArenaMgr) GetPeakArenaInfo(serverId int) *PeakArenaInfo {
	self.Mu.Lock()
	defer self.Mu.Unlock()
	//获得该服务器分组
	groupId := GetServerGroupMgr().GetZoneId(serverId)
	// 获得竞技场期数
	periods := GetServerGroupMgr().GetPeakPeriods()
	_, ok := self.PeakArenaInfo[groupId]
	if !ok {
		self.PeakArenaInfo[groupId] = self.NewPeakArenaInfo(groupId, periods)
	}
	return self.PeakArenaInfo[groupId]
}
func (self *PeakArenaMgr) GetPeakArenaInfoByGroupId(groupId int) *PeakArenaInfo {
	return self.PeakArenaInfo[groupId]
}
func (self *PeakArenaMgr) GetGroupConfig() string {
	//config := GetServerGroupMgr().GetArenaConfig()
	groupConfig := GetServerGroupMgr().GetZoneConfig()
	return utils.HF_JtoA(groupConfig)
}
func (self *PeakArenaMgr) GetConfig(serverId int) (string, string, string, string) {
	config := GetServerGroupMgr().GetPeakConfig()
	groupConfig := GetServerGroupMgr().GetZoneConfig()
	groups := GetServerGroupMgr().GetZones(serverId)
	cosmicArenaConfig := GetServerGroupMgr().GetCosmicArenaConfig()
	return utils.HF_JtoA(config), utils.HF_JtoA(groupConfig), utils.HF_JtoA(groups), utils.HF_JtoA(cosmicArenaConfig)
}
func (self *PeakArenaMgr) GetInfo(uid int64, serverId int, fightInfos []*model.JS_FightInfo) (int, string, string, string) {
	if len(fightInfos) < PEAK_ARENA_TEAM_MAX {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", "", ""
	}
	peakArenaInfo := self.GetPeakArenaInfo(serverId)
	if peakArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", "", ""
	}
	info, enemyInfo, enemyFightInfo := peakArenaInfo.GetInfo(uid, fightInfos)
	infoStr := ""
	enemyInfoStr := ""
	enemyFightInfoStr := ""
	if info != nil {
		infoStr = utils.HF_JtoA(info)
	}
	if enemyInfo != nil {
		enemyInfoStr = utils.HF_JtoA(enemyInfo)
	}
	if enemyFightInfo != nil {
		enemyFightInfoStr = utils.HF_JtoA(enemyFightInfo)
	}
	return RETCODE_DATA_CROSS_OK, infoStr, enemyInfoStr, enemyFightInfoStr
}

func (self *PeakArenaInfo) GetInfoDB(uid int64, fightInfos []*model.JS_FightInfo) *PeakArenaDB {
	if len(fightInfos) == 0 {
		return nil
	}
	self.Locker.Lock()
	defer self.Locker.Unlock()
	infoDB, ok := self.db_list[uid]
	if !ok {
		infoDB = new(PeakArenaDB)
		infoDB.Uid = uid
		infoDB.Periods = self.periods
		infoDB.GroupId = self.groupId
		infoDB.SvrId = fightInfos[0].Server
		//infoDB.fightInfos = fightInfos
		infoDB.info = self.NewInfo(fightInfos)
		self.db_list[uid] = infoDB

		infoDB.Encode()
		id := db.InsertTable(TABLE_PEAK_ARENA, infoDB, 0, false)
		infoDB.Id = int(id)
		infoDB.Init(TABLE_PEAK_ARENA, infoDB, false)

		infoDB.info.RankPos = len(self.rankInfo) + 1
		self.rankInfo = append(self.rankInfo, infoDB.info)
		sort.Sort(lstPeakArenaRank(self.rankInfo))
		for index, v := range self.rankInfo {
			v.RankPos = index + 1
		}

		find := false
		for _, value := range self.serverInfo {
			if value.SvrId == infoDB.SvrId {
				if value.SvrName == "" {
					value.SvrName = server.GetServerMgr().GetServerName(value.SvrId)
				}
				value.Score += infoDB.info.Score
				find = true
				break
			}
		}
		if !find {
			data := new(PeakArenaServer)
			data.SvrId = infoDB.info.SvrId
			data.SvrName = server.GetServerMgr().GetServerName(infoDB.info.SvrId)
			data.Score = infoDB.info.Score
			self.serverInfo = append(self.serverInfo, data)
		}
		sort.Sort(lstPeakArenaServerRank(self.serverInfo))
		for index, v := range self.serverInfo {
			v.RankPos = index + 1
		}
	}

	infoDB.SetFightInfo(fightInfos)
	return infoDB
}
func (self *PeakArenaInfo) GetInfo(uid int64, fightInfos []*model.JS_FightInfo) (*PeakArenaUser, []*PeakArenaUser, map[int64]string) {
	infoDB := self.GetInfoDB(uid, fightInfos)
	if infoDB == nil {
		return nil, nil, nil
	}
	if infoDB.info != nil && len(fightInfos) > 0 {
		infoDB.info.Fight = 0
		infoDB.info.UName = fightInfos[0].Uname
		infoDB.info.Icon = fightInfos[0].Iconid
		infoDB.info.Portrait = fightInfos[0].Portrait
		infoDB.info.Title = fightInfos[0].Title
		infoDB.info.Level = fightInfos[0].Level
		infoDB.info.ArenaFight = self.CheckArenaFight(infoDB.info.ArenaFight)
		for _, v := range fightInfos {
			realValue := v.Deffight / 100
			infoDB.info.Fight += realValue * 100
		}
		fight := int64(0)
		heroid := 0
		for i, _ := range fightInfos {
			if len(fightInfos[i].Heroinfo) > 0 {
				for _, value := range fightInfos[i].Heroinfo {
					if fight == 0 {
						fight = value.Fight
						heroid = value.Heroid
					} else {
						if value.Fight > fight {
							fight = value.Fight
							heroid = value.Heroid
						}
					}
				}
			}
		}
		infoDB.info.HeroId = heroid
	}
	enemyInfo, enemyFightInfo := self.GetEnemy(uid)
	return infoDB.info, enemyInfo, enemyFightInfo
}

func (self *PeakArenaInfo) NewInfo(fightInfo []*model.JS_FightInfo) *PeakArenaUser {
	data := new(PeakArenaUser)
	data.Uid = fightInfo[0].Uid
	data.SvrId = fightInfo[0].Server
	data.UName = fightInfo[0].Uname
	data.UnionName = fightInfo[0].UnionName
	data.SetScore(PEAK_ARENA_SCORE_START)
	data.Level = fightInfo[0].Level
	data.Vip = fightInfo[0].Vip
	data.Icon = fightInfo[0].Iconid
	data.Portrait = fightInfo[0].Portrait
	data.Title = fightInfo[0].Title
	for _, v := range fightInfo {
		realValue := v.Deffight / 100
		data.Fight += realValue * 100
	}
	fight := int64(0)
	heroid := 0
	for i, _ := range fightInfo {
		if len(fightInfo[i].Heroinfo) > 0 {
			for _, value := range fightInfo[0].Heroinfo {
				if fight == 0 {
					fight = value.Fight
					heroid = value.Heroid
				} else {
					if value.Fight > fight {
						fight = value.Fight
						heroid = value.Heroid
					}
				}
			}
		}
	}
	data.HeroId = heroid
	return data
}

func (self *PeakArenaMgr) GetEnemy(uid int64, serverId int) (int, string, string) {
	peakArenaInfo := self.GetPeakArenaInfo(serverId)
	if peakArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	enemyInfo, enemyFightInfo := peakArenaInfo.GetEnemy(uid)
	return RETCODE_DATA_CROSS_OK, utils.HF_JtoA(enemyInfo), utils.HF_JtoA(enemyFightInfo)
}

func (self *PeakArenaInfo) GetEnemy(uid int64) ([]*PeakArenaUser, map[int64]string) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	relUser := make([]*PeakArenaUser, 0)
	relFight := make(map[int64]string, 0)
	info, ok := self.db_list[uid]
	if !ok {
		return nil, nil
	}
	if info.info == nil {
		return nil, nil
	}
	index := info.info.RankPos - 1
	if index < 0 || index >= len(self.rankInfo) {
		return nil, nil
	}
	//先把自己2个档位差的人全部取出来，作为备选列表
	score := self.rankInfo[index].Score
	scoreLv := self.rankInfo[index].ScoreLv
	scoreLvMap := make(map[int]map[int64]int)
	for i := index - 1; i >= 0; i-- {
		if self.rankInfo[i].IsSign == model.LOGIC_FALSE {
			continue
		}
		if self.rankInfo[i].Uid == 0 {
			continue
		}
		nowScoreLv := self.rankInfo[i].ScoreLv
		if nowScoreLv-scoreLv > 2 {
			break
		}
		_, ok := scoreLvMap[nowScoreLv]
		if !ok {
			scoreLvMap[nowScoreLv] = make(map[int64]int)
		}
		if len(scoreLvMap[nowScoreLv]) >= PEAK_ARENA_ENEMY_CHOOSE_LIST {
			continue
		}
		scoreLvMap[nowScoreLv][self.rankInfo[i].Uid] = model.LOGIC_TRUE
	}
	for i := index + 1; i < len(self.rankInfo); i++ {
		if self.rankInfo[i].IsSign == model.LOGIC_FALSE {
			continue
		}
		if self.rankInfo[i].Uid == 0 {
			continue
		}
		nowScoreLv := self.rankInfo[i].ScoreLv
		if scoreLv-nowScoreLv > 2 {
			break
		}
		_, ok := scoreLvMap[nowScoreLv]
		if !ok {
			scoreLvMap[nowScoreLv] = make(map[int64]int)
		}
		if len(scoreLvMap[nowScoreLv]) >= PEAK_ARENA_ENEMY_CHOOSE_LIST {
			continue
		}
		scoreLvMap[nowScoreLv][self.rankInfo[i].Uid] = model.LOGIC_TRUE
	}
	cantList := make(map[int64]int)
	for k := range self.rankInfo[index].CantList {
		cantList[k] = model.LOGIC_TRUE
	}

	firstEnemy, cantList := self.GetFirstEnemy(scoreLvMap, cantList, scoreLv, score)
	relUser = append(relUser, firstEnemy)
	secondEnemy, cantList := self.GetSecondEnemy(scoreLvMap, cantList, scoreLv, score)
	relUser = append(relUser, secondEnemy)
	thirdEnemy, _ := self.GetThirdEnemy(scoreLvMap, cantList, scoreLv, score)
	for k := range self.rankInfo[index].CantList {
		delete(cantList, k)
	}
	//暂时关闭过滤保证
	//self.rankInfo[index].CantList = cantList
	relUser = append(relUser, thirdEnemy)
	sort.Slice(relUser, func(i, j int) bool {
		return relUser[i].Score >= relUser[j].Score
	})
	for _, v := range relUser {
		if v.Uid == 0 {
			continue
		}
		enemyInfo, ok := self.db_list[v.Uid]
		if !ok {
			continue
		}
		relFight[v.Uid] = enemyInfo.FightInfos
	}
	//深拷返回
	deepRelUser := make([]*PeakArenaUser, 0)
	deepRelFight := make(map[int64]string, 0)
	utils.HF_DeepCopy(&deepRelUser, &relUser)
	utils.HF_DeepCopy(&deepRelFight, &relFight)
	return deepRelUser, deepRelFight
}

// 1. 高1档位
// 2. 高2个档位
// 3. 当前档位
// 4. 低1个档位
// 5. 低2个档位
// 6. 补机器人
func (self *PeakArenaInfo) GetFirstEnemy(scoreLvMap map[int]map[int64]int, cantList map[int64]int, scoreLv int, score int) (*PeakArenaUser, map[int64]int) {

	for k := range scoreLvMap[scoreLv+1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv+2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	//前面全部没有选中，开始补机器人
	robotInfo := new(PeakArenaUser)
	robotInfo.Score = score
	return robotInfo, cantList
}

// 1. 当前档位
// 2. 高一个档位
// 3. 低一个档位
// 4. 高两个档位
// 5. 低两个档位
// 6. 补机器人
func (self *PeakArenaInfo) GetSecondEnemy(scoreLvMap map[int]map[int64]int, cantList map[int64]int, scoreLv int, score int) (*PeakArenaUser, map[int64]int) {
	for k := range scoreLvMap[scoreLv] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv+1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv+2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	//前面全部没有选中，开始补机器人
	robotInfo := new(PeakArenaUser)
	robotInfo.Score = score
	return robotInfo, cantList
}

// 1. 低一个档位
// 2. 低两个档位
// 3. 当前档位
// 4. 补机器人
func (self *PeakArenaInfo) GetThirdEnemy(scoreLvMap map[int]map[int64]int, cantList map[int64]int, scoreLv int, score int) (*PeakArenaUser, map[int64]int) {
	for k := range scoreLvMap[scoreLv-1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	//前面全部没有选中，开始补机器人
	robotInfo := new(PeakArenaUser)
	robotInfo.Score = score
	return robotInfo, cantList
}

func (self *PeakArenaMgr) AttackEnd(uid int64, serverId int, attack []*model.JS_FightInfo,
	defend []*model.JS_FightInfo, battleInfo []*model.BattleInfo, result int, fightID [PEAK_ARENA_TEAM_MAX]int64) (int, string, string, string) {
	peakArenaInfo := self.GetPeakArenaInfo(serverId)
	if peakArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", "", ""
	}
	info, enemyInfo, enemyFightInfo := peakArenaInfo.AttackEnd(uid, attack, defend, battleInfo, result, fightID)
	return RETCODE_DATA_CROSS_OK, utils.HF_JtoA(info), utils.HF_JtoA(enemyInfo), utils.HF_JtoA(enemyFightInfo)
}

func (self *PeakArenaInfo) AttackEnd(uid int64, attack []*model.JS_FightInfo, defend []*model.JS_FightInfo,
	battleInfo []*model.BattleInfo, result int, fightID [PEAK_ARENA_TEAM_MAX]int64) (*PeakArenaUser, []*PeakArenaUser, map[int64]string) {

	self.CalAttackEnd(attack, defend, battleInfo, result, fightID)
	enemyInfo, enemyFightInfo := self.GetEnemy(uid)
	infoDB := self.GetInfoDB(uid, attack)
	if infoDB == nil {
		return nil, enemyInfo, enemyFightInfo
	}
	infoDB.SetFightInfo(attack)
	return infoDB.info, enemyInfo, enemyFightInfo
}

func (self *PeakArenaInfo) CalAttackEnd(attack []*model.JS_FightInfo, defend []*model.JS_FightInfo,
	battleInfo []*model.BattleInfo, result int, fightID [PEAK_ARENA_TEAM_MAX]int64) {

	for _, v := range battleInfo {
		utils.LogDebug(fmt.Sprintf("CalAttackEnd,pre id:%d", v.Id))
	}

	self.Locker.Lock()
	defer self.Locker.Unlock()
	attackStartScore := 1000 //初始积分
	attackScore := attackStartScore
	defenceStartScore := 1000 //初始积分
	defenceScore := defenceStartScore
	attackPoint := 0  //变化的积分
	defencePoint := 0 //变化的积分
	attackUid := int64(0)
	if attack[0].Uid > 0 {
		attackUid = attack[0].Uid
		attackInfo, ok := self.db_list[attackUid]
		if ok && attackInfo.info != nil {
			attackStartScore = attackInfo.info.Score
			attackInfo.info.IsSign = model.LOGIC_TRUE
		}
	}
	//防守方需要排除机器人
	defendUid := int64(0)
	if defend[0].Uid > 0 {
		defendUid = defend[0].Uid
		defendInfo, ok := self.db_list[defendUid]
		if ok && defendInfo.info != nil {
			defenceStartScore = defendInfo.info.Score
		}
	}
	if result == model.ATTACK_WIN {
		attackPoint = CalWinScore(attackStartScore, defenceStartScore)
		defencePoint = CalLoseScore(attackStartScore, defenceStartScore)
		if attackUid > 0 {
			attackInfo, ok := self.db_list[attackUid]
			if !ok || attackInfo.info == nil {
				return
			}
			attackInfo.info.Score += attackPoint
			if attackInfo.info.Score < 0 {
				attackInfo.info.Score = 0
			}
			attackInfo.info.ScoreLv = GetScoreLv(attackInfo.info.Score)
			attackScore = attackInfo.info.Score
			self.AddScore(attackInfo.info, attackPoint)
		}
		if defendUid > 0 {
			defendInfo, ok := self.db_list[defendUid]
			if ok && defendInfo.info != nil {
				defendInfo.info.Score += defencePoint
				if defendInfo.info.Score < 0 {
					defendInfo.info.Score = 0
				}
				defendInfo.info.ScoreLv = GetScoreLv(defendInfo.info.Score)
				defenceScore = defendInfo.info.Score
				self.AddScore(defendInfo.info, defencePoint)
			}
		} else {
			defenceScore += defencePoint
		}
	} else {
		defencePoint = CalWinScore(attackStartScore, defenceStartScore)
		attackPoint = CalLoseScore(attackStartScore, defenceStartScore)
		if attackUid > 0 {
			attackInfo, ok := self.db_list[attackUid]
			if !ok || attackInfo.info == nil {
				return
			}
			attackInfo.info.Score += attackPoint
			if attackInfo.info.Score < 0 {
				attackInfo.info.Score = 0
			}
			attackInfo.info.ScoreLv = GetScoreLv(attackInfo.info.Score)
			attackScore = attackInfo.info.Score
			self.AddScore(attackInfo.info, attackPoint)
		}
		if defendUid > 0 {
			defendInfo, ok := self.db_list[defendUid]
			if ok && defendInfo.info != nil {
				defendInfo.info.Score += defencePoint
				if defendInfo.info.Score < 0 {
					defendInfo.info.Score = 0
				}
				defendInfo.info.ScoreLv = GetScoreLv(defendInfo.info.Score)
				defenceScore = defendInfo.info.Score
				self.AddScore(defendInfo.info, defencePoint)
			}
		} else {
			defenceScore += defencePoint
		}
	}

	sort.Sort(lstPeakArenaRank(self.rankInfo))
	for index, v := range self.rankInfo {
		v.RankPos = index + 1
	}
	//fightId := GetServerGroupMgr().GetRecordId()
	for index, v := range battleInfo {
		v.LevelID = 600003
		v.Id = fightID[index]
		if v.UserInfo[0] != nil {
			v.UserInfo[0].Score = attackScore
			v.UserInfo[0].Param1 = attackPoint
		}
		if v.UserInfo[1] != nil {
			v.UserInfo[1].Score = defenceScore
			v.UserInfo[1].Param1 = defencePoint
		}
		battleRecord := model.BattleRecord{}
		battleRecord.Id = v.Id
		battleRecord.LevelID = v.LevelID
		battleRecord.Result = result
		battleRecord.RandNum = v.Random
		battleRecord.FightInfo[0] = attack[index]
		battleRecord.FightInfo[1] = defend[index]
		lz4Str := utils.Lz4Encode(&battleRecord)
		db.HMSetRedisEx(REDIS_TABLE_NAME_BATTLERECORD, battleRecord.Id, lz4Str, utils.DAY_SECS*3)

		utils.LogDebug(fmt.Sprintf("CalAttackEnd,battleRecord id:%d", battleRecord.Id))
	}
	for _, v := range battleInfo {
		utils.LogDebug(fmt.Sprintf("CalAttackEnd,next  id:%d", v.Id))
	}
	if attackUid > 0 {
		attackInfo, ok := self.db_list[attackUid]
		if ok {
			//生成战报
			attackPeakArenaFight := self.NewPeakArenaFight(fightID[0], attack, defend, battleInfo, result, 0, attackPoint, attack[0])
			attackInfo.info.ArenaFight = append(attackInfo.info.ArenaFight, attackPeakArenaFight)
			attackInfo.info.ArenaFight = self.CheckArenaFight(attackInfo.info.ArenaFight)
		}
	}
	if defendUid > 0 {
		defendInfo, ok := self.db_list[defendUid]
		if ok {
			//生成战报
			defendPeakArenaFight := self.NewPeakArenaFight(fightID[0], attack, defend, battleInfo, result, 1, defencePoint, defend[0])
			defendInfo.info.ArenaFight = append(defendInfo.info.ArenaFight, defendPeakArenaFight)
			defendInfo.info.ArenaFight = self.CheckArenaFight(defendInfo.info.ArenaFight)
		}
	}

	return
}

func (self *PeakArenaInfo) NewPeakArenaFight(fightId int64, attack []*model.JS_FightInfo, defend []*model.JS_FightInfo,
	battleInfo []*model.BattleInfo, result int, side int, point int, fightInfo *model.JS_FightInfo) *model.PeakArenaFight {

	data := new(model.PeakArenaFight)
	data.FightId = fightId
	data.Side = side
	data.Result = result
	data.Point = point
	data.Uid = fightInfo.Uid
	data.IconId = fightInfo.Iconid
	data.Portrait = fightInfo.Portrait
	data.Title = fightInfo.Title
	data.Name = fightInfo.Uname
	data.Time = model.TimeServer().Unix()
	data.Level = fightInfo.Level
	if side == 0 {
		for _, v := range attack {
			data.Fight += v.Deffight
		}
	} else {
		for _, v := range defend {
			data.Fight += v.Deffight
		}
	}
	data.BattleInfo = battleInfo
	return data
}

func (self *PeakArenaMgr) GetRank(uid int64, serverId int, fightInfos []*model.JS_FightInfo) (int, string, string, string) {
	peakArenaInfo := self.GetPeakArenaInfo(serverId)
	if peakArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", "", ""
	}
	selfInfo, rankInfo, serverInfo := peakArenaInfo.GetRank(uid, fightInfos, serverId)
	selfInfoStr := ""
	rankInfoStr := ""
	serverInfoStr := ""
	if selfInfo != nil {
		selfInfoStr = utils.HF_JtoA(selfInfo)
	}
	if rankInfo != nil {
		rankInfoStr = utils.HF_JtoA(rankInfo)
	}
	if serverInfo != nil {
		serverInfoStr = utils.HF_JtoA(serverInfo)
	}
	return RETCODE_DATA_CROSS_OK, selfInfoStr, rankInfoStr, serverInfoStr
}

func (self *PeakArenaInfo) GetRank(uid int64, fightInfos []*model.JS_FightInfo, serverId int) (*model.RankInfo, []*model.RankInfo, []*PeakArenaServer) {
	selfInfo := self.GetInfoDB(uid, fightInfos)
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rankInfo := make([]*model.RankInfo, 0)
	for _, v := range self.rankInfo {
		if v.IsSign == model.LOGIC_FALSE {
			break
		}
		if len(rankInfo) >= PEAK_ARENA_RANK_MAX {
			break
		}
		rank := GetRankInfoPeakArena(v)
		rankInfo = append(rankInfo, rank)
	}
	if selfInfo == nil {
		return nil, rankInfo, self.serverInfo
	}
	return GetRankInfoPeakArena(selfInfo.info), rankInfo, self.serverInfo
}

func (self *PeakArenaMgr) GetRecord(uid int64, serverId int, id int64) (int, string) {
	utils.LogDebug("PeakArenaMgr,GetRecord:", id)
	var battleRecord model.BattleRecord
	value, flag, err := db.HGetRedisEx(REDIS_TABLE_NAME_BATTLERECORD, id, fmt.Sprintf("%d", id))
	if err != nil || !flag {
		return 0, ""
	}
	utils.Lz4Decode([]byte(value), &battleRecord)
	//err1 := json.Unmarshal([]byte(value), &battleRecord)
	//if err1 != nil {
	//	return 0, ""
	//}
	if battleRecord.Id == 0 {
		return 0, ""
	}
	return 0, utils.HF_JtoA(battleRecord)
}

func (self *PeakArenaMgr) Look(uid int64, serverId int, targetUid int64) (int, string, string) {
	peakArenaInfo := self.GetPeakArenaInfo(serverId)
	if peakArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	userInfo, userFightInfo := peakArenaInfo.Look(targetUid)
	return RETCODE_DATA_CROSS_OK, utils.HF_JtoA(userInfo), utils.HF_JtoA(userFightInfo)
}

func (self *PeakArenaInfo) Look(uid int64) (*PeakArenaUser, string) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	info, ok := self.db_list[uid]
	if !ok {
		return nil, ""
	}
	return info.info, info.FightInfos
}

func (self *PeakArenaMgr) GetHallOfGlory(groupId int) *HallOfGlorys {
	info := self.GetPeakArenaInfoByGroupId(groupId)
	if info == nil {
		return nil
	}
	return info.GetHallOfGlorys()
}

func (self *PeakArenaInfo) GetHallOfGlorys() *HallOfGlorys {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rel := new(HallOfGlorys)
	for i := 0; i <= len(self.rankInfo); i++ {
		if i >= len(self.rankInfo) {
			break
		}
		data := self.MakeHallOfGloryInfo(self.rankInfo[i])
		rel.HallOfGlory = append(rel.HallOfGlory, data)
		if len(rel.HallOfGlory) >= 3 {
			break
		}
	}
	return rel
}
func (self *PeakArenaInfo) MakeHallOfGloryInfo(data *PeakArenaUser) *HallOfGloryInfo {
	hallOfGloryInfo := new(HallOfGloryInfo)
	if data == nil {
		return hallOfGloryInfo
	}
	hallOfGloryInfo.Uid = data.Uid
	hallOfGloryInfo.SvrId = data.SvrId
	hallOfGloryInfo.SvrName = data.SvrName
	hallOfGloryInfo.UName = data.UName
	hallOfGloryInfo.UnionName = data.UnionName
	hallOfGloryInfo.Score = data.Score
	hallOfGloryInfo.ScoreLv = data.ScoreLv
	hallOfGloryInfo.RankPos = data.RankPos
	hallOfGloryInfo.Level = data.Level
	hallOfGloryInfo.Vip = data.Vip
	hallOfGloryInfo.Icon = data.Icon
	hallOfGloryInfo.Portrait = data.Portrait
	hallOfGloryInfo.Title = data.Title
	hallOfGloryInfo.Fight = data.Fight
	return hallOfGloryInfo
}

func (self *PeakArenaMgr) ClearData() {
	self.PeakArenaInfo = make(map[int]*PeakArenaInfo, 0)
}

func (self *PeakArenaMgr) UpdateInfo(uid int64, serverId int, fightInfos []*model.JS_FightInfo) (int, string) {
	if len(fightInfos) < PEAK_ARENA_TEAM_MAX {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	peakArenaInfo := self.GetPeakArenaInfo(serverId)
	if peakArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	info := peakArenaInfo.UpdateInfo(uid, fightInfos)
	infoStr := ""
	if info != nil {
		infoStr = utils.HF_JtoA(info)
	}
	return RETCODE_DATA_CROSS_OK, infoStr
}
func (self *PeakArenaInfo) UpdateInfo(uid int64, fightInfos []*model.JS_FightInfo) *PeakArenaUser {
	infoDB := self.GetInfoDB(uid, fightInfos)
	if infoDB == nil {
		return nil
	}
	infoDB.SetFightInfo(fightInfos)
	if infoDB.info != nil && len(fightInfos) > 0 {
		infoDB.info.Fight = 0
		infoDB.info.UName = fightInfos[0].Uname
		infoDB.info.Icon = fightInfos[0].Iconid
		infoDB.info.Portrait = fightInfos[0].Portrait
		infoDB.info.Title = fightInfos[0].Title
		infoDB.info.Level = fightInfos[0].Level
		infoDB.info.UnionName = fightInfos[0].UnionName
		infoDB.info.SvrId = fightInfos[0].Server

		for _, v := range fightInfos {
			//3队加起来因为看不见后两位的原因，导致数值偏大
			realValue := v.Deffight / 100
			infoDB.info.Fight += realValue * 100
		}
	}
	return infoDB.info
}

func (self *PeakArenaDB) SetFightInfo(fightInfos []*model.JS_FightInfo) {
	if len(fightInfos) != PEAK_ARENA_TEAM_MAX {
		return
	}
	for _, v := range fightInfos {
		if v == nil {
			return
		}
		if len(v.Heroinfo) == 0 {
			return
		}
	}
	self.FightInfos = utils.Lz4Encode(fightInfos)
}
func (self *PeakArenaMgr) SendRankReward() {
	period := GetServerGroupMgr().GetPeakPeriods()
	for _, v := range self.PeakArenaInfo {
		rewardList, serverInfos, HallOfGlorysInfo := v.GetRewardList(period)
		if HallOfGlorysInfo != nil {
			HallOfGlorysInfo.StartTime = GetServerGroupMgr().ServerZoneConfig.crossServerPlayConfig.ArenaConfig.StartTime
			HallOfGlorysInfo.EndTime = GetServerGroupMgr().ServerZoneConfig.crossServerPlayConfig.ArenaConfig.EndTime
		}
		for _, rewardInfo := range rewardList {
			//给荣耀殿堂赋值
			data, ok := GetServerGroupMgr().ServerZone.Load(rewardInfo.ServerId)
			if ok {
				serverInfo := data.(*ServerZoneInfo)
				if serverInfo.activityInfo == nil {
					serverInfo.activityInfo = new(ActivityInfo)
				}
				serverInfo.activityInfo.HallOfGlorys = append(serverInfo.activityInfo.HallOfGlorys, HallOfGlorysInfo)
			}
			allRewardInfo := new(PeakRewardInfo)
			allRewardInfo.PersonReward = rewardInfo
			_, ok2 := serverInfos[rewardInfo.ServerId]
			if ok2 {
				allRewardInfo.ServerReward = serverInfos[rewardInfo.ServerId]
			}
			info := utils.HF_JtoA(allRewardInfo)
			WriterLog(4, info)
			core.GetCenterApp().AddEvent(rewardInfo.ServerId, core.CROSS_SERVER_PEAK_ARENA_RANK_REWARD, 0,
				0, 0, info)
			//给玩家打上排行标记
			for _, user := range rewardInfo.RewardInfo {
				GetOfflineInfoMgr().SetPeakRank(user.Uid, period, user.RankPos)
			}
		}
	}
}

func (self *PeakArenaInfo) GetRewardList(period int) (map[int]*RewardList, map[int]*RewardServerInfo, *HallOfGlorys) {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rewardMap := make(map[int]*RewardList)
	rel := new(HallOfGlorys)
	for index, v := range self.rankInfo {
		if v.IsSign == model.LOGIC_FALSE {
			break
		}
		//抓取荣耀殿堂数据
		if index < 3 {
			data := self.MakeHallOfGloryInfo(v)
			rel.HallOfGlory = append(rel.HallOfGlory, data)
		}
		info, ok := rewardMap[v.SvrId]
		if !ok {
			info = new(RewardList)
			info.ServerId = v.SvrId
			info.Period = period
			rewardMap[info.ServerId] = info
		}
		info.RewardInfo = append(info.RewardInfo, &RewardInfo{Uid: v.Uid, RankPos: v.RankPos})
	}
	self.CountAllScore(true)
	serverInfos := make(map[int]*RewardServerInfo, 0)
	for _, value := range self.serverInfo {
		serverInfos[value.SvrId] = &RewardServerInfo{value.SvrId, value.RankPos}
	}
	return rewardMap, serverInfos, rel
}
func (self *PeakArenaMgr) DeletePlayerRecord(req *RPC_RankDoLikeReq) {
	peakArenaInfo := self.GetPeakArenaInfo(req.ServerId)
	if peakArenaInfo == nil {
		return
	}
	peakArenaInfo.DeletePlayerRecord(req.Uid)
}
func (self *PeakArenaInfo) DeletePlayerRecord(uid int64) {
	self.Locker.Lock()
	defer self.Locker.Unlock()

	user, ok := self.db_list[uid]
	if !ok {
		return
	}
	if user.info == nil {
		return
	}
	self.AddScore(user.info, -user.info.Score)
	user.info.Score = 0
	sort.Sort(lstPeakArenaRank(self.rankInfo))
	for index, v := range self.rankInfo {
		v.RankPos = index + 1
	}
}

//
//func (self *PeakArenaMgr) GameServerGetRankReward(serverId int, userList string) {
//
//	targetList := make(map[int64]int)
//	json.Unmarshal([]byte(userList), &targetList)
//	if len(targetList) == 0 {
//		return
//	}
//	targetPeriod := GetServerGroupMgr().GetPeakPeriods() - 1
//
//	rewardInfo := new(RewardList)
//	rewardInfo.ServerId = serverId
//	rewardInfo.Period = targetPeriod
//	rewardInfo.RewardInfo = make([]*RewardInfo, 0)
//	for uid, period := range targetList {
//		if period >= targetPeriod {
//			continue
//		}
//		rankPos := GetOfflineInfoMgr().GetArenaRank(targetPeriod, uid)
//		if rankPos == 0 {
//			continue
//		}
//		rewardInfo.RewardInfo = append(rewardInfo.RewardInfo, &RewardInfo{Uid: uid, RankPos: rankPos})
//	}
//	core.GetCenterApp().AddEvent(rewardInfo.ServerId, core.CROSS_SERVER_PEAK_ARENA_RANK_REWARD, 0,
//		0, 0, utils.HF_JtoA(rewardInfo))
//}

func (self *PeakArenaInfo) CheckArenaFight(arenaFight []*model.PeakArenaFight) []*model.PeakArenaFight {
	nowTime := model.TimeServer().Unix()
	rel := make([]*model.PeakArenaFight, 0)
	for _, v := range arenaFight {
		if v.Time+3*utils.DAY_SECS < nowTime {
			continue
		}
		rel = append(rel, v)
	}
	if len(rel) > 10 {
		rel = rel[1:]
	}
	return rel
}

func (self *PeakArenaInfo) CountAllScore(safe bool) {
	if !safe {
		self.Locker.Lock()
		defer self.Locker.Unlock()
	}
	serverInfoMap := make(map[int]*PeakArenaServer)
	for _, value := range self.rankInfo {
		_, ok := serverInfoMap[value.SvrId]
		if !ok {
			serverInfoMap[value.SvrId] = new(PeakArenaServer)
			serverInfoMap[value.SvrId].SvrId = value.SvrId
			serverInfoMap[value.SvrId].SvrName = server.GetServerMgr().GetServerName(value.SvrId)
			serverInfoMap[value.SvrId].Score = value.Score
			serverInfoMap[value.SvrId].RankPos = 0
		} else {
			if serverInfoMap[value.SvrId].SvrName == "" {
				serverInfoMap[value.SvrId].SvrName = server.GetServerMgr().GetServerName(value.SvrId)
			}
			serverInfoMap[value.SvrId].Score += value.Score
		}
	}

	serverData := []*PeakArenaServer{}
	for _, value := range serverInfoMap {
		serverData = append(serverData, value)
	}
	self.serverInfo = serverData
	sort.Sort(lstPeakArenaServerRank(self.serverInfo))
	for index, v := range self.serverInfo {
		v.RankPos = index + 1
	}
}

func (self *PeakArenaInfo) AddScore(infoDB *PeakArenaUser, score int) {
	find := false
	for _, value := range self.serverInfo {
		if value.SvrId == infoDB.SvrId {
			value.Score += score
			if value.Score < 0 {
				value.Score = 0
			}
			if value.SvrName == "" {
				value.SvrName = server.GetServerMgr().GetServerName(value.SvrId)
			}
			find = true
			break
		}
	}
	if !find {
		data := new(PeakArenaServer)
		data.SvrId = infoDB.SvrId
		data.SvrName = server.GetServerMgr().GetServerName(infoDB.SvrId)
		if score < 0 {
			data.Score = 0
		} else {
			data.Score = score
		}
		self.serverInfo = append(self.serverInfo, data)
	}
	sort.Sort(lstPeakArenaServerRank(self.serverInfo))
	for index, v := range self.serverInfo {
		v.RankPos = index + 1
	}
}
