package crossserver

import (
	"context"
	"sync"
	"time"

	"errors"
	"master/db"
	"master/utils"
)

const (
	RET_CODE_OK = 0
	RET_CODE_ERR = 1

	ACTION_ENTER  = 1
	ACTION_LEAVE  = 2
	ACTION_ATTACK = 3

	MINE_LEVEL_MAX = 6 // 矿点等级上限
	POSITION_MAX   = 16 // 坑位上限
	TEAM_MAX       = 3  // 队伍上限

	// 时间常量
	WEEKLY_START_HOUR = 22 // 每周开始时间(22:00)
	WEEKLY_END_HOUR   = 10 // 每周结束时间(10:00)
	WEEKLY_START_DAY  = 4  // 每周开始日(周四)
)

type RPC_MinePointReq struct {
	MineId      int64 `json:"mine_id"`
	Uid         int64 `json:"uid"`
	ServerId    int64 `json:"server_id"`
	TeamId      int64 `json:"team_id"`
	Position    int   `json:"position"`
	AttackPower int64 `json:"attack_power"`
	GroupId     int   `json:"group_id"` // 支线ID
}

type RPC_MinePointRes struct {
	Code       int                `json:"code"`
	Points     []*MinePoint       `json:"points"`
	Positions  []*MinePosition    `json:"positions"`
	Teams      []*MineTeam        `json:"teams"`
	OutputNum  int                `json:"output_num"`
	OutputItem int                `json:"output_item"`
}

type MinePoint struct {
	MineId       int64 `json:"mine_id"`
	OwnerId      int64 `json:"owner_id"`
	Shield       int64 `json:"shield"`
	AttackNum    int   `json:"attack_num"`
	OutputItem   int   `json:"output_item"`
	OutputRate   int64 `json:"output_rate"`
	Level        int   `json:"level"`
	LineIndex    int   `json:"line_index"`
	GroupId      int   `json:"group_id"` // 支线ID，0表示公共矿点
	LastSaveTime int64 `json:"last_save_time"`
	OutputNum    int64 `json:"output_num"`
}

type MinePosition struct {
	PositionId int64 `json:"position_id"`
	MineId     int64 `json:"mine_id"`
	PlayerId   int64 `json:"player_id"`
	EnterTime  int64 `json:"enter_time"`
	LeaveTime  int64 `json:"leave_time"`
	Status     int   `json:"status"`
	GroupId    int   `json:"group_id"`
}

type MineTeam struct {
	TeamId         int64 `json:"team_id"`
	PlayerId       int64 `json:"player_id"`
	MineId         int64 `json:"mine_id"`
	PositionId     int64 `json:"position_id"`
	GroupId        int   `json:"group_id"`
	Status         int   `json:"status"`
	LastAttackTime int64 `json:"last_attack_time"`
}

type MineWarInfo struct {
	groupId       int
	MinePoints    map[int64]*MinePoint
	MinePositions map[int64]*MinePosition
	MineTeams     map[int64]*MineTeam
	Players       map[int64][]int64
	Groups        map[int][]int64
	Config        *MineWarConfig
	lock          *sync.RWMutex
}

type MineWarConfig struct {
	KeepTime         int64 `json:"keep_time"`         // 持续时间
	BoothProtection  int64 `json:"booth_protection"`  // 占位保护时长秒
	AttackNum        int   `json:"attack_num"`        // 每日攻击次数
	ShieldProtection int64 `json:"shield_protection"` // 护盾保护时长秒
	BoothMaxtime     int64 `json:"booth_maxtime"`     // 坑位最长占领时间
	AttackCd         int64 `json:"attack_cd"`         // 队伍攻击CD时间
}

type MineWarMgr struct {
	MineWarInfo sync.Map
	lock        *sync.RWMutex
}

func (self *MineWarMgr) Save() {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 遍历所有MineWarInfo并保存
	self.MineWarInfo.Range(func(key, value interface{}) bool {
		info := value.(*MineWarInfo)
		info.lock.Lock()
		defer info.lock.Unlock()

			// 保存MinePoints
			for _, point := range info.MinePoints {
				dbPoint := &MinePointDB{
					MineId:       point.MineId,
					OwnerId:      point.OwnerId,
					Shield:       point.Shield,
					OutputItem:   point.OutputItem,
					OutputRate:   point.OutputRate,
					Level:        point.Level,
					LineIndex:    point.LineIndex,
					GroupId:      info.groupId,
					LastSaveTime: point.LastSaveTime,
					OutputNum:    point.OutputNum,
				}
				dbPoint.UpdateEx("groupid", info.groupId)
			}

			// 保存MinePositions
			for _, pos := range info.MinePositions {
				dbPos := &MinePositionDB{
					PositionId: pos.PositionId,
					MineId:     pos.MineId,
					PlayerId:   pos.PlayerId,
					EnterTime:  pos.EnterTime,
					LeaveTime:  pos.LeaveTime,
					Status:     utils.Lz4Encode(pos),
					GroupId:    info.groupId,
				}
				dbPos.UpdateEx("groupid", info.groupId)
			}

			// 保存MineTeams
			for _, team := range info.MineTeams {
				dbTeam := &MineTeamDB{
					TeamId:    team.TeamId,
					PlayerId:  team.PlayerId,
					MineId:    team.MineId,
					PositionId: team.PositionId,
					GroupId:   info.groupId,
					Status:    utils.Lz4Encode(team),
				}
				dbTeam.UpdateEx("groupid", info.groupId)
			}

		return true
	})
}

var s_mineWarMgr *MineWarMgr = nil

func GetMineWarMgr() *MineWarMgr {
	if s_mineWarMgr == nil {
		s_mineWarMgr = new(MineWarMgr)
	}
	return s_mineWarMgr
}

func (self *MineWarMgr) LoadAll() error {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 加载矿点数据
	var minePoints []MinePointDB
	if data := db.GetDBMgr().DBUser.GetAllData("select * from tbl_crossserverminewar_points", &minePoints); data == nil {
		utils.LogError("Load mine points failed")
		return errors.New("Load mine points failed")
	}

	// 加载坑位数据
	var positions []MinePositionDB
	if data := db.GetDBMgr().DBUser.GetAllData("select * from tbl_crossserverminewar_positions", &positions); data == nil {
		utils.LogError("Load mine positions failed")
		return errors.New("Load mine positions failed")
	}

	// 加载队伍数据
	var teams []MineTeamDB
	if data := db.GetDBMgr().DBUser.GetAllData("select * from tbl_crossserverminewar_teams", &teams); data == nil {
		utils.LogError("Load mine teams failed")
		return errors.New("Load mine teams failed")
	}

	// 按groupId分组加载数据
	for _, dbPoint := range minePoints {
		info, ok := self.MineWarInfo.Load(dbPoint.GroupId)
		if !ok {
			info = &MineWarInfo{
				groupId:     dbPoint.GroupId,
				MinePoints:  make(map[int64]*MinePoint),
				MinePositions: make(map[int64]*MinePosition),
				MineTeams:     make(map[int64]*MineTeam),
				Players:      make(map[int64][]int64),
				Groups:       make(map[int][]int64),
				Config:       &MineWarConfig{},
				lock:         &sync.RWMutex{},
			}
			self.MineWarInfo.Store(dbPoint.GroupId, info)
		}

		// 解码矿点数据
		info.(*MineWarInfo).MinePoints[dbPoint.MineId] = &MinePoint{
			MineId:       dbPoint.MineId,
			OwnerId:      dbPoint.OwnerId,
			Shield:       dbPoint.Shield,
			OutputItem:   dbPoint.OutputItem,
			OutputRate:   dbPoint.OutputRate,
			Level:        dbPoint.Level,
			LineIndex:    dbPoint.LineIndex,
			GroupId:      dbPoint.GroupId,
			LastSaveTime: dbPoint.LastSaveTime,
			OutputNum:    dbPoint.OutputNum,
		}

		// 添加到支线矿点列表
		if _, ok := info.(*MineWarInfo).Groups[dbPoint.GroupId]; !ok {
			info.(*MineWarInfo).Groups[dbPoint.GroupId] = make([]int64, 0)
		}
		info.(*MineWarInfo).Groups[dbPoint.GroupId] = append(info.(*MineWarInfo).Groups[dbPoint.GroupId], dbPoint.MineId)
	}

	// 加载坑位数据
	for _, dbPos := range positions {
		info, ok := self.MineWarInfo.Load(dbPos.GroupId)
		if !ok {
			continue
		}

		// 解码坑位数据
		var position MinePosition
		utils.Lz4Decode([]byte(dbPos.Status), &position)
		info.(*MineWarInfo).MinePositions[dbPos.PositionId] = &position
	}

	// 加载队伍数据
	for _, dbTeam := range teams {
		info, ok := self.MineWarInfo.Load(dbTeam.GroupId)
		if !ok {
			continue
		}

		// 解码队伍数据
		var team MineTeam
		utils.Lz4Decode([]byte(dbTeam.Status), &team)
		info.(*MineWarInfo).MineTeams[dbTeam.TeamId] = &team

		// 添加到玩家队伍列表
		if _, ok := info.(*MineWarInfo).Players[dbTeam.PlayerId]; !ok {
			info.(*MineWarInfo).Players[dbTeam.PlayerId] = make([]int64, 0)
		}
		info.(*MineWarInfo).Players[dbTeam.PlayerId] = append(info.(*MineWarInfo).Players[dbTeam.PlayerId], dbTeam.TeamId)
	}

	return nil
}

func (self *MineWarMgr) EnterMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	// 加载MineWarInfo
	info, ok := self.MineWarInfo.Load(req.GroupId)
	if !ok {
		res.Code = RET_CODE_ERR
		return
	}

	// 获取MineWarInfo实例
	mineWarInfo := info.(*MineWarInfo)
	mineWarInfo.lock.Lock()
	defer mineWarInfo.lock.Unlock()

	// 获取矿点信息
	mine, ok := mineWarInfo.MinePoints[req.MineId]
	if !ok {
		res.Code = RET_CODE_ERR
		return
	}

	// 检查是否在休战期
	if self.IsWarTime() {
		res.Code = RET_CODE_ERR
		return
	}

	// 检查玩家队伍数量是否已满
	teams, ok := mineWarInfo.Players[req.Uid]
	if ok && len(teams) >= TEAM_MAX {
		res.Code = RET_CODE_ERR
		return
	}

	// 检查指定坑位是否可用
	var position *MinePosition
	if req.Position > 0 && req.Position <= POSITION_MAX {
		pos, ok := mineWarInfo.MinePositions[int64(req.Position)]
		if ok && pos.Status == 0 {
			position = pos
		} else {
			res.Code = RET_CODE_ERR
			return
		}
	} else {
		// 随机找一个空闲坑位
		for i := 1; i <= POSITION_MAX; i++ {
			pos, ok := mineWarInfo.MinePositions[int64(i)]
			if ok && pos.Status == 0 {
				position = pos
				break
			}
		}
		if position == nil {
			res.Code = RET_CODE_ERR
			return
		}
	}

	// 创建新的队伍
	team := &MineTeam{
		TeamId:     utils.HF_Itoi64(utils.HF_RandInt(100000000, 999999999)),
		PlayerId:   req.Uid,
		MineId:     req.MineId,
		PositionId: position.PositionId,
		GroupId:    req.GroupId,
		Status:     1,
	}

	// 更新坑位状态
	position.PlayerId = req.Uid
	position.Status = 1

	// 更新玩家队伍列表
	if teams == nil {
		teams = make([]int64, 0)
		mineWarInfo.Players[req.Uid] = teams
	}
	teams = append(teams, team.TeamId)

	// 更新矿点数据
	mine.OwnerId = req.Uid
	mine.Shield = mineWarInfo.Config.ShieldProtection
	mine.LastSaveTime = time.Now().Unix()

	// 保存数据
	self.Save()

	// 返回结果
	res.Code = RET_CODE_OK
	res.Points = []*MinePoint{mine}
	res.Positions = []*MinePosition{position}
	res.Teams = []*MineTeam{team}
}

func (self *MineWarMgr) AttackMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	// 加载MineWarInfo
	info, ok := self.MineWarInfo.Load(req.GroupId)
	if !ok {
		res.Code = RET_CODE_ERR
		return
	}

	// 获取MineWarInfo实例
	mineWarInfo := info.(*MineWarInfo)
	mineWarInfo.lock.Lock()
	defer mineWarInfo.lock.Unlock()

	// 获取矿点信息
	mine, ok := mineWarInfo.MinePoints[req.MineId]
	if !ok {
		res.Code = RET_CODE_ERR
		return
	}

	// 检查矿点是否被占领
	if mine.OwnerId <= 0 {
		res.Code = RET_CODE_ERR
		return
	}

	// 检查是否在休战期
	if self.IsWarTime() {
		res.Code = RET_CODE_ERR
		return
	}

	// 检查玩家是否有队伍在该矿点
	team, ok := mineWarInfo.MineTeams[req.TeamId]
	if !ok || team.MineId != req.MineId {
		res.Code = RET_CODE_ERR
		return
	}

	// 检查攻击次数是否已满
	if mine.AttackNum >= mineWarInfo.Config.AttackNum {
		res.Code = RET_CODE_ERR
		return
	}

	// 检查冷却时间
	if time.Now().Unix()-team.LastAttackTime < mineWarInfo.Config.AttackCd {
		res.Code = RET_CODE_ERR
		return
	}

	// 计算伤害
	damage := req.AttackPower * 10 / 100

	// 减少护盾
	mine.Shield -= damage
	if mine.Shield < 0 {
		mine.Shield = 0
	}

	// 更新攻击次数
	mine.AttackNum++

	// 更新队伍攻击时间
	team.LastAttackTime = time.Now().Unix()

	// 保存数据
	self.Save()

	// 返回结果
	res.Code = RET_CODE_OK
	res.Points = []*MinePoint{mine}
	res.Teams = []*MineTeam{team}
}

func (self *MineWarMgr) LeaveMinePoint(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	// 加载MineWarInfo
	info, ok := self.MineWarInfo.Load(req.GroupId)
	if !ok {
		res.Code = RET_CODE_ERR
		return
	}

	// 获取MineWarInfo实例
	mineWarInfo := info.(*MineWarInfo)
	mineWarInfo.lock.Lock()
	defer mineWarInfo.lock.Unlock()

	// 获取矿点信息
	mine, ok := mineWarInfo.MinePoints[req.MineId]
	if !ok {
		res.Code = RET_CODE_ERR
		return
	}

	// 获取队伍信息
	team, ok := mineWarInfo.MineTeams[req.TeamId]
	if !ok || team.Status != 1 {
		res.Code = RET_CODE_ERR
		return
	}

	// 获取坑位信息
	position, ok := mineWarInfo.MinePositions[team.PositionId]
	if !ok || position.Status != 1 {
		res.Code = RET_CODE_ERR
		return
	}

	// 更新队伍状态
	team.Status = 0

	// 更新坑位状态
	position.PlayerId = 0
	position.LeaveTime = time.Now().Unix()
	position.Status = 0

	// 从玩家队伍列表中移除
	teams := mineWarInfo.Players[req.Uid]
	for i, tid := range teams {
		if tid == req.TeamId {
			teams = append(teams[:i], teams[i+1:]...)
			mineWarInfo.Players[req.Uid] = teams
			break
		}
	}

	// 保存数据
	self.Save()

	// 返回结果
	res.Code = RET_CODE_OK
	res.Points = []*MinePoint{mine}
	res.Positions = []*MinePosition{position}
	res.Teams = []*MineTeam{team}
}

func (self *MineWarMgr) GetMineOutput(req *RPC_MinePointReq, res *RPC_MinePointRes) {
	// 加载MineWarInfo
	info, ok := self.MineWarInfo.Load(req.GroupId)
	if !ok {
		res.Code = RET_CODE_ERR
		return
	}

	// 获取MineWarInfo实例
	mineWarInfo := info.(*MineWarInfo)
	mineWarInfo.lock.Lock()
	defer mineWarInfo.lock.Unlock()

	// 获取矿点信息
	mine, ok := mineWarInfo.MinePoints[req.MineId]
	if !ok {
		res.Code = RET_CODE_ERR
		return
	}

	// 检查是否为占领者
	if mine.OwnerId != req.Uid {
		res.Code = RET_CODE_ERR
		return
	}

	// 计算产出
	currentTime := time.Now().Unix()
	outputTime := currentTime - mine.LastSaveTime
	baseOutput := outputTime * mine.OutputRate

	// 坑位加成
	positionBonus := int64(0)
	for _, pos := range mineWarInfo.MinePositions {
		if pos.MineId == mine.MineId && pos.Status == 1 {
			positionBonus += mine.OutputRate * 10 / 100 // 10%加成
		}
	}

	totalOutput := baseOutput + positionBonus

	// 更新产出时间
	mine.LastSaveTime = currentTime
	mine.OutputNum += totalOutput

	// 保存数据
	self.Save()

	// 返回结果
	res.Code = RET_CODE_OK
	res.Points = []*MinePoint{mine}
	res.OutputNum = int(totalOutput)
	res.OutputItem = mine.OutputItem
}

func (self *MineWarMgr) Update() {
	self.lock.Lock()
	defer self.lock.Unlock()

	// 遍历所有MineWarInfo
	self.MineWarInfo.Range(func(key, value interface{}) bool {
		mineWarInfo := value.(*MineWarInfo)
		mineWarInfo.lock.Lock()
		defer mineWarInfo.lock.Unlock()

		// 更新所有矿点的产出
		for _, mine := range mineWarInfo.MinePoints {
			if mine.OwnerId != 0 {
				currentTime := time.Now().Unix()
				outputTime := currentTime - mine.LastSaveTime
				baseOutput := outputTime * mine.OutputRate

				// 坑位加成
				positionBonus := int64(0)
				for _, pos := range mineWarInfo.MinePositions {
					if pos.MineId == mine.MineId && pos.Status == 1 {
						positionBonus += mine.OutputRate * 10 / 100 // 10%加成
					}
				}

				// 计算总产出
				totalOutput := baseOutput + positionBonus

				// 更新产出
				mine.OutputNum += totalOutput
				mine.LastSaveTime = currentTime

			}
		}
		// 保存数据
		self.Save()
		return true
	})
}

func (self *MineWarMgr) Run(ctx context.Context) {
	// 每分钟执行一次
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			self.Update()
		case <-ctx.Done():
			self.lock.Lock()
			defer self.lock.Unlock()
			self.Update()
			return
		}
	}
}

func (self *MineWarMgr) OnTimer() {
	self.lock.Lock()
	defer self.lock.Unlock()
	self.Update()
}

func (self *MineWarMgr) IsWarTime() bool {
	t := time.Now()
	// 检查是否在休战期
	if t.Hour() >= WEEKLY_END_HOUR && t.Hour() < WEEKLY_START_HOUR {
		return true
	}

	// 检查是否在周四之前
	if t.Weekday() < time.Thursday {
		return true
	}

	return false
}
