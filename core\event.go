/*
@Time : 2020/5/10 23:02
<AUTHOR> 96121
@File : event
@Software: GoLand
*/
package core

const (
	PLAYER_EVENT_FIND_FRIEND             = 1  //! 寻找好友
	PLAYER_EVENT_ADD_FRIEND              = 2  //! 增加好友
	PLAYER_EVENT_DEL_FRIEND              = 3  //! 删除好友
	PLAYER_EVENT_AGREEE_FRIEND           = 4  //! 同意添加好友
	PLAYER_EVENT_REFUSE_FRIEND           = 5  //! 拒绝添加好友
	PLAYER_EVENT_BLACK_FRIEND            = 6  //! 拉黑好友
	PLAYER_EVENT_POWER_FRIEND            = 7  //! 友情点赠送
	PLAYER_EVENT_UPDATE_HIRE_HERO        = 11 //! 更新租借英雄信息
	PLAYER_EVENT_UPDATE_HIRE_HERO_SINGLE = 12 //! 更新租借英雄信息 单
	PLAYER_EVENT_AGREE_HIRE_HERO         = 13 //! 通知雇佣成功
	PLAYER_EVENT_REFUSE_HIRE_HERO        = 14 //! 通知雇佣拒绝
	PLAYER_EVENT_DELETE_HIRE             = 15 //! 移除雇佣

	UNION_EVENT_MASTER_FAIL          = 100 // 会长拒绝
	UNION_EVENT_MASTER_OK            = 101 // 会长同意
	UNION_EVENT_OUT_PLAYER           = 102 // 会长踢人
	UNION_EVENT_UNION_MODIFY         = 103 // 通知职位变更
	UNION_EVENT_UNION_SET_BRAVE_HAND = 104 // 设置无畏之手
	UNION_EVENT_UNION_HUNTER_AWARD   = 105 // 狩猎奖励
	UNION_EVENT_UNION_UPDATE         = 106 // 更新公会
	UNION_EVENT_UNION_SEND_MAIL      = 107 // 更新公会

	CHAT_NEW_WORLD_MESSAGE       = 210 //! 聊天新消息世界
	CHAT_NEW_UNION_MESSAGE       = 211 //! 聊天新消息公会
	CHAT_NEW_PRIVATE_MESSAGE     = 212 //! 聊天新消息私聊
	CHAT_GAP_PLAYER              = 213 //! 封禁聊天，通知客户端删除对应玩家的当前聊天记录
	CHAT_NEW_SERVER_MESSAGE      = 214 //! 聊天新消息跨服聊天
	CHAT_NEW_SYSTEM_MESSAGE      = 215 //! 聊天系统消息
	CHAT_NEW_DEMONSLAYER_MESSAGE = 216 //! 聊天新消息公会

	CITYBROKEN_BE_ATTACKEN = 220 //! 千城破被击败
	CITYBROKEN_INFO_UPDATE = 221 //! 千城破积分更新
	//比赛消息
	MATCH_CROSSARENA_UPDATE = 300 //! 跨服竞技场的被动推送
	//段位赛返回
	MATCH_CROSSLEVELARENA_ATTACKEND   = 400 //! 段位赛结果推送
	MATCH_CROSSLEVELARENA_BE_ATTACK   = 401 //! 段位赛被攻击
	MATCH_CROSSLEVELARENA_FIGHT_ERROR = 402 //! 段位赛未成功匹配

	//宇宙段位赛返回
	MATCH_COSMICARENA_ATTACKEND   = 410 //! 段位赛结果推送
	MATCH_COSMICARENA_BE_ATTACK   = 411 //! 段位赛被攻击
	MATCH_COSMICARENA_FIGHT_ERROR = 412 //! 段位赛未成功匹配

	//中心服对游戏服的广播
	CROSS_SERVER_GROUP_CONFIG_UPDATE    = 10001 //! 期数切换推送
	CROSS_SERVER_ARENA_RANK_REWARD      = 10002 //! 跨服竞技场发送排行奖励
	CROSS_SERVER_NEW_START              = 10003 //!
	CROSS_SERVER_LEVELARENA_RANK_REWARD = 10004 //! 跨服段位赛发送排行奖励
	CROSS_SERVER_AREA_CONFIG_UPDATE     = 10005 //! 大组通知
	//中心服对游戏服的广播
	CROSS_SERVER_ZONE_CONFIG_UPDATE      = 11001 //! 期数切换推送
	CROSS_SERVER_PEAK_ARENA_RANK_REWARD  = 11002 //! 跨服竞技场发送排行奖励
	CROSS_SERVER_COSMICARENA_RANK_REWARD = 11004 //! 跨服段位赛发送排行奖励

	CROSS_SERVER_PEAK_ARENA_CONFIG_UPDATE  = 11005 //! 跨服竞技场更新
	CROSS_SERVER_COSMICARENA_CONFIG_UPDATE = 11006 //! 跨服段位赛更新

	CROSS_SERVER_RETURN_EVENT_LINK = 11007 // 跨服绑定回归活动

	CHAMPION_ZONE_EVENT_FIGHT_UPDATE = 11008 // 跨服冠军赛战斗更新
	CHAMPION_ZONE_EVENT_SYN          = 11009 // 跨服冠军赛同步
	CHAMPION_ZONE_EVENT_RANK_REWARD  = 11010 // 排行奖励
	CHAMPION_ZONE_EVENT_BET_REWARD   = 11011 // 押注奖励
	CHAMPION_ZONE_EVENT_BET_RECORD   = 11012 // 押注纪录

	UNION_BATTLE_EVENT_SYN = 11013 //! 公会战状态同步

)

const (
	BATTLE_TYPE_TOWER                 = 1  // 爬塔
	BATTLE_TYPE_ARENA                 = 2  // 普通竞技场
	BATTLE_TYPE_UNION_HUNT_NORMAl     = 3  // 公会狩猎1
	BATTLE_TYPE_UNION_HUNT_SPECIAL    = 4  // 公会狩猎2
	BATTLE_TYPE_ARENA_SPECIAL         = 5  // 高阶竞技场
	BATTLE_TYPE_NORMAL                = 6  // 日常副本
	BATTLE_TYPE_RECORD_BOSS           = 7  // 暗域入侵
	BATTLE_TYPE_RECORD_CROSSARENA     = 8  // 跨服竞技战报
	BATTLE_TYPE_RECORD_BOSS_FESTIVAL  = 9  // 节日BOSS
	BATTLE_TYPE_RECORD_NEW_BOSS       = 11 //
	BATTLE_TYPE_RECORD_UNION_ACTIVITY = 12 //
	BATTLE_TYPE_CROSS_SERVER_ARENA    = 13 //苍穹战场
	BATTLE_TYPE_CHAMPION              = 14 //冠军赛
	BATTLE_TYPE_LEVELARENA            = 15 //段位赛
	BATTLE_TYPE_PEAKARENA             = 16 //巅峰竞技场 苍穹战场copy
	BATTLE_TYPE_COSMICARENA           = 17 //鬼灭宇宙段位赛
	BATTLE_TYPE_CHAMPIONZONE          = 18 //跨服冠军赛
	BATTLE_TYPE_RECORD_NEW_BOSS2      = 19 //
	BATTLE_TYPE_CITY_BROKEN           = 20 //千城破
)
