23:03:52.146208 app_master.go:119: [debug ] 连接数据库...
23:03:52.150102 dbserver.go:60: [debug ] db connect! ds_master_001
23:03:52.152048 dbserver.go:60: [debug ] db connect! ds_master_log_001
23:03:52.189026 app_master.go:123: [debug ] 启动逻辑处理...
23:03:52.189081 app_master.go:134: [debug ] 注册并启动服务...
23:03:52.190637 mgr_tower.go:150: [info  ] 迁移数据： san_towerbattleinfo 0
23:03:52.191708 mgr_tower.go:177: [info  ] san_towerbattleinfo 迁移数据OK
23:04:53.422218 mgr_gamedata.go:93: [debug ] SQL BASE EXEC: update `tbl_servergroup` set `param`='{"opentime":1681160400,"activecount":0,"maxfightname":"3234"}' where `serverid`=1 limit 1
23:04:53.424278 mgr_gamedata.go:93: [debug ] SQL BASE EXEC: update `tbl_servergroupconfig` set `starttime`=1742158800,`centretime`=1742745600,`endtime`=1743350400,`crossserverplayconfig`='{"ArenaConfig":{"StartTime":1742158800,"RewardTime":1742745600,"EndTime":1742745600,"Reward":0,"Periods":4},"LevelArenaConfig":{"StartTime":1742158800,"RewardTime":1742745600,"EndTime":1742745600,"Reward":0,"Periods":4}}' where `id`=1 limit 1
23:04:53.426370 mgr_gamedata.go:93: [debug ] SQL BASE EXEC: update `tbl_serverareaconfig` set `starttime`=1742158800,`endtime`=1743350400 where `id`=1 limit 1
