package battle

import (
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
	"net/rpc"
	"sync"
	//"time"
)

const WIN_ATTCK = 1
const WIN_DEFENCE = 2
const WIN_NULL = 0

const (
	BATTLE_REPLAY_RECORD = "san_replayrecord"
)

const (
	FIGHT_TYPE_PASS                   = 1  // 普通关卡 偶尔验算需要走战斗服
	FIGHT_TYPE_ARENA                  = 2  // 普通竞技场 (暂时没有走战斗服)
	FIGHT_TYPE_UNION_ACTIVITY         = 3  // 公会战  (暂时没有走战斗服)
	FIGHT_TYPE_ACTIVITY_BOSS          = 4  // 活动boss(未开启)  (暂时没有走战斗服)
	FIGHT_TYPE_ACTIVITY_BOSS_FESTIVAL = 5  // 活动节日boss(未开启)  (暂时没有走战斗服)
	FIGHT_TYPE_ACTIVITY_DAY_BOSS      = 6  // 活动 日boss宿命交锋(未开启)  (暂时没有走战斗服)
	FIGHT_TYPE_NEW_BOSS               = 7  // 新boss 缘一木偶
	FIGHT_TYPE_MINESWEEPER            = 8  // 扫雷 鼓之庭院 (暂时没有走战斗服)
	FIGHT_TYPE_NEWPIT                 = 9  // 地牢 空岛远征 蜘蛛山  (暂时没有走战斗服)
	FIGHT_TYPE_RESOURCE               = 10 // 试炼之地  (暂时没有走战斗服)
	FIGHT_TYPE_TOWER                  = 11 // 爬塔 偶尔验算需要走战斗服
	FIGHT_TYPE_CHAMPION_SCORE         = 12 // 冠军赛积分赛
	FIGHT_TYPE_CHAMPION_ELIMINATE     = 13 // 冠军赛淘汰赛
	FIGHT_TYPE_CROSSSERVERARENA       = 14 // 苍穹战场 决战空岛3v3
	FIGHT_TYPE_CROSSSERVERLEVELARENA  = 15 // 跨服段位赛
	FIGHT_TYPE_PEAKARENA              = 16 // 巅峰竞技场
	FIGHT_TYPE_COSMICARENA            = 17 // 宇宙竞技场段位赛
)

//func FightServer(w http.ResponseWriter, r *http.Request) {
//	defer func() {
//		x := recover()
//		if x != nil {
//			log.Println(x, string(debug.Stack()))
//			utils.LogError(x, string(debug.Stack()))
//		}
//	}()
//	msgtype := r.FormValue("msgtype")
//	if msgtype == "get" {
//		client := GetFightMgr().GetTopFight()
//		if client == nil {
//			w.Write([]byte("false"))
//		} else {
//			utils.LogDebug("有战斗数据")
//			var node FightServerNode
//			node.Id = client.Id
//			node.Att = client.Fight[0]
//			node.Def = client.Fight[1]
//			node.Random = client.Random
//			utils.LogDebug("有战斗数据", *node.Att, *node.Def)
//			w.Write(utils.HF_JtoB(&node))
//		}
//		return
//	} else if msgtype == "set" {
//		data := r.FormValue("data")
//		//utils.LogDebug(data)
//		var node core.FightResultNode
//		err := json.Unmarshal([]byte(data), &node)
//		if err == nil {
//			GetFightMgr().SetResult(&node)
//		}
//		client := GetFightMgr().GetTopFight()
//		if client == nil {
//			//log.Println("无战斗数据")
//			w.Write([]byte("false"))
//		} else {
//			utils.LogDebug("有战斗数据", client.Id)
//			var node FightServerNode
//			node.Id = client.Id
//			node.Att = client.Fight[0]
//			node.Def = client.Fight[1]
//			node.Random = client.Random
//			w.Write(utils.HF_JtoB(&node))
//		}
//		return
//	}
//}
//
//func (self *FightMgr) GetTopFight() *core.FightResult {
//	if len(self.WaitForGet) <= 0 {
//		return nil
//	}
//	self.FightLock.RLock()
//	defer self.FightLock.RUnlock()
//
//	//! 取出一个战斗返回
//	var ret *core.FightResult = nil
//	var needDelete = false
//	for _, v := range self.WaitForGet {
//		//! 如果战斗没有取出，就返回
//		if v.GetNum == 0 || core.TimeServer().Unix()-v.GetTime > 45 {
//			v.GetNum++
//			v.GetTime = core.TimeServer().Unix()
//
//			if v.GetNum > 3 {
//				v.NeedDelete = true
//				needDelete = true
//				continue
//			}
//
//			if info, ok := self.MapAutoFight[v.FightId]; ok {
//				ret = info
//				break
//			} else {
//				//! 不存在则删除
//				needDelete = true
//				v.NeedDelete = true
//			}
//		}
//	}
//
//	if needDelete == true {
//		if len(self.WaitForGet) == 1 {
//			self.WaitForGet = make([]*WaitForGetInfo, 0)
//		} else {
//			for i := 0; i < len(self.WaitForGet); {
//				//! 删除元素
//				if self.WaitForGet[i].NeedDelete == true {
//					if len(self.WaitForGet) == 1 {
//						self.WaitForGet = make([]*WaitForGetInfo, 0)
//					} else {
//						self.WaitForGet[i] = self.WaitForGet[len(self.WaitForGet)-1]
//						self.WaitForGet = self.WaitForGet[:len(self.WaitForGet)-1]
//					}
//				} else {
//					i++
//				}
//			}
//		}
//	}
//
//	return ret
//}

type FightServerNode struct {
	Id     int64               `json:"id"`
	Att    *model.JS_FightInfo `json:"att"`
	Def    *model.JS_FightInfo `json:"def"`
	Random int                 `json:"random"`
}

type WaitForGetInfo struct {
	FightId    int64
	GetTime    int64
	GetNum     int
	NeedDelete bool
	DeleteMap  bool
	DelayTime  int64 //! 延迟时间
}
type FightMgr struct {
	Locker *sync.RWMutex

	FightId      int64                        //! 战斗id
	MapAutoFight map[int64]*model.FightResult //! 自动战斗
	WaitForGet   []*WaitForGetInfo            //! 等待战斗服务器去取的队列
	FightLock    *sync.RWMutex
	Client       *rpc.Client //! RPC连接
}

var fightmgrsingleton *FightMgr = nil

// ! public
func GetFightMgr() *FightMgr {
	if fightmgrsingleton == nil {
		fightmgrsingleton = new(FightMgr)
		fightmgrsingleton.Locker = new(sync.RWMutex)
		fightmgrsingleton.FightLock = new(sync.RWMutex)
		fightmgrsingleton.MapAutoFight = make(map[int64]*model.FightResult)
		conn, err := rpc.DialHTTP("tcp", core.GetMasterApp().GetConfig().BattleSvr)
		if err == nil {
			fightmgrsingleton.Client = conn
		} else {
			fightmgrsingleton.Client = nil
		}
	}

	return fightmgrsingleton
}

func (self *FightMgr) AddFightToBattle(attack *model.JS_FightInfo, defence *model.JS_FightInfo, random int) *model.FightResult {
	record := self.GetFightInfoID()
	self.FightLock.Lock()
	defer self.FightLock.Unlock()
	utils.LogDebug("fightRecordId = ", record)

	client := new(model.FightResult)
	client.Id = record
	client.Fight[0] = attack
	client.Fight[1] = defence
	client.Random = random
	self.MapAutoFight[record] = client
	self.WaitForGet = append(self.WaitForGet, &WaitForGetInfo{FightId: client.Id})

	return client
}

func (self *FightMgr) GetFightInfoID() int64 {
	self.FightLock.Lock()
	defer self.FightLock.Unlock()

	self.FightId++
	record := (model.TimeServer().Unix()%100000)*1000 + self.FightId%1000 + int64(100000000*core.GetMasterApp().GetConfig().ServerId)
	return record
}

func (self *FightMgr) GetResult(fightid int64) *model.FightResult {
	self.FightLock.Lock()
	defer self.FightLock.Unlock()

	result, ok := self.MapAutoFight[fightid]
	if !ok || result.Result == WIN_NULL { //! 没有结果
		return nil
	}
	return result
}
func (self *FightMgr) GetFightResult(fightId int64) *model.FightResult {
	self.FightLock.Lock()
	defer self.FightLock.Unlock()
	data, ok := self.MapAutoFight[fightId]
	if !ok {
		return nil
	}
	return data
}

// 战报存储数据库
func (self *FightMgr) SetResult(node *model.FightResultNode) {
	self.FightLock.Lock()
	defer self.FightLock.Unlock()

	if node == nil {
		return
	}

	result, ok := self.MapAutoFight[node.Fightid]
	if !ok {
		return
	}

	result.Info = node.Info
	result.IsSet = true //标记是通过战斗服务器拿到的结果
	if node.Winner == 1 {
		result.Result = WIN_ATTCK
	} else {
		result.Result = WIN_DEFENCE
	}
	result.ResultDetail = node
	utils.LogDebug("战斗结果改变:", result.Id, ",", result.Result, node)

	if result.ResultDetail != nil && result.ResultDetail.Record != nil && len(result.ResultDetail.Record) > 0 {
		fightData := model.FightReplay{}
		fightData.Record = result.ResultDetail.Record
		fightData.FightType = result.FightType
		fightData.LevelID = 0
		db.HMSetRedisEx(BATTLE_REPLAY_RECORD, result.Id, &fightData, utils.HOUR_SECS*24)
	}

	//! 删除记录
	for i := 0; i < len(self.WaitForGet); i++ {
		//! 删除元素
		if self.WaitForGet[i].FightId == result.Id {
			self.WaitForGet[i] = self.WaitForGet[len(self.WaitForGet)-1]
			self.WaitForGet = self.WaitForGet[:len(self.WaitForGet)-1]
			break
		}
	}
	//写入数据库
	self.MapAutoFight[node.Fightid] = result
}

type FightInfo struct {
	FightId   int64  `json:"fight_id"`   // 战报Id
	Result    int    `json:"result"`     // 战斗方式
	EnemyName string `json:"enemy_name"` // 敌方
	Icon      int    `json:"icon"`       // 敌方Icon
	Fight     int64  `json:"fight"`      // 敌方战力
	Time      int    `json:"time"`       // 时间
	Side      int    `json:"side"`       // 1 进攻方  2 防守方
	SecKill   int    `json:"sec_kill"`   // 0 不是秒杀 1 秒杀
	TeamA     int    `json:"team_a"`     // 军团战队伍1
	TeamB     int    `json:"team_b"`     // 军团战队伍2
	Group     int    `json:"group"`      // 分组
	IsSet     bool   `json:"isset"`      // 是否通过战斗服务器
}

func (self *FightMgr) DelResult(fightid int64) {
	self.FightLock.Lock()
	delete(self.MapAutoFight, fightid)
	self.FightLock.Unlock()
}
