package player

import (
	"fmt"
	json "github.com/bytedance/sonic"
	"master/db"
	"sync"
)

const (
	RETURN_EVENT_INVITE_CODE = "return_event_invite_code"
)

type InviteCodeInfo struct {
	Uid      int64  `json:"uid"`       //! 角色ID
	ServerId int    `json:"server_id"` //! 服务器ID
	Code     string `json:"code"`      //! 邀请码
	Account  string `json:"account"`   //! 账号
}

//! 回归事件
type ReturnEventMgr struct {
	Locker *sync.RWMutex //! 数据锁
}

//! 单例模式
var s_return_event_mgr *ReturnEventMgr

func GetReturnEventMgr() *ReturnEventMgr {
	if s_return_event_mgr == nil {
		s_return_event_mgr = new(ReturnEventMgr)
		s_return_event_mgr.Locker = new(sync.RWMutex)
	}
	return s_return_event_mgr
}

func (self *ReturnEventMgr) RegInviteCode(req *RPC_RegInviteCodeReq) (int, error) {
	data := InviteCodeInfo{}
	data.Uid = req.Uid
	data.ServerId = req.ServerId
	data.Code = req.Code
	data.Account = req.Account
	return 0, db.HMSetRedisExStringKey(RETURN_EVENT_INVITE_CODE, req.Code, &data, 0)
}

func (self *ReturnEventMgr) GetInviteCode(code string) (error, *InviteCodeInfo) {
	var data *InviteCodeInfo
	value, flag, err1 := db.HGetRedisExStringKey(RETURN_EVENT_INVITE_CODE, code, fmt.Sprintf("%s", code))
	if err1 != nil || !flag {
		return err1, nil
	}

	err2 := json.Unmarshal([]byte(value), &data)
	if err2 != nil {
		return err2, nil
	}

	return nil, data
}
