package crossserver

import (
	"encoding/json"
	"fmt"
	"master/center/server"
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
	"strconv"
	"sync"
	"time"
)

const (
	RETCODE_OK         = 0 //! 没有错误
	RETCODE_DATA_ERROR = 1 //! 数据异常
)

const (
	CityBrokenRedisFightRecord  = "citybrokenfightrecord"
	CityBrokenRedisRecord       = "citybrokenrecord"
	CityBrokenRedisFightId      = "citybrokenfightid"
	CityBrokenRedisRecordNum    = 20
	CITY_BROKEN_ATTACK_CD       = 90
	CITY_BROKEN_STATUS_SIMULATE = 2 // 海选赛
	CITY_BROKEN_STATUS_FIGHT    = 3 // 总决赛
)

type CityBrokenConfig struct {
	Id           int
	KeyId        int
	StartTime    int64 // 开始时间
	AdTime       int64 //预选赛
	SimulateTime int64 //海选赛
	FightTime    int64 //总决赛
	ResultTime   int64 //展示期
	EndTime      int64 // 总结束时间

	db.DataUpdate
}

type CityBrokenUpdateInfo struct {
	Point  int64   `json:"point"`
	TeamId []int64 `json:"team_id"`
}

func (self *CityBrokenConfig) Decode() {
}

// ! 将data数据写入数据库
func (self *CityBrokenConfig) Encode() {
}

type JS_CityInfo struct {
	CityBrokenId int64 `json:"city_broken_id"`
	TeamType     int   `json:"team_type"`
	StartTime    int64 `json:"start_time"`  // 占领起始时间
	ShieldTime   int64 `json:"shield_time"` // 护盾保护起始时间
}

func (self *CityBrokenInfo) JS_CityInfoChange(city *CityBrokenCity) *JS_CityInfo {
	cityInfo := new(JS_CityInfo)
	cityInfo.CityBrokenId = city.CityBrokenId
	cityInfo.TeamType = int(city.fightInfo.Param)
	cityInfo.StartTime = city.StartTime
	cityInfo.ShieldTime = city.ShieldTime
	return cityInfo
}

type CityBrokenCity struct {
	CityBrokenId int64
	GroupId      int //预留字段 暂不启用 如果以后要做服务器分组的话不用额外加字段了
	SeasonTime   int64
	FightInfo    string
	StartTime    int64 // 占领起始时间
	AttackTime   int64 // 攻击者的攻击时间
	AttackUid    int64 // 攻击者的uid
	ShieldTime   int64 // 护盾保护起始时间

	fightInfo *model.JS_FightInfo
	db.DataUpdate
}

type CityBrokenServer struct {
	ServerId   int    `json:"server_id"`
	ServerName string `json:"server_name"`
}

type CityBrokenPlayer struct {
	Uid      int64           `json:"uid"`
	ServerId int             `json:"server_id"`
	TeamCity map[int64]int64 `json:"team_city"` // map[队伍id][城市id]
}

type BrokenCity struct {
	Levelid          int64 `json:"levelid"`
	ActivityStage    int   `json:"activity_stage"`
	ActivityLevel    int   `json:"activity_level"`
	ActivityLine     int   `json:"activity_line"`
	OutputItem       int   `json:"output_item"`
	OutputNum        int   `json:"output_num"`
	RestrictionPoint int64 `json:"restriction_point"`
	RestrictionTime  int64 `json:"restriction_time"`
	Shield           int64 `json:"shield"`
}
type BrokenCityActivityConfig struct {
	ActivityGroup    int   `json:"activity_group"`
	KeepTime         int   `json:"keep_time"`         //持续
	AdTime           int64 `json:"ad_time"`           //预选赛
	SimulateTime     int64 `json:"simulate_time"`     //海选赛
	FightTime        int64 `json:"fight_time"`        //总决赛
	ResultTime       int64 `json:"result_time"`       //展示期
	AdAdvance        int   `json:"ad_advance"`        //第1阶段晋级人数
	SimulateAdvance  int   `json:"simulate_advance"`  //第2阶段可晋级人数
	BoothProtection  int   `json:"booth_protection"`  //占位保护时长秒
	AttackNum        int   `json:"Attack_num"`        //每日攻击次数
	AttackSale       int   `json:"Attack_sale"`       //攻击购买次数
	ShieldProtection int   `json:"Shield_protection"` //护盾保护时长秒
	ShieldFreeNum    int   `json:"Shield_free_num"`   //每日护盾免费次数
	ShieldSale       int   `json:"Shield_sale"`       //每日护盾购买次数
	RevivalTime      int64 `json:"revival_time"`      //队伍复活时长
	BoothMaxtime     int   `json:"booth_maxtime"`     //坑位最长占领时间
	AttackCd         int64 `json:"Attack_cd"`         //队伍攻击CD时间
}
type CityBrokenPoint struct {
	Id    int `json:"id"`
	Point int `json:"point"`
}

type CityBrokenInfo struct {
	CityBrokenCity        map[int]map[int64]*CityBrokenCity   // 城市数据 map[group][cityid]
	CityBrokenPlayer      map[int]map[int64]*CityBrokenPlayer // 玩家数据 map[group][uid]
	CityBrokenServer      map[int]map[int]*CityBrokenServer   // 参与的服务器数据 map[group][serverid]
	CityBrokenServerGroup map[int]int                         // 服务器所在的组 map[serverid]group

	CityBrokenConfig *CityBrokenConfig
	lock             *sync.RWMutex
}

// ! 千城破城主战斗
type CityBrokenMgr struct {
	CityBrokenInfo *CityBrokenInfo //!

	BrokenCityConfigMap         map[int64]*BrokenCity             //City_Broken
	BrokenCityActivityConfigMap map[int]*BrokenCityActivityConfig //City_Activity_Config
}

// ! 跨服战管理类
var s_citybrokenmgr *CityBrokenMgr = nil

func GetCityBrokenMgr() *CityBrokenMgr {
	if s_citybrokenmgr == nil {
		s_citybrokenmgr = new(CityBrokenMgr)
		s_citybrokenmgr.LoadCsv()
	}
	return s_citybrokenmgr
}

// ! 将数据库数据写入data-无特殊结构
func (self *CityBrokenCity) Decode() {
	json.Unmarshal([]byte(self.FightInfo), &self.fightInfo)
}

// ! 将data数据写入数据库
func (self *CityBrokenCity) Encode() {
	self.FightInfo = utils.HF_JtoA(&self.fightInfo)
}

func (self *CityBrokenMgr) LoadCsv() {
	self.BrokenCityConfigMap = make(map[int64]*BrokenCity)
	breakCity := make([]*BrokenCity, 0)
	utils.GetCsvUtilMgr().LoadCsv("City_Broken", &breakCity)
	for _, v := range breakCity {
		self.BrokenCityConfigMap[v.Levelid] = v
	}
	self.BrokenCityActivityConfigMap = make(map[int]*BrokenCityActivityConfig)
	utils.GetCsvUtilMgr().LoadCsv("City_Activity_Config", &self.BrokenCityActivityConfigMap)

	return
}

func (self *CityBrokenMgr) GetCityBrokenLevelConfig(levelId int64) *BrokenCity {
	_, ok := self.BrokenCityConfigMap[levelId]
	if !ok {
		return nil
	}
	return self.BrokenCityConfigMap[levelId]
}

func (self *CityBrokenMgr) GetBrokenCityActivityConfig(group int) *BrokenCityActivityConfig {
	_, ok := self.BrokenCityActivityConfigMap[group]
	if !ok {
		return nil
	}
	return self.BrokenCityActivityConfigMap[group]
}

func (self *CityBrokenMgr) CheckCityBroken(req *RPC_UpdateActivityReq) {
	if self.CityBrokenInfo.CityBrokenConfig.EndTime == 0 {
		self.CityBrokenInfo.InitCityBrokenConfig(req.StartTime, req.RewardTime, req.EndTime, req.Periods)
		self.GetAllData()
	} else if self.CityBrokenInfo.CityBrokenConfig.EndTime != req.EndTime {
		self.CityBrokenInfo.InitCityBrokenConfig(req.StartTime, req.RewardTime, req.EndTime, req.Periods)
		self.CityBrokenInfo.CleanCityBroken()
	}
}

func (self *CityBrokenInfo) CleanCityBroken() {
	self.lock.Lock()
	defer self.lock.Unlock()
	for _, group := range self.CityBrokenCity {
		for _, v := range group {
			if v.SeasonTime != self.CityBrokenConfig.EndTime {
				v.SeasonTime = self.CityBrokenConfig.EndTime
				v.fightInfo = NewFightInfo(v.CityBrokenId)
				v.StartTime = 0
				v.ShieldTime = 0
				v.AttackUid = 0
				v.AttackTime = 0
				v.Decode()
			}
		}
	}
	self.CityBrokenPlayer = make(map[int]map[int64]*CityBrokenPlayer)
	self.CityBrokenServer = make(map[int]map[int]*CityBrokenServer)
	self.CityBrokenServerGroup = make(map[int]int)
}

////每个月1号和16号
//func GetSeasonRefreshTime() int64 {
//	now := time.Now()
//	currentYear, currentMonth, _ := now.Date()
//	currentLocation := now.Location()
//	nowMonthFirstDay := time.Date(currentYear, currentMonth, 1, 5, 0, 0, 0, currentLocation).Unix()
//	nowMonthMidDay := time.Date(currentYear, currentMonth, 1, 5, 0, 0, 0, currentLocation).
//		AddDate(0, 0, 15).Unix()
//	nextMonthDay := time.Date(currentYear, currentMonth, 1, 5, 0, 0, 0, currentLocation).
//		AddDate(0, 1, 0).Unix()
//	if now.Unix() < nowMonthFirstDay {
//		return nowMonthFirstDay
//	}
//	if now.Unix() < nowMonthMidDay {
//		return nowMonthMidDay
//	}
//	return nextMonthDay
//}

func NewFightInfo(id int64) *model.JS_FightInfo {
	data := new(model.JS_FightInfo)
	data.Rankid = int(id)
	return data
}

func NewCityBrokenUser(cityBrokenId int64, groupid int) *CityBrokenCity {
	data := new(CityBrokenCity)
	data.CityBrokenId = cityBrokenId
	data.GroupId = groupid
	data.SeasonTime = GetCityBrokenMgr().CityBrokenInfo.CityBrokenConfig.EndTime
	data.fightInfo = NewFightInfo(cityBrokenId)
	data.StartTime = 0
	db.InsertTable("tbl_citybrokenuser", data, 0, false)
	data.Init("tbl_citybrokenuser", data, false)
	return data
}

func (self *CityBrokenInfo) GetCityBrokenInfo(groupid int, cityid int64) *CityBrokenCity {
	_, groupok := self.CityBrokenCity[groupid]
	if !groupok {
		return nil
	}

	info, ok := self.CityBrokenCity[groupid][cityid]
	if !ok {
		return nil
	}
	return info
}

func (self *CityBrokenMgr) NewCityBrokenInfo() *CityBrokenInfo {
	data := new(CityBrokenInfo)
	data.CityBrokenCity = make(map[int]map[int64]*CityBrokenCity, 0)
	data.CityBrokenPlayer = make(map[int]map[int64]*CityBrokenPlayer, 0)
	data.CityBrokenServer = make(map[int]map[int]*CityBrokenServer, 0)
	data.CityBrokenServerGroup = make(map[int]int)
	data.lock = new(sync.RWMutex)
	return data
}

func (self *CityBrokenInfo) InitCityBrokenConfig(starttime, rewardtime, enttime int64, keyid int) {
	self.lock.Lock()
	defer self.lock.Unlock()
	self.CityBrokenConfig.KeyId = keyid
	self.CityBrokenConfig.StartTime = starttime
	self.CityBrokenConfig.EndTime = enttime

	config := GetCityBrokenMgr().GetBrokenCityActivityConfig(self.CityBrokenConfig.KeyId / 1000)
	// 第一阶段结束时间 第二阶段的开始时间
	self.CityBrokenConfig.AdTime = self.CityBrokenConfig.StartTime + config.AdTime*utils.DAY_SECS
	// 第二阶段的结束时间 第三阶段的开始时间
	self.CityBrokenConfig.SimulateTime = self.CityBrokenConfig.AdTime + config.SimulateTime*utils.DAY_SECS
	// 第三阶段的结束时间
	self.CityBrokenConfig.FightTime = self.CityBrokenConfig.SimulateTime + config.FightTime*utils.DAY_SECS
	// 结算期结束时间
	self.CityBrokenConfig.ResultTime = self.CityBrokenConfig.FightTime + config.ResultTime*utils.DAY_SECS
}

// ! 从数据库载入数据
func (self *CityBrokenMgr) GetAllData() {
	//计算赛季刷新时间
	if self.CityBrokenInfo == nil {
		self.CityBrokenInfo = self.NewCityBrokenInfo()
	}

	if self.CityBrokenInfo.CityBrokenConfig == nil {
		self.CityBrokenInfo.CityBrokenConfig = new(CityBrokenConfig)
		queryConfigStr := fmt.Sprintf("select * from `%s` limit 1;", "tbl_citybrokenconfig")
		ret := db.GetDBMgr().DBUser.GetOneData(queryConfigStr, self.CityBrokenInfo.CityBrokenConfig, "tbl_citybrokenconfig", 0)
		if ret == true {
			self.CityBrokenInfo.CityBrokenConfig.Decode()
		} else {
			self.CityBrokenInfo.CityBrokenConfig.Id = 1
			db.InsertTable("tbl_citybrokenconfig", self.CityBrokenInfo.CityBrokenConfig, 0, false)
		}
		self.CityBrokenInfo.CityBrokenConfig.Init("tbl_citybrokenconfig", self.CityBrokenInfo.CityBrokenConfig, false)
	}

	if self.CityBrokenInfo.CityBrokenConfig.EndTime == 0 {
		return
	}

	queryStr := fmt.Sprintf("select * from `tbl_citybrokenuser`;")
	var msg CityBrokenCity
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	for i := 0; i < len(res); i++ {
		data := res[i].(*CityBrokenCity)
		_, ok := self.CityBrokenInfo.CityBrokenCity[data.GroupId]
		if !ok {
			self.CityBrokenInfo.CityBrokenCity[data.GroupId] = make(map[int64]*CityBrokenCity)
		}

		self.CityBrokenInfo.CityBrokenCity[data.GroupId][data.CityBrokenId] = data
		data.Init("tbl_citybrokenuser", data, false)

		if data.SeasonTime != self.CityBrokenInfo.CityBrokenConfig.EndTime {
			data.FightInfo = ""
			data.SeasonTime = self.CityBrokenInfo.CityBrokenConfig.EndTime
		}
		data.Decode()

		if data != nil && data.fightInfo != nil {
			if data.fightInfo.Server > 0 {
				self.CityBrokenInfo.LoadSetCityBrokenServerList(data.fightInfo.Server, data.GroupId)
			}
			// 说明是玩家
			if data.fightInfo.Uid != 0 {
				self.CityBrokenInfo.AddCityBrokenPlayer(data)
			}
		}
	}
	return
}

// ! 主逻辑循环
func (self *CityBrokenMgr) Run() {
	//! 每秒执行一次
	ticker := time.NewTicker(time.Minute * 1)
	for {
		<-ticker.C
		self.OnTimer()
	}
	ticker.Stop()
}

// ! 主逻辑循环
func (self *CityBrokenMgr) OnTimer() {
	self.CityBrokenInfo.lock.Lock()
	defer self.CityBrokenInfo.lock.Unlock()
	self.CityBrokenInfo.OnTimer()
}

func (self *CityBrokenInfo) OnTimer() {
	timeNow := model.TimeServer()
	timeNowUnix := timeNow.Unix()
	// 时间还没到第二阶段和第三阶段 或者是结算期
	if timeNowUnix < self.CityBrokenConfig.AdTime || timeNowUnix >= self.CityBrokenConfig.FightTime || timeNow.Hour() < 12 || timeNow.Hour() >= 22 {
		return
	}

	updatePoint := false
	updateTeam := false
	if timeNow.Hour() == 21 && timeNow.Minute() == 59 && timeNowUnix < self.CityBrokenConfig.SimulateTime {
		updatePoint = true
	}

	if self.CityBrokenPlayer == nil {
		self.CityBrokenPlayer = make(map[int]map[int64]*CityBrokenPlayer)
	}
	doublePoint := false
	if timeNow.Hour() >= 21 {
		doublePoint = true
	}
	for group, value := range self.CityBrokenPlayer {
		serverPlayer := make(map[int]map[int64]*CityBrokenUpdateInfo)
		serverPoint := make(map[int]int64)
		for _, playerInfo := range value {
			deleteTeam := []int64{}
			if timeNowUnix < self.CityBrokenConfig.SimulateTime {
				addPoint := 0
				var fightInfo *model.JS_FightInfo = nil
				for teamid, cityId := range playerInfo.TeamCity {
					cityConfig := GetCityBrokenMgr().GetCityBrokenLevelConfig(cityId)
					if cityConfig == nil {
						continue
					}
					if cityConfig.ActivityStage != CITY_BROKEN_STATUS_SIMULATE {
						continue
					}

					cityInfo := self.GetCityBrokenInfo(group, cityId)
					if cityInfo == nil || cityInfo.fightInfo == nil || cityInfo.fightInfo.Uid != playerInfo.Uid {
						deleteTeam = append(deleteTeam, teamid)
						continue
					}

					if timeNowUnix-cityInfo.StartTime > 3600 {
						deleteTeam = append(deleteTeam, teamid)
						cityInfo.fightInfo = NewFightInfo(cityId)
						cityInfo.StartTime = 0
						cityInfo.AttackTime = 0
						cityInfo.AttackUid = 0
						cityInfo.ShieldTime = 0
						continue
					}

					if fightInfo == nil {
						fightInfo = cityInfo.fightInfo
					} else {
						if fightInfo.Deffight < cityInfo.fightInfo.Deffight {
							fightInfo = cityInfo.fightInfo
						}
					}
					addPoint += cityConfig.OutputNum
				}
				if fightInfo != nil {
					rankInfo := new(model.RankInfo)
					rankInfo.Uid = playerInfo.Uid
					rankInfo.RankId = RANK_TYPE_CITY_BROKEN_POINT1
					rankInfo.Period = group
					rankInfo.SvrId = fightInfo.Server
					rankInfo.SvrName = server.GetServerMgr().GetServerName(fightInfo.Server)
					rankInfo.UName = fightInfo.Uname
					rankInfo.Level = fightInfo.Level
					rankInfo.Icon = fightInfo.Iconid
					rankInfo.Portrait = fightInfo.Portrait
					rankInfo.Title = fightInfo.Title
					rankInfo.Vip = fightInfo.Vip
					rankInfo.Fight = fightInfo.Deffight
					rankInfo.FameId = fightInfo.FameId
					rankInfo.Camp = fightInfo.Camp
					rankInfo.UnionName = fightInfo.UnionName
					rankInfo.Time = model.TimeServer().Unix()
					rankInfo.Num = int64(addPoint)

					msg := new(RPC_UploadRankReq)
					msg.ServerId = fightInfo.Server
					msg.RankId = RANK_TYPE_CITY_BROKEN_POINT1
					msg.RankInfo = rankInfo
					GetRankMgr().UploadRank(msg, nil)
				}
			} else {
				addPoint := 0
				var fightInfo *model.JS_FightInfo = nil
				for teamid, cityId := range playerInfo.TeamCity {
					cityConfig := GetCityBrokenMgr().GetCityBrokenLevelConfig(cityId)
					if cityConfig == nil {
						continue
					}
					if cityConfig.ActivityStage != CITY_BROKEN_STATUS_FIGHT {
						continue
					}
					cityInfo := self.GetCityBrokenInfo(group, cityId)
					if cityInfo == nil || cityInfo.fightInfo == nil || cityInfo.fightInfo.Uid != playerInfo.Uid {
						deleteTeam = append(deleteTeam, teamid)
						continue
					}

					if timeNowUnix-cityInfo.StartTime > 3600 {
						deleteTeam = append(deleteTeam, teamid)
						cityInfo.fightInfo = NewFightInfo(cityId)
						cityInfo.StartTime = 0
						cityInfo.AttackTime = 0
						cityInfo.AttackUid = 0
						cityInfo.ShieldTime = 0
						continue
					}

					if fightInfo == nil {
						fightInfo = cityInfo.fightInfo
					} else {
						if fightInfo.Deffight < cityInfo.fightInfo.Deffight {
							fightInfo = cityInfo.fightInfo
						}
					}
					if !doublePoint {
						addPoint += cityConfig.OutputNum
					} else {
						addPoint += cityConfig.OutputNum * 2
					}
				}
				if fightInfo != nil {
					rankInfo := new(model.RankInfo)
					rankInfo.Uid = playerInfo.Uid
					rankInfo.RankId = RANK_TYPE_CITY_BROKEN_POINT2
					rankInfo.Period = group
					rankInfo.SvrId = fightInfo.Server
					rankInfo.SvrName = server.GetServerMgr().GetServerName(fightInfo.Server)
					rankInfo.UName = fightInfo.Uname
					rankInfo.Level = fightInfo.Level
					rankInfo.Icon = fightInfo.Iconid
					rankInfo.Portrait = fightInfo.Portrait
					rankInfo.Title = fightInfo.Title
					rankInfo.Vip = fightInfo.Vip
					rankInfo.Fight = fightInfo.Deffight
					rankInfo.FameId = fightInfo.FameId
					rankInfo.Camp = fightInfo.Camp
					rankInfo.UnionName = fightInfo.UnionName
					rankInfo.Time = model.TimeServer().Unix()
					rankInfo.Num = int64(addPoint)

					msg := new(RPC_UploadRankReq)
					msg.ServerId = fightInfo.Server
					msg.RankId = RANK_TYPE_CITY_BROKEN_POINT2
					msg.RankInfo = rankInfo
					GetRankMgr().UploadRank(msg, nil)

					_, ok3 := serverPoint[playerInfo.ServerId]
					if !ok3 {
						serverPoint[playerInfo.ServerId] = int64(addPoint)
					} else {
						serverPoint[playerInfo.ServerId] += int64(addPoint)
					}
				}
			}
			if len(deleteTeam) > 0 {
				if !updateTeam {
					updateTeam = true
				}
				_, ok1 := serverPlayer[playerInfo.ServerId]
				if !ok1 {
					serverPlayer[playerInfo.ServerId] = make(map[int64]*CityBrokenUpdateInfo)
				}
				_, ok2 := serverPlayer[playerInfo.ServerId][playerInfo.Uid]
				if !ok2 {
					serverPlayer[playerInfo.ServerId][playerInfo.Uid] = new(CityBrokenUpdateInfo)
					serverPlayer[playerInfo.ServerId][playerInfo.Uid].TeamId = deleteTeam
				} else {
					serverPlayer[playerInfo.ServerId][playerInfo.Uid].TeamId = deleteTeam
				}
				for _, teamid := range deleteTeam {
					delete(playerInfo.TeamCity, teamid)
				}
			}
			if updatePoint {
				_, ok1 := serverPlayer[playerInfo.ServerId]
				if !ok1 {
					serverPlayer[playerInfo.ServerId] = make(map[int64]*CityBrokenUpdateInfo)
				}
				myPoint := int64(0)
				rankInfo := GetRankMgr().GetMyRank(RANK_TYPE_CITY_BROKEN_POINT1, int(group), playerInfo.Uid)
				if rankInfo != nil {
					myPoint = rankInfo.Num
				}
				_, ok2 := serverPlayer[playerInfo.ServerId][playerInfo.Uid]
				if !ok2 {
					serverPlayer[playerInfo.ServerId][playerInfo.Uid] = new(CityBrokenUpdateInfo)
					serverPlayer[playerInfo.ServerId][playerInfo.Uid].Point = myPoint
				} else {
					serverPlayer[playerInfo.ServerId][playerInfo.Uid].Point = myPoint
				}
			}
		}

		if updatePoint || updateTeam {
			for server, data := range serverPlayer {
				core.GetCenterApp().AddEvent(server, core.CITYBROKEN_INFO_UPDATE, 0,
					0, 0, utils.HF_JtoA(data))
			}
		}

		if len(serverPoint) > 0 {
			for serverid, point := range serverPoint {
				rankInfo := new(model.RankInfo)
				rankInfo.Uid = int64(serverid)
				rankInfo.RankId = RANK_TYPE_CITY_BROKEN_SERVER_POINT2
				rankInfo.Period = group
				rankInfo.SvrId = serverid
				rankInfo.SvrName = server.GetServerMgr().GetServerName(serverid)
				rankInfo.UName = server.GetServerMgr().GetServerName(serverid)
				rankInfo.Num = point
				rankInfo.Time = model.TimeServer().Unix()
				msg := new(RPC_UploadRankReq)
				msg.ServerId = serverid
				msg.RankId = RANK_TYPE_CITY_BROKEN_SERVER_POINT2
				msg.RankInfo = rankInfo
				GetRankMgr().UploadRank(msg, nil)
			}
		}

	}
	self.GMCheckAllCity()
	/*now := time.Now().Unix()*/
	//if GetSeasonRefreshTime() == self.SeasonRefreshTime {
	//	return
	//}
	//self.lock.Lock()
	//defer self.lock.Unlock()
	//self.SeasonRefreshTime = GetSeasonRefreshTime()
	//for _, group := range self.CityBrokenCity {
	//	for _, v := range group {
	//		if v.SeasonTime != self.SeasonRefreshTime {
	//			v.SeasonTime = self.SeasonRefreshTime
	//			v.fightInfo = NewFightInfo(v.CityBrokenId)
	//			v.Decode()
	//		}
	//	}
	//}
}

func (self *CityBrokenMgr) GetCityBrokenFightRecord(req *RPC_CityBrokenRecordReq, res *RPC_CityBrokenRecordRes) {
	if self.CityBrokenInfo == nil {
		return
	}
	self.CityBrokenInfo.GetCityBrokenFightRecord(req, res)
}

func (self *CityBrokenMgr) GetCityBrokenFightRecordById(req *RPC_CityBrokenRecordReq, res *RPC_CityBrokenRecordRes) {
	if self.CityBrokenInfo == nil {
		return
	}
	self.CityBrokenInfo.GetCityBrokenFightRecordById(req, res)
}

func (self *CityBrokenMgr) GetCityBrokenAll(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	if self.CityBrokenInfo == nil {
		return
	}
	self.CityBrokenInfo.GetCityBrokenAll(req, res)
}

func (self *CityBrokenMgr) GetCityBrokenFightInfo(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	if self.CityBrokenInfo == nil {
		return
	}
	self.CityBrokenInfo.GetCityBrokenFightInfo(req, res)
}

func (self *CityBrokenMgr) AttackCityBrokenStart(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	if self.CityBrokenInfo == nil {
		return
	}
	self.CityBrokenInfo.AttackCityBrokenStart(req, res)
}

func (self *CityBrokenMgr) AttackCityBrokenEnd(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	if self.CityBrokenInfo == nil {
		return
	}
	self.CityBrokenInfo.AttackCityBrokenEnd(req, res)
}

func (self *CityBrokenMgr) SetCityBrokenShield(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	if self.CityBrokenInfo == nil {
		return
	}
	self.CityBrokenInfo.SetCityBrokenShield(req, res)
}

func (self *CityBrokenMgr) GetOwnerPoint(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	if self.CityBrokenInfo == nil {
		return
	}
	self.CityBrokenInfo.GetOwnerPoint(req, res)
}

func (self *CityBrokenMgr) UpdateTeam(req *RPC_CityBrokenTeamUpdateReq, res *RPC_CityBrokenTeamUpdateRes) {
	if self.CityBrokenInfo == nil {
		return
	}
	self.CityBrokenInfo.UpdateTeam(req, res)
}

//func (self *CityBrokenMgr) UpdateCityBrokenPoint(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
//	if self.CityBrokenInfo == nil {
//		return
//	}
//	self.CityBrokenInfo.UpdateCityBrokenPoint(req, res)
//}

func (self *CityBrokenInfo) GetCityBrokenAll(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	self.lock.Lock()
	defer self.lock.Unlock()
	groupid := self.GetGroupId(req.ServerId, req.KeyId)
	_, groupok := self.CityBrokenCity[groupid]
	if !groupok {
		self.CityBrokenCity[groupid] = make(map[int64]*CityBrokenCity)
	}
	for _, v := range self.CityBrokenCity[groupid] {
		if v.fightInfo == nil {
			v.fightInfo = NewFightInfo(v.CityBrokenId)
		}
		if v.fightInfo.Uid == 0 {
			continue
		}
		if req.FightInfo != nil && v.fightInfo.Uid == req.FightInfo.Uid {
			req.FightInfo.Rankid = v.fightInfo.Rankid
			req.FightInfo.Param = v.fightInfo.Param
			v.fightInfo = req.FightInfo
			if v.fightInfo != nil {
				self.SetCityBrokenServerList(v.fightInfo.Server, groupid)
			}
		}
		res.FightInfoList = append(res.FightInfoList, v.fightInfo)
		res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(v))
	}
	_, serverok := self.CityBrokenServer[groupid]
	if !serverok {
		self.CityBrokenServer[groupid] = make(map[int]*CityBrokenServer)
	}
	res.CityBrokenServer = self.CityBrokenServer[groupid]
}

func (self *CityBrokenInfo) GetCityBrokenFightInfo(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	self.lock.RLock()
	defer self.lock.RUnlock()
	groupid := self.GetGroupId(req.ServerId, req.KeyId)
	_, groupok := self.CityBrokenCity[groupid]
	if !groupok {
		self.CityBrokenCity[groupid] = make(map[int64]*CityBrokenCity)
	}
	self.SetCityBrokenServerList(req.ServerId, groupid)
	for _, v := range req.CityBrokenIdList {
		info, ok := self.CityBrokenCity[groupid][v]
		if !ok {
			info = NewCityBrokenUser(v, groupid)
			info.GroupId = groupid
			self.CityBrokenCity[groupid][info.CityBrokenId] = info
		}
		if info.fightInfo == nil {
			info.fightInfo = NewFightInfo(info.CityBrokenId)
		}
		//if req.FightInfo != nil && info.fightInfo.Uid == req.FightInfo.Uid {
		//	req.FightInfo.Rankid = info.fightInfo.Rankid
		//	req.FightInfo.Param = info.fightInfo.Param
		//	info.fightInfo = req.FightInfo
		//	if info.fightInfo != nil {
		//		self.SetCityBrokenServerList(info.fightInfo.Server, groupid)
		//	}
		//}
		res.FightInfoList = append(res.FightInfoList, info.fightInfo)
		res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(info))
	}
}

func (self *CityBrokenInfo) AttackCityBrokenEnd(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	if req.FightInfo == nil {
		res.RetCode = model.LOGIC_TRUE
		return
	}
	self.lock.Lock()
	defer self.lock.Unlock()
	groupid := self.GetGroupId(req.ServerId, req.KeyId)
	_, groupok := self.CityBrokenCity[groupid]
	if !groupok {
		self.CityBrokenCity[groupid] = make(map[int64]*CityBrokenCity)
	}
	info, ok := self.CityBrokenCity[groupid][req.CityBrokenId]
	if !ok {
		info = NewCityBrokenUser(req.CityBrokenId, groupid)
		self.CityBrokenCity[groupid][info.CityBrokenId] = info
	}
	// 进攻者不是我 报错
	if info.AttackUid != req.FightInfo.Uid {
		res.RetCode = model.LOGIC_TRUE
		res.FightInfoList = append(res.FightInfoList, info.fightInfo)
		res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(info))
		return
	}
	// 被打的人变了 我打的是别人
	if info.fightInfo != nil && info.fightInfo.Uid != req.TargetUid {
		res.RetCode = model.LOGIC_TRUE
		res.FightInfoList = append(res.FightInfoList, info.fightInfo)
		res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(info))
		return
	}
	// 检测积分
	cityConfig := GetCityBrokenMgr().GetCityBrokenLevelConfig(req.CityBrokenId)
	if cityConfig == nil {
		res.RetCode = model.LOGIC_TRUE
		return
	}
	rankid := RANK_TYPE_CITY_BROKEN_POINT2
	if cityConfig.ActivityStage == CITY_BROKEN_STATUS_SIMULATE {
		rankid = RANK_TYPE_CITY_BROKEN_POINT1
	}

	myPoint := int64(0)
	rankInfo := GetRankMgr().GetMyRank(rankid, int(groupid), req.TargetUid)
	if rankInfo != nil {
		myPoint = rankInfo.Num
	}

	if myPoint < cityConfig.RestrictionPoint {
		res.RetCode = model.LOGIC_TRUE
		return
	}

	self.AddCityBrokenFightRecord(req.FightId, req.Rand, req.FightInfo, info.fightInfo, req.Result)

	if req.Result == 0 {
		// 这个队伍已经占领了其他城市
		cityId := self.GetCityBrokenPlayerCity(groupid, req.FightInfo.Uid, req.FightInfo.Param)
		if cityId != 0 {
			_, ok2 := self.CityBrokenCity[groupid][cityId]
			if ok2 {
				// 删除上一个城市
				self.CityBrokenCity[groupid][cityId].fightInfo = NewFightInfo(cityId)
				self.CityBrokenCity[groupid][cityId].StartTime = 0
				self.CityBrokenCity[groupid][cityId].AttackTime = 0
				self.CityBrokenCity[groupid][cityId].AttackUid = 0
				self.CityBrokenCity[groupid][cityId].ShieldTime = 0
				res.FightInfoList = append(res.FightInfoList, self.CityBrokenCity[groupid][cityId].fightInfo)
				res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(self.CityBrokenCity[groupid][cityId]))
				self.DeleteCityBrokenPlayer(groupid, req.FightInfo.Uid, req.FightInfo.Param)
			}
		}
		if info.fightInfo.Uid > 0 {
			// 删除前主人的纪录
			self.DeleteCityBrokenPlayer(groupid, info.fightInfo.Uid, info.fightInfo.Param)
			core.GetCenterApp().AddEvent(info.fightInfo.Server, core.CITYBROKEN_BE_ATTACKEN, info.fightInfo.Uid,
				info.CityBrokenId, int(info.fightInfo.Param), "")
		}
		info.StartTime = model.TimeServer().Unix()
		info.AttackTime = 0
		info.AttackUid = 0
		info.ShieldTime = 0
		info.fightInfo = req.FightInfo
		info.fightInfo.Rankid = int(info.CityBrokenId)
		if info.fightInfo != nil {
			self.SetCityBrokenServerList(info.fightInfo.Server, groupid)
			self.AddCityBrokenPlayer(info)
		}
	} else {
		// 这个队伍已经占领了其他城市
		cityId := self.GetCityBrokenPlayerCity(groupid, req.FightInfo.Uid, req.FightInfo.Param)
		if cityId != 0 {
			_, ok2 := self.CityBrokenCity[groupid][cityId]
			if ok2 {
				// 删除上一个城市
				self.CityBrokenCity[groupid][cityId].ShieldTime = 0
				res.FightInfoList = append(res.FightInfoList, self.CityBrokenCity[groupid][cityId].fightInfo)
				res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(self.CityBrokenCity[groupid][cityId]))
			}
		}
	}

	res.FightInfoList = append(res.FightInfoList, info.fightInfo)
	res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(info))
	self.GMCheckAllCity()
}

func (self *CityBrokenInfo) AttackCityBrokenStart(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	self.lock.Lock()
	defer self.lock.Unlock()
	groupid := self.GetGroupId(req.ServerId, req.KeyId)
	_, groupok := self.CityBrokenCity[groupid]
	if !groupok {
		self.CityBrokenCity[groupid] = make(map[int64]*CityBrokenCity)
	}
	info, ok := self.CityBrokenCity[groupid][req.CityBrokenId]
	if !ok {
		info = NewCityBrokenUser(req.CityBrokenId, groupid)
		self.CityBrokenCity[groupid][info.CityBrokenId] = info
		self.CityBrokenCity[groupid][info.CityBrokenId] = info
	}
	// 检测积分
	cityConfig := GetCityBrokenMgr().GetCityBrokenLevelConfig(req.CityBrokenId)
	if cityConfig == nil {
		res.RetCode = model.LOGIC_TRUE
		return
	}
	rankid := RANK_TYPE_CITY_BROKEN_POINT2
	if cityConfig.ActivityStage == CITY_BROKEN_STATUS_SIMULATE {
		rankid = RANK_TYPE_CITY_BROKEN_POINT1
	}
	myPoint := int64(0)
	rankInfo := GetRankMgr().GetMyRank(rankid, int(groupid), req.TargetUid)
	if rankInfo != nil {
		myPoint = rankInfo.Num
	}

	if myPoint < cityConfig.RestrictionPoint {
		// 积分不足
		res.RetCode = 2
		return
	}

	timeNow := model.TimeServer().Unix()
	if info.fightInfo.Uid == 0 {
		// 正在被人进攻的锁和cd时间
		//if info.AttackUid != 0 && timeNow < info.AttackTime+CITY_BROKEN_ATTACK_CD {
		//	res.RetCode = core.LOGIC_TRUE
		//	return
		//}
		info.AttackUid = req.TargetUid
		info.AttackTime = model.TimeServer().Unix()

		res.FightInfoList = append(res.FightInfoList, info.fightInfo)
		res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(info))
	} else {
		// 不能打自己
		if info.fightInfo.Uid == req.TargetUid {
			res.RetCode = 3
			return
		}
		//// 正在被人进攻的锁和cd时间
		//if info.AttackUid != 0 && timeNow < info.AttackTime+CITY_BROKEN_ATTACK_CD {
		//	res.RetCode = core.LOGIC_TRUE
		//	return
		//}

		// 别人刚占领不足一分钟
		if timeNow < info.StartTime+utils.MIN_SECS {
			res.RetCode = 4
			return
		}

		// 使用护盾护盾的时长
		if info.ShieldTime != 0 && timeNow < info.ShieldTime+10*utils.MIN_SECS {
			res.RetCode = 5
			return
		}

		info.AttackUid = req.TargetUid
		info.AttackTime = model.TimeServer().Unix()

		res.FightInfoList = append(res.FightInfoList, info.fightInfo)
		res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(info))
	}
}

func (self *CityBrokenInfo) SetCityBrokenShield(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	self.lock.Lock()
	defer self.lock.Unlock()
	groupid := self.GetGroupId(req.ServerId, req.KeyId)
	_, groupok := self.CityBrokenCity[groupid]
	if !groupok {
		self.CityBrokenCity[groupid] = make(map[int64]*CityBrokenCity)
	}
	info, ok := self.CityBrokenCity[groupid][req.CityBrokenId]
	if !ok {
		info = NewCityBrokenUser(req.CityBrokenId, groupid)
		self.CityBrokenCity[groupid][info.CityBrokenId] = info
	}
	// 不是自己
	if info.fightInfo == nil || info.fightInfo.Uid != req.TargetUid {
		res.RetCode = 6
		res.FightInfoList = append(res.FightInfoList, info.fightInfo)
		res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(info))
		return
	}
	timeNow := model.TimeServer().Unix()
	// 使用护盾护盾的时长
	if info.ShieldTime != 0 && timeNow < info.ShieldTime+10*utils.MIN_SECS {
		res.RetCode = 7
		return
	}
	info.ShieldTime = model.TimeServer().Unix()
	res.FightInfoList = append(res.FightInfoList, info.fightInfo)
	res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(info))
}

func (self *CityBrokenInfo) GetOwnerPoint(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	groupid := self.GetGroupId(req.ServerId, req.KeyId)
	rankid := RANK_TYPE_CITY_BROKEN_POINT2
	if req.Result == CITY_BROKEN_STATUS_SIMULATE {
		rankid = RANK_TYPE_CITY_BROKEN_POINT1
	}
	myPoint := int64(0)
	rankInfo := GetRankMgr().GetMyRank(rankid, int(groupid), req.TargetUid)
	if rankInfo != nil {
		myPoint = rankInfo.Num
	}
	res.CityBrokenId = myPoint
}

func (self *CityBrokenInfo) UpdateTeam(req *RPC_CityBrokenTeamUpdateReq, res *RPC_CityBrokenTeamUpdateRes) {
	self.lock.Lock()
	defer self.lock.Unlock()

	groupid := self.GetGroupId(req.ServerId, req.KeyId)
	_, groupok := self.CityBrokenCity[groupid]
	if !groupok {
		self.CityBrokenCity[groupid] = make(map[int64]*CityBrokenCity)
	}
	for _, fightinfo := range req.FightInfo {
		// 这个队伍已经占领了其他城市
		cityId := self.GetCityBrokenPlayerCity(groupid, req.TargetUid, fightinfo.Param)
		if cityId != 0 {
			_, ok2 := self.CityBrokenCity[groupid][cityId]
			if ok2 {
				self.CityBrokenCity[groupid][cityId].fightInfo = fightinfo
				self.CityBrokenCity[groupid][cityId].fightInfo.Rankid = int(cityId)
				res.FightInfoList = append(res.FightInfoList, self.CityBrokenCity[groupid][cityId].fightInfo)
				res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(self.CityBrokenCity[groupid][cityId]))
			}
		}
	}
}

//func (self *CityBrokenInfo) UpdateCityBrokenPoint(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
//	self.lock.Lock()
//	defer self.lock.Unlock()
//
//}

func (self *CityBrokenMgr) GetCityOwner(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	if self.CityBrokenInfo == nil {
		return
	}
	self.CityBrokenInfo.GetCityOwner(req, res)
}

func (self *CityBrokenInfo) GetCityOwner(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	self.lock.Lock()
	defer self.lock.Unlock()
	groupid := self.GetGroupId(req.ServerId, req.KeyId)
	_, groupok := self.CityBrokenCity[groupid]
	if !groupok {
		self.CityBrokenCity[groupid] = make(map[int64]*CityBrokenCity)
	}
	for _, v := range self.CityBrokenCity[groupid] {
		if v.fightInfo == nil {
			v.fightInfo = NewFightInfo(v.CityBrokenId)
		}
		if v.fightInfo.Uid != req.TargetUid {
			continue
		}
		//res.FightInfoList = append(res.FightInfoList, v.fightInfo)
		res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(v))
	}
}

func (self *CityBrokenMgr) AttackCityBrokenEndByPass(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	if self.CityBrokenInfo == nil {
		return
	}
	self.CityBrokenInfo.AttackCityBrokenEndByPass(req, res)
}

func (self *CityBrokenInfo) AttackCityBrokenEndByPass(req *RPC_CityBrokenReq, res *RPC_CityBrokenRes) {
	self.lock.Lock()
	defer self.lock.Unlock()
	groupid := self.GetGroupId(req.ServerId, req.KeyId)
	_, groupok := self.CityBrokenCity[groupid]
	if !groupok {
		self.CityBrokenCity[groupid] = make(map[int64]*CityBrokenCity)
	}
	info, ok := self.CityBrokenCity[groupid][req.CityBrokenId]
	if !ok {
		info = NewCityBrokenUser(req.CityBrokenId, groupid)
		self.CityBrokenCity[groupid][info.CityBrokenId] = info
	}
	// 不是空城市
	if info.fightInfo != nil && info.fightInfo.Uid != 0 {
		res.RetCode = model.LOGIC_TRUE
		return
	}
	// 检测积分
	cityConfig := GetCityBrokenMgr().GetCityBrokenLevelConfig(req.CityBrokenId)
	if cityConfig == nil {
		res.RetCode = model.LOGIC_TRUE
		return
	}
	rankid := RANK_TYPE_CITY_BROKEN_POINT2
	if cityConfig.ActivityStage == CITY_BROKEN_STATUS_SIMULATE {
		rankid = RANK_TYPE_CITY_BROKEN_POINT1
	}
	myPoint := int64(0)
	rankInfo := GetRankMgr().GetMyRank(rankid, int(groupid), req.TargetUid)
	if rankInfo != nil {
		myPoint = rankInfo.Num
	}

	if myPoint < cityConfig.RestrictionPoint {
		res.RetCode = 2
		return
	}
	//看是不是其他地方的城主
	cityId := self.GetCityBrokenPlayerCity(groupid, req.FightInfo.Uid, req.FightInfo.Param)
	if cityId != 0 {
		_, ok2 := self.CityBrokenCity[groupid][cityId]
		if ok2 {
			// 删除上一个城市
			self.CityBrokenCity[groupid][cityId].fightInfo = NewFightInfo(cityId)
			self.CityBrokenCity[groupid][cityId].StartTime = 0
			self.CityBrokenCity[groupid][cityId].AttackTime = 0
			self.CityBrokenCity[groupid][cityId].AttackUid = 0
			self.CityBrokenCity[groupid][cityId].ShieldTime = 0
			res.FightInfoList = append(res.FightInfoList, self.CityBrokenCity[groupid][cityId].fightInfo)
			res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(self.CityBrokenCity[groupid][cityId]))
			self.DeleteCityBrokenPlayer(groupid, req.FightInfo.Uid, req.FightInfo.Param)
		}
	}
	info.StartTime = model.TimeServer().Unix()
	info.AttackTime = 0
	info.AttackUid = 0
	info.ShieldTime = 0
	info.fightInfo = req.FightInfo
	info.fightInfo.Rankid = int(info.CityBrokenId)
	if info.fightInfo != nil {
		self.AddCityBrokenPlayer(info)
		self.SetCityBrokenServerList(info.fightInfo.Server, groupid)
	}
	res.FightInfoList = append(res.FightInfoList, info.fightInfo)
	res.CityInfoList = append(res.CityInfoList, self.JS_CityInfoChange(info))
	self.GMCheckAllCity()
}

func (self *CityBrokenMgr) Save() {
	if self.CityBrokenInfo != nil {
		self.CityBrokenInfo.Save()
	}
}

func (self *CityBrokenInfo) Save() {
	self.lock.RLock()
	defer self.lock.RUnlock()
	for _, group := range self.CityBrokenCity {
		for _, v := range group {
			v.Encode()
			v.UpdateEx("groupid", v.GroupId)
		}
	}
	self.CityBrokenConfig.Update(true, false)
}

func (self *CityBrokenInfo) AddCityBrokenFightRecord(fightId int64, rand int64, fightInfo, targetFightInfo *model.JS_FightInfo, result int) {
	if fightInfo == nil || targetFightInfo == nil || fightInfo.Uid <= 0 || targetFightInfo.Uid <= 0 {
		return
	}
	//fightId, err := db.GetRedisMgr().Incr(CityBrokenRedisFightId)
	//if err != nil {
	//	return
	//}
	record := new(model.CityBrokenRecordInfo)
	record.FightId = fightId
	record.Info = make([]*model.JS_FightInfo, 2)
	record.Info[0] = fightInfo
	record.Info[1] = targetFightInfo
	record.Result = result
	record.Rand = rand
	record.Time = time.Now().Unix()

	db.GetRedisMgr().Set(fmt.Sprintf("%s_%d", CityBrokenRedisFightRecord, fightId), utils.HF_JtoA(record))
	db.GetRedisMgr().Expire(fmt.Sprintf("%s_%d", CityBrokenRedisFightRecord, fightId), utils.DAY_SECS*3)

	db.GetRedisMgr().LPush(fmt.Sprintf("%s_%d", CityBrokenRedisRecord, fightInfo.Uid), fmt.Sprintf("%d", fightId))
	db.GetRedisMgr().LTrim(fmt.Sprintf("%s_%d", CityBrokenRedisRecord, fightInfo.Uid), 0, CityBrokenRedisRecordNum-1)

	db.GetRedisMgr().LPush(fmt.Sprintf("%s_%d", CityBrokenRedisRecord, targetFightInfo.Uid), fmt.Sprintf("%d", fightId))
	db.GetRedisMgr().LTrim(fmt.Sprintf("%s_%d", CityBrokenRedisRecord, targetFightInfo.Uid), 0, CityBrokenRedisRecordNum-1)
}

func (self *CityBrokenInfo) GetRecordSimpleInfo(recordInfo *model.CityBrokenRecordInfo, uid int64) *model.CityBrokenSimpleRecord {
	simpleInfo := new(model.CityBrokenSimpleRecord)
	simpleInfo.FightId = recordInfo.FightId
	simpleInfo.Result = recordInfo.Result
	simpleInfo.FightTime = recordInfo.Time
	if uid == recordInfo.Info[0].Uid {
		simpleInfo.IsAttack = model.LOGIC_TRUE
		simpleInfo.EnemyName = recordInfo.Info[1].Uname
		simpleInfo.EnemyLevel = recordInfo.Info[1].Level
		simpleInfo.EnemyIcon = recordInfo.Info[1].Iconid
		simpleInfo.EnemyPortrait = recordInfo.Info[1].Portrait
		simpleInfo.EnemyUnionName = recordInfo.Info[1].UnionName
		simpleInfo.EnemyFight = recordInfo.Info[1].Deffight
		simpleInfo.EnemyVip = recordInfo.Info[1].Vip
	} else if uid == recordInfo.Info[1].Uid {
		simpleInfo.IsAttack = model.LOGIC_FALSE
		simpleInfo.EnemyName = recordInfo.Info[0].Uname
		simpleInfo.EnemyLevel = recordInfo.Info[0].Level
		simpleInfo.EnemyIcon = recordInfo.Info[0].Iconid
		simpleInfo.EnemyPortrait = recordInfo.Info[0].Portrait
		simpleInfo.EnemyUnionName = recordInfo.Info[0].UnionName
		simpleInfo.EnemyFight = recordInfo.Info[0].Deffight
		simpleInfo.EnemyVip = recordInfo.Info[0].Vip
	}

	return simpleInfo
}

func (self *CityBrokenInfo) GetCityBrokenFightRecord(req *RPC_CityBrokenRecordReq, res *RPC_CityBrokenRecordRes) {
	fightIds, err := db.GetRedisMgr().LRange(fmt.Sprintf("%s_%d", CityBrokenRedisRecord, req.Uid), 0, -1)
	if err != nil {
		utils.LogDebug("citybroken get record list err", err)
		return
	}

	res.RetCode = RETCODE_OK
	res.SimpleRecordList = make([]*model.CityBrokenSimpleRecord, 0)
	for i := 0; i < len(fightIds); i++ {
		fightId, err := strconv.ParseInt(fightIds[i], 10, 64)
		if err != nil {
			continue
		}
		val, _, err := db.GetRedisMgr().Get(fmt.Sprintf("%s_%d", CityBrokenRedisFightRecord, fightId))
		if err != nil {
			res.RetCode = RETCODE_DATA_ERROR
			return
		}
		var recordInfo model.CityBrokenRecordInfo
		err = json.Unmarshal([]byte(val), &recordInfo)
		if err != nil {
			res.RetCode = RETCODE_DATA_ERROR
			return
		}
		simpleInfo := self.GetRecordSimpleInfo(&recordInfo, req.Uid)
		res.SimpleRecordList = append(res.SimpleRecordList, simpleInfo)
	}
}

func (self *CityBrokenInfo) GetCityBrokenFightRecordById(req *RPC_CityBrokenRecordReq, res *RPC_CityBrokenRecordRes) {

	val, _, err := db.GetRedisMgr().Get(fmt.Sprintf("%s_%d", CityBrokenRedisFightRecord, req.Uid))
	if err != nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	var recordInfo model.CityBrokenRecordInfo
	err = json.Unmarshal([]byte(val), &recordInfo)
	if err != nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	res.RetCode = RETCODE_OK
	res.Record = &recordInfo
}

func (self *CityBrokenInfo) SetCityBrokenServerList(serverid int, groupid int) {
	server := server.GetServerMgr().GetServer(serverid, false)
	if server != nil {
		_, serverok := self.CityBrokenServer[groupid]
		if !serverok {
			self.CityBrokenServer[groupid] = make(map[int]*CityBrokenServer)
		}
		_, ok := self.CityBrokenServer[groupid][server.Data.ServerId]
		if !ok {
			self.CityBrokenServer[groupid][server.Data.ServerId] = &CityBrokenServer{server.Data.ServerId, server.Data.Name}
		}
		if self.CityBrokenServer[groupid][server.Data.ServerId].ServerName != server.Data.Name {
			self.CityBrokenServer[groupid][server.Data.ServerId].ServerName = server.Data.Name
		}
		if self.CityBrokenServerGroup == nil {
			self.CityBrokenServerGroup = make(map[int]int)
		}
		_, ok2 := self.CityBrokenServerGroup[serverid]
		if !ok2 {
			self.CityBrokenServerGroup[serverid] = groupid
		} else {
			if self.CityBrokenServerGroup[serverid] != groupid {
				self.CityBrokenServerGroup[serverid] = groupid
			}
		}
	}
}

func (self *CityBrokenInfo) LoadSetCityBrokenServerList(serverid int, groupid int) {
	if self.CityBrokenServerGroup == nil {
		self.CityBrokenServerGroup = make(map[int]int)
	}
	_, ok2 := self.CityBrokenServerGroup[serverid]
	if !ok2 {
		self.CityBrokenServerGroup[serverid] = groupid
	} else {
		if self.CityBrokenServerGroup[serverid] != groupid {
			self.CityBrokenServerGroup[serverid] = groupid
		}
	}
}

func (self *CityBrokenInfo) AddCityBrokenPlayer(data *CityBrokenCity) {
	// 分组不存在则创造
	_, ok1 := self.CityBrokenPlayer[data.GroupId]
	if !ok1 {
		self.CityBrokenPlayer[data.GroupId] = make(map[int64]*CityBrokenPlayer)
	}
	// 检测玩家数据
	_, ok2 := self.CityBrokenPlayer[data.GroupId][data.fightInfo.Uid]
	if !ok2 {
		playerInfo := new(CityBrokenPlayer)
		playerInfo.Uid = data.fightInfo.Uid
		playerInfo.ServerId = data.fightInfo.Server
		playerInfo.TeamCity = make(map[int64]int64)
		playerInfo.TeamCity[data.fightInfo.Param] = data.CityBrokenId
		self.CityBrokenPlayer[data.GroupId][data.fightInfo.Uid] = playerInfo
	} else {
		_, ok4 := self.CityBrokenPlayer[data.GroupId][data.fightInfo.Uid].TeamCity[data.fightInfo.Param]
		if !ok4 {
			self.CityBrokenPlayer[data.GroupId][data.fightInfo.Uid].TeamCity[data.fightInfo.Param] = data.CityBrokenId
			self.CityBrokenPlayer[data.GroupId][data.fightInfo.Uid].ServerId = data.fightInfo.Server
		} else {
			cityId := self.CityBrokenPlayer[data.GroupId][data.fightInfo.Uid].TeamCity[data.fightInfo.Param]
			if cityId != 0 && cityId != data.CityBrokenId {
				_, ok2 := self.CityBrokenCity[data.GroupId][cityId]
				if ok2 {
					// 删除上一个城市
					self.CityBrokenCity[data.GroupId][cityId].fightInfo = NewFightInfo(cityId)
					self.CityBrokenCity[data.GroupId][cityId].StartTime = 0
					self.CityBrokenCity[data.GroupId][cityId].AttackTime = 0
					self.CityBrokenCity[data.GroupId][cityId].AttackUid = 0
					self.CityBrokenCity[data.GroupId][cityId].ShieldTime = 0
				}
			}

			self.CityBrokenPlayer[data.GroupId][data.fightInfo.Uid].TeamCity[data.fightInfo.Param] = data.CityBrokenId
			self.CityBrokenPlayer[data.GroupId][data.fightInfo.Uid].ServerId = data.fightInfo.Server
			//data.fightInfo = NewFightInfo(data.CityBrokenId)
			//data.Decode()
		}
	}
}

func (self *CityBrokenInfo) DeleteCityBrokenPlayer(groupId int, uid int64, teamType int64) bool {
	// 分组不存在则创造
	_, ok1 := self.CityBrokenPlayer[groupId]
	if !ok1 {
		return false
	}
	// 检测玩家数据
	_, ok2 := self.CityBrokenPlayer[groupId][uid]
	if !ok2 {
		return false
	}

	_, ok3 := self.CityBrokenPlayer[groupId][uid].TeamCity[teamType]
	if !ok3 {
		return false
	}

	delete(self.CityBrokenPlayer[groupId][uid].TeamCity, teamType)
	return true
}

func (self *CityBrokenInfo) GetCityBrokenPlayerCity(groupId int, uid int64, teamType int64) int64 {
	// 分组不存在则创造
	_, ok1 := self.CityBrokenPlayer[groupId]
	if !ok1 {
		return 0
	}
	// 检测玩家数据
	_, ok2 := self.CityBrokenPlayer[groupId][uid]
	if !ok2 {
		return 0
	}

	_, ok3 := self.CityBrokenPlayer[groupId][uid].TeamCity[teamType]
	if !ok3 {
		return 0
	}

	return self.CityBrokenPlayer[groupId][uid].TeamCity[teamType]
}

func (self *CityBrokenInfo) GetGroupId(serverid int, keyid int) int {
	return keyid //GetServerGroupMgr().GetGroupId(serverid)
}

//func (self *CityBrokenInfo) UpdateCityBrokenInfo(serverid int, updateAll bool, cityid int64) {
//	groupid := self.GetGroupId(serverid)
//	_, serverok := self.CityBrokenServer[groupid]
//	if !serverok {
//		return
//	}
//	for _, server := range self.CityBrokenServer[groupid] {
//		core.GetCenterApp().AddEvent(server.ServerId, core.CITYBROKEN_INFO_UPDATE, 0,
//			0, 0, "")
//	}
//}

func (self *CityBrokenMgr) GetGroupIdSafe(serverId int) int {
	if self.CityBrokenInfo == nil {
		return 0
	}
	return self.CityBrokenInfo.GetRanGroupIdSafe(serverId)
}

func (self *CityBrokenInfo) GetRanGroupIdSafe(serverid int) int {
	_, ok := self.CityBrokenServerGroup[serverid]
	if !ok {
		return 0
	}
	return self.CityBrokenServerGroup[serverid]
}

func (self *CityBrokenMgr) GetGroupId(serverId int) int {
	if self.CityBrokenInfo == nil {
		return 0
	}
	return self.CityBrokenInfo.GetRanGroupId(serverId)
}

func (self *CityBrokenInfo) GetRanGroupId(serverid int) int {
	self.lock.Lock()
	defer self.lock.Unlock()
	_, ok := self.CityBrokenServerGroup[serverid]
	if !ok {
		return 0
	}
	return self.CityBrokenServerGroup[serverid]
}

func (self *CityBrokenInfo) GMCheckAllCity() {
	for group, value := range self.CityBrokenCity {
		_, ok := self.CityBrokenPlayer[group]
		if !ok {
			fmt.Println("FFFFFFFFFF")
			continue
		}
		for _, city := range value {
			if city.fightInfo.Uid != 0 {
				_, ok1 := self.CityBrokenPlayer[group][city.fightInfo.Uid]
				if !ok1 {
					fmt.Println("FFFFFFFFFF")
					continue
				}
				_, ok2 := self.CityBrokenPlayer[group][city.fightInfo.Uid].TeamCity[city.fightInfo.Param]
				if !ok2 {
					fmt.Println("FFFFFFFFFF")
					continue
				}
				if self.CityBrokenPlayer[group][city.fightInfo.Uid].TeamCity[city.fightInfo.Param] != city.CityBrokenId {
					fmt.Println("FFFFFFFFFF")
					continue
				}
			}
		}
	}

	for group, value := range self.CityBrokenPlayer {
		_, ok := self.CityBrokenCity[group]
		if !ok {
			fmt.Println("FFFFFFFFFF")
			continue
		}
		for _, player := range value {
			for teamid, cityid := range player.TeamCity {
				_, ok1 := self.CityBrokenCity[group][cityid]
				if !ok1 {
					fmt.Println("FFFFFFFFFF")
					continue
				}
				if self.CityBrokenCity[group][cityid].fightInfo.Uid != player.Uid {
					fmt.Println("FFFFFFFFFF")
					continue
				}
				if teamid != self.CityBrokenCity[group][cityid].fightInfo.Param {
					fmt.Println("FFFFFFFFFF")
					continue
				}
			}
		}
	}
}
