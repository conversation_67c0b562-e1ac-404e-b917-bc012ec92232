package utils

import (
	"log"
)

type Data struct {
	Key  int
	Data string
}

func TestSafeMap() {
	safe_map := NewSafeMapInt()

	safe_map.Store(1, 1)
	safe_map.Store(2, true)
	safe_map.Store(3, "a")

	json, ok := safe_map.Encode()
	if ok == nil {
		log.Println(string(json))
	}

	safe_map.Store(4, &Data{
		Key:  4,
		Data: "Key 4",
	})

	json, ok = safe_map.Encode()
	if ok == nil {
		log.Println(string(json))
	}
	//safe_map1 := NewSafeMapInt()

	//var data map[int]*Data
	//json2.Unmarshal(json, &data)


	data2 := NewSafeMapIntInt()
	data2.Decode(string(json))

	if ok == nil {
		log.Println(string(json))
	}

}
