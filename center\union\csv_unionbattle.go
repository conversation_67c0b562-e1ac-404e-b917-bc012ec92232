package union

import "master/utils"

var (
	GuildWarConfigMap    map[int]*GuildWarConfig
	ResultAwardConfigMap map[int]*ResultAwardConfig
)

func init() {
	GuildWarConfigMap = make(map[int]*GuildWarConfig)
	utils.GetCsvUtilMgr().LoadCsv("Guild_war", &GuildWarConfigMap)

	ResultAwardConfigMap = make(map[int]*ResultAwardConfig)
	utils.GetCsvUtilMgr().LoadCsv("Result_Award", &ResultAwardConfigMap)
}

func GetGuildWarConfigMap(index int) *GuildWarConfig {
	return GuildWarConfigMap[index]
}

func GetResultAwardConfigMap(system_group int, result_type int) *ResultAwardConfig {
	for _, v := range ResultAwardConfigMap {
		if v.SystemGroup == system_group && v.ResultType == result_type {
			return v
		}
	}
	return nil
}

type GuildWarConfig struct {
	Id                int   `json:"id"`
	Limit             []int `json:"limit"`
	Point             []int `json:"point"`
	PointDefeated     int   `json:"point_defeated"` //失败积分
	PointCrash        int   `json:"point_crash"`    //吊打积分
	DefeatedAttribute []int `json:"defeated_attribute"`
	DefeatedNum       []int `json:"defeated_num"`
	SupportAttribute  []int `json:"support_attribute"`
	SupportNum        []int `json:"support_num"`
}

type ResultAwardConfig struct {
	Id          int   `json:"id"`
	SystemGroup int   `json:"system_group"`
	ResultType  int   `json:"result_type"`
	ResultAward []int `json:"result_award"`
	AwardNum    []int `json:"award_num"`
	MailId      int   `json:"mail_id"`
}
