package crossserver

import (
	"fmt"
	json "github.com/bytedance/sonic"
	"master/db"
	"master/model"
	"master/utils"
)

// FightInfoCache ! 战斗信息结构
type FightInfoCache struct {
	NeedSave         bool              //! 是否需要保存
	LastLiveTime     int64             //! 上次更新时间
	FightInfoCacheDB *FightInfoCacheDB //! 数据保存结构
}

// FightInfoCacheDB ! 战斗信息-数据库结构
type FightInfoCacheDB struct {
	Uid       int64
	ActType   int    //! 活动类型
	FightInfo string //! 战斗数据

	fightInfo *model.JS_FightInfo //! 通用的战斗结构,每次变更阵容都会修改
	db.DataUpdate
}

func (self *FightInfoCacheDB) GetFightInfo() *model.JS_FightInfo {
	return self.fightInfo
}

func (self *FightInfoCacheDB) SetFightInfo(info *model.JS_FightInfo) {
	self.fightInfo = info
}

// ChampionInfo ! 跨服冠军赛结构信息
type ChampionInfo struct {
	Id            int
	Period        int    // 期数
	ActType       int    // 活动类型
	ZoneId        int    // 组
	Stage         int    // 当前在哪个阶段
	SignTime      int64  // 报名时间
	ScoreTime     int64  // 积分赛时间
	EliminateTime int64  // 淘汰赛时间
	EndTime       int64  // 结束时间
	NextTime      int64  // 预告时间
	ScoreInfo     string // DataScoreInfo
	EliminateInfo string // DataEliminateInfo
	BetInfo       string // MapBetInfo
	TopRank       string // SliceTopRank
	HistoryInfo   string // DataHistoryInfo
	NextSignTime  int64  //展示用 预告开始时间
	NextEndTime   int64  //展示用 预告结算时间

	DataScoreInfo     *JS_ChampionScoreInfo      // 积分赛信息   只用来和客户端消息用，逻辑部分分拆到外层单独存储
	DataEliminateInfo *JS_ChampionEliminateInfo  // 淘汰赛信息   只用来和客户端消息用，逻辑部分分拆到外层单独存储
	MapBetInfo        map[int64]*ChampionBetInfo // 记录冠军赛的下注情况
	SliceTopRank      []*ChampionTop             // 冠军赛top
	DataHistoryInfo   *ChampionHistoryInfo       // 冠军赛历史信息
	MapScoreTime      map[int]int
	MapScoreTimeAll   map[int]int
	MapEliminateTime  map[int]int
	MapEliminateAll   map[int]int
	db.DataUpdate
}

// ChampionBetInfo ! 冠军赛投注信息
type ChampionBetInfo struct {
	Uid       int64 `json:"uid"`
	TargetUid int64 `json:"targetuid"` // 下注目标
	TargetBet int   `json:"targetbet"` // 下注金额
	NowBet    int   `json:"nowbet"`    // 拥有的金额
	SendSign  int   `json:"sendsign"`  //
	ServerId  int   `json:"serverid"`  //
}

// ChampionTop ! 冠军赛排行榜信息
type ChampionTop struct {
	Uid int64 `json:"uid"`
	//BattleInfo []*BattleInfo `json:"battleinfo"`
	WinTimes  int `json:"wintimes"`
	Score     int `json:"score"`
	ArenaRank int `json:"arenarank"`
	RankPos   int `json:"rankpos"`
	ServerId  int `json:"serverid"` //
}

// ChampionScoreInfo ! 冠军赛积分信息
type ChampionScoreInfo struct {
	ScoreTurn      int                     `json:"scoreturn"`      //当前轮次
	ScoreUser      []*ScoreTurnUser        `json:"scoreuser"`      //参赛人
	UserFight      int64                   `json:"userfight"`      //参赛人平均战斗力
	ScoreMatchInfo map[int]*ScoreMatchInfo `json:"scorematchinfo"` //每一轮的信息
	TempBetResult  map[int]int
}

// JS_SimpleInfo ! 冠军赛简单信息
type JS_SimpleInfo struct {
	Rankid    int    `json:"rankid"`    // 类型
	Uid       int64  `json:"uid"`       // id
	Uname     string `json:"uname"`     // 名字
	UnionName string `json:"union"`     //! 军团名字
	UnionId   int    `json:"unionid"`   //! 军团id
	Iconid    int    `json:"iconid"`    // icon
	Camp      int    `json:"camp"`      // 阵营
	Level     int    `json:"level"`     // 等级
	Vip       int    `json:"vip"`       // Vip 等级
	Deffight  int64  `json:"deffight"`  // FightInfo总战力
	FightTeam int    `json:"fightteam"` // 出战兵营
	Portrait  int    `json:"portrait"`  // 边框  20190412 by zy
	Title     int    `json:"title"`
	Server    int    `json:"server"` //
	Score     int    `json:"score"`  //
	Param     int64  `json:"param"`  //
}

func (self *ChampionScoreInfo) GetTempBetResult() int {
	if self.TempBetResult == nil {
		self.TempBetResult = make(map[int]int)
	}
	_, ok := self.TempBetResult[self.ScoreTurn]
	if ok {
		return 1
	}
	return 0
}

func (self *ChampionScoreInfo) SetTempBetResult(result int) {
	if self.TempBetResult == nil {
		self.TempBetResult = make(map[int]int)
	}
	_, ok := self.TempBetResult[self.ScoreTurn]
	if !ok {
		self.TempBetResult[self.ScoreTurn] = result
	}
}

type JS_ChampionScoreInfo struct {
	ScoreTurn      int                        `json:"scoreturn"`      //当前轮次
	ScoreUser      []*ScoreTurnUser           `json:"scoreuser"`      //参赛人
	UserFight      int64                      `json:"userfight"`      //参赛人平均战斗力
	ScoreMatchInfo map[int]*JS_ScoreMatchInfo `json:"scorematchinfo"` //每一轮的信息
}

type ChampionHistoryInfo struct {
	Period    int                   `json:"period"`
	StartTime int64                 `json:"starttime"`
	EndTime   int64                 `json:"endtime"`
	TopRank   []*model.JS_FightInfo `json:"toprank"` // 冠军赛top
	AllRank   []*ChampionTop        `json:"allrank"` // 历史结果
}

func (self *ChampionHistoryInfo) Init() {
	self.TopRank = make([]*model.JS_FightInfo, 0)
	self.AllRank = make([]*ChampionTop, 0)
}

func (self *ChampionHistoryInfo) Check() {
	if self.TopRank == nil {
		self.TopRank = make([]*model.JS_FightInfo, 0)
	}

	if self.AllRank == nil {
		self.AllRank = make([]*ChampionTop, 0)
	}
}

type ScoreTurnUser struct {
	Uid           int64   `json:"uid"`
	ServerId      int     `json:"server_id"`
	KeyId         int     `json:"keyid"`
	BattleInfo    []int64 `json:"battleinfo"`
	WinTimes      int     `json:"wintimes"`
	Score         int     `json:"score"`
	ArenaRank     int     `json:"arenarank"`
	ScoreWinTimes int     `json:"s"` //积分赛胜场,辅助匹配
}

type ScoreMatchInfo struct {
	Id                int    `json:"id"`       //
	Period            int    `json:"period"`   //
	ActType           int    `json:"act_type"` // 活动类型
	ZoneId            int    `json:"zoneid"`   //
	ScoreTurnId       int    `json:"scoreturnid"`
	ScoreMatchStage   int    `json:"scorematchstage"`   //当前阶段 下注  战斗  战斗结束
	BetTeamId         int    `json:"betteamid"`         //场次id
	BetEnd            int64  `json:"betend"`            //下注结束时间
	BattleEnd         int64  `json:"battleend"`         //战斗结束时间
	UserFight         int64  `json:"userfight"`         //
	ScoreMatchTeamStr string `json:"scorematchteamstr"` //
	ScoreUserStr      string `json:"scoreuserstr"`      //

	ScoreUser      []*ScoreTurnUser //参赛人 数据回溯用
	ScoreMatchTeam []*ScoreMatchTeam

	db.DataUpdate
}

type JS_ScoreMatchInfo struct {
	Period          int               `json:"period"` //
	ScoreTurnId     int               `json:"scoreturnid"`
	ScoreMatchStage int               `json:"scorematchstage"` //当前阶段 下注  战斗  战斗结束
	BetTeamId       int               `json:"betteamid"`
	BetEnd          int64             `json:"betend"`    //下注结束时间
	BattleEnd       int64             `json:"battleend"` //战斗结束时间
	ScoreUser       []*ScoreTurnUser  `json:"scoreuser"` //参赛人 数据回溯用
	ScoreMatchTeam  []*ScoreMatchTeam `json:"scorematchteam"`
}

type ScoreMatchTeam struct {
	TeamId     int                 `json:"teamid"`
	AttackUid  int64               `json:"attackuid"`
	DefenceUid int64               `json:"defenceuid"`
	AttackBet  int                 `json:"attackbet"`  //押注攻击方 改为计算人数
	DefenceBet int                 `json:"defencebet"` //押注防守方 改为计算人数
	Random     int                 `json:"random"`
	BattleId   int64               `json:"battleid"`
	BattleInfo []*model.BattleInfo `json:"battleinfo"` //取消这里战报的存储，数据过大 20230308
}

type EliminateUser struct {
	Uid        int64   `json:"uid"`
	KeyId      int     `json:"keyid"`
	BattleInfo []int64 `json:"battleinfo"`
	WinTimes   int     `json:"wintimes"`
	Score      int     `json:"score"`
	ArenaRank  int     `json:"arenarank"`
}

type EliminateMatchInfo struct {
	Id                    int    `json:"id"`       //
	Period                int    `json:"period"`   //
	ZoneId                int    `json:"zoneid"`   //
	ActType               int    `json:"act_type"` //
	EliminateTurnId       int    `json:"eliminateturnid"`
	EliminateMatchStage   int    `json:"eliminatematchstage"` //当前阶段 下注  战斗  战斗结束
	BetTeamId             int    `json:"betteamid"`
	BetEnd                int64  `json:"betend"`                //下注结束时间
	BattleEnd             int64  `json:"battleend"`             //战斗结束时间
	EliminateMatchTeamStr string `json:"eliminatematchteamstr"` //
	EliminateUserStr      string `json:"eliminateuserstr"`      //

	EliminateUser      []*EliminateUser //参赛人 数据回溯用
	EliminateMatchTeam []*ScoreMatchTeam
	db.DataUpdate
}

type JS_EliminateMatchInfo struct {
	Period              int               `json:"period"` //
	EliminateTurnId     int               `json:"eliminateturnid"`
	EliminateMatchStage int               `json:"eliminatematchstage"` //当前阶段 下注  战斗  战斗结束
	BetTeamId           int               `json:"betteamid"`
	BetEnd              int64             `json:"betend"`        //下注结束时间
	BattleEnd           int64             `json:"battleend"`     //战斗结束时间
	EliminateUser       []*EliminateUser  `json:"eliminateuser"` //参赛人 数据回溯用
	EliminateMatchTeam  []*ScoreMatchTeam `json:"eliminatematchteam"`
}

type ChampionEliminateInfo struct {
	EliminateTurn      int                         `json:"eliminateturn"`      //当前轮次
	EliminateUser      []*EliminateUser            `json:"eliminateuser"`      //参赛人
	EliminateMatchInfo map[int]*EliminateMatchInfo `json:"eliminatematchinfo"` //每一轮的信息
	TempBetResult      map[int]int
}

func (self *ChampionEliminateInfo) GetTempBetResult() int {
	if self.TempBetResult == nil {
		self.TempBetResult = make(map[int]int)
	}
	_, ok := self.TempBetResult[self.EliminateTurn]
	if ok {
		return 1
	}
	return 0
}

func (self *ChampionEliminateInfo) SetTempBetResult(result int) {
	if self.TempBetResult == nil {
		self.TempBetResult = make(map[int]int)
	}
	_, ok := self.TempBetResult[self.EliminateTurn]
	if !ok {
		self.TempBetResult[self.EliminateTurn] = result
	}
}

type JS_ChampionEliminateInfo struct {
	EliminateTurn      int                            `json:"eliminateturn"`      //当前轮次
	EliminateUser      []*EliminateUser               `json:"eliminateuser"`      //参赛人
	EliminateMatchInfo map[int]*JS_EliminateMatchInfo `json:"eliminatematchinfo"` //每一轮的信息
}

func (self *JS_ChampionEliminateInfo) Init() {
	self.EliminateUser = make([]*EliminateUser, 0)
	self.EliminateMatchInfo = make(map[int]*JS_EliminateMatchInfo)
}

func (self *JS_ChampionEliminateInfo) Check() {
	if self.EliminateUser == nil {
		self.EliminateUser = make([]*EliminateUser, 0)
	}
	if self.EliminateMatchInfo == nil {
		self.EliminateMatchInfo = make(map[int]*JS_EliminateMatchInfo)
	}
}

type ChampionBetRecord struct {
	Uid       int64      `json:"uid"`
	BetRecord *BetRecord `json:"betrecord"`
}

type BetRecord struct {
	TeamInfo      *ScoreMatchTeam `json:"scorematchteam"`
	TargetUid     int64           `json:"targetuid"`
	TargetBet     int             `json:"targetvalue"`
	BetResult     int             `json:"betresult"`
	BetChange     int             `json:"betchange"`
	Stage         int             `json:"stage"`
	ScoreTurn     int             `json:"scoreturn"`     //积分赛轮次
	EliminateTurn int             `json:"eliminateturn"` //淘汰赛轮次
}

func (self *ChampionInfo) Decode() {
	json.Unmarshal([]byte(self.BetInfo), &self.MapBetInfo)
	json.Unmarshal([]byte(self.TopRank), &self.SliceTopRank)
	json.Unmarshal([]byte(self.HistoryInfo), &self.DataHistoryInfo)
}

func (self *ChampionInfo) Encode() {
	self.BetInfo = utils.HF_JtoA(&self.MapBetInfo)
	self.TopRank = utils.HF_JtoA(&self.SliceTopRank)
	self.HistoryInfo = utils.HF_JtoA(&self.DataHistoryInfo)
}

func (self *ScoreMatchInfo) Decode() {
	json.Unmarshal([]byte(self.ScoreMatchTeamStr), &self.ScoreMatchTeam)
	json.Unmarshal([]byte(self.ScoreUserStr), &self.ScoreUser)
}

func (self *EliminateMatchInfo) Decode() {
	json.Unmarshal([]byte(self.EliminateMatchTeamStr), &self.EliminateMatchTeam)
	json.Unmarshal([]byte(self.EliminateUserStr), &self.EliminateUser)
}

func NewScoreTurnUser(fightInfo *model.JS_FightInfo) *ScoreTurnUser {
	scoreTurnUser := new(ScoreTurnUser)
	scoreTurnUser.Uid = fightInfo.Uid
	scoreTurnUser.ServerId = fightInfo.Server
	scoreTurnUser.BattleInfo = make([]int64, 0)
	return scoreTurnUser
}

func NewEliminateUser(scoreTurnUser *ScoreTurnUser) *EliminateUser {
	eliminateUser := new(EliminateUser)
	eliminateUser.Uid = scoreTurnUser.Uid
	eliminateUser.ArenaRank = scoreTurnUser.ArenaRank
	eliminateUser.Score = scoreTurnUser.Score
	return eliminateUser
}

func (self *ChampionTop) IsRobot() bool {
	return self.Uid < ROBOT_ARENA_UID_END
}

func (self *ScoreMatchInfo) GetTeam(teamId int) *ScoreMatchTeam {
	for _, v := range self.ScoreMatchTeam {
		if v.TeamId == teamId {
			return v
		}
	}
	return nil
}

func (self *EliminateMatchInfo) GetTeam(teamId int) *ScoreMatchTeam {
	for _, v := range self.EliminateMatchTeam {
		if v.TeamId == teamId {
			return v
		}
	}
	return nil
}

func GetBattleInfoRedis(fightID int64) *model.BattleInfo {
	var battleInfo model.BattleInfo
	value, flag, err := db.HGetRedisEx(BATTLE_INFO_CHAMPION, fightID, fmt.Sprintf("%d", fightID))
	if err != nil || !flag {
		return nil
	}
	if flag {
		err := json.Unmarshal([]byte(value), &battleInfo)
		if err != nil {
			return &battleInfo
		}
	}
	if battleInfo.Id != 0 {
		return &battleInfo
	}
	return nil
}

func (self *FightInfoCacheDB) Encode() {
	self.FightInfo = utils.Lz4Encode(&self.fightInfo)
}

func (self *FightInfoCacheDB) Decode() {
	utils.Lz4Decode([]byte(self.FightInfo), &self.fightInfo)
	return
}

func (self *FightInfoCache) Save() {
	if self.NeedSave {
		self.FightInfoCacheDB.Encode()
		self.FightInfoCacheDB.Update(true, false)
		self.NeedSave = false
	}
}
