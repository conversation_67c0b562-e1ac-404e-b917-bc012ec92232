package union

type UserActivityRecord struct {
	Time     int64 `json:"time"`     // 时间
	AddCount int   `json:"addcount"` // 数量
}

// JS_UnionMember ! 公会成员信息
type JS_UnionMember struct {
	Uid                        int64  `json:"uid"`
	Level                      int    `json:"level"`
	Uname                      string `json:"uname"`
	Iconid                     int    `json:"iconid"`
	Portrait                   int    `json:"portrait"`
	Vip                        int    `json:"vip"`
	Position                   int    `json:"position"`
	Fight                      int64  `json:"fight"`
	Lastlogintime              int64  `json:"lastlogintime"`
	BraveHand                  int    `json:"bravehand"` // 无畏之手
	Stage                      int    `json:"stage"`     // 关卡进度
	ServerID                   int    `json:"serverid"`
	Title                      int    `json:"title"`
	WarShip                    int    `json:"warship"` //展示战船
	ActivityAttackTimes        int    //工会战已攻击次数
	ActivityAttackProtectTimes int    //工会战已使用保护次数
	ActivityStarWin            int    //工会战个人获取星数
	ActivityScore              int    //工会战个人积分

	ActivityRecord []*UserActivityRecord `json:"activityrecord"` // 记录
}

type JS_UnionApply struct {
	Uid       int64  `json:"uid"`
	Level     int    `json:"level"`
	Uname     string `json:"uname"`
	Iconid    int    `json:"iconid"`
	Portrait  int    `json:"portrait"`
	Vip       int    `json:"vip"`
	Fight     int64  `json:"fight"`
	Applytime int64  `json:"lastlogintime"`
	ServerID  int    `json:"serverid"`
}

type CommunityConfig struct {
	Level            int `json:"lv"`
	Exp              int `json:"exp"`
	Changeexp        int `json:"changeexp"`
	Membernum        int `json:"population"`
	Lively           int `json:"lively"`
	Changelively     int `json:"changelively"`
	Elder            int `json:"elder"`
	Fearless         int `json:"fearless"`
	Activelimit      int `json:"activelimit"`
	Warfare          int `json:"warfare"`
	GuildActiveLimit int `json:"guildactivelimit"`
	GuildExpLimit    int `json:"guildexplimit"`
}
