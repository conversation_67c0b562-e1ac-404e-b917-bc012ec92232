package match

import (
	"master/model"
)

const (
	RETCODE_OK         = 0 //! 没有错误
	RETCODE_DATA_ERROR = 1 //! 数据异常
)

const (
	RECORD_MAX = 50
)

// ! 消息主体
type RPC_Match struct {
}

// ! 事件请求
type RPC_GeneralActionReq struct {
	Uid           int64            //! 角色Id
	SelfInfo      *Js_GeneralUser  //!
	GeneralRecord []*GeneralRecord //! 公告
}

// ! 事件请求
type RPC_GeneralActionReqAll struct {
	KeyId    int //! 活动Key
	ServerId int //! 服务器Id
	OpenDay  int //! 开服时间
}

// ! 事件返回
type RPC_GeneralActionRes struct {
	RetCode       int               //! 返回结果
	RankInfo      []*Js_GeneralUser //! 排行信息
	SelfInfo      *Js_GeneralUser   //! 玩家信息
	GeneralRecord []*GeneralRecord  //! 公告
}

// ! 事件请求
type RPC_DimensionalActionReq struct {
	Uid               int64                //! 角色Id
	SelfInfo          *Js_DimensionalUser  //!
	DimensionalRecord []*DimensionalRecord //! 公告
}

// ! 事件请求
type RPC_DimensionalActionReqAll struct {
	KeyId    int //! 活动Key
	ServerId int //! 服务器Id
	OpenDay  int //! 开服时间
}

// ! 事件返回
type RPC_DimensionalActionRes struct {
	RetCode           int //! 返回结果
	ActType           int
	RankInfo          []*Js_DimensionalUser //! 排行信息
	SelfInfo          *Js_DimensionalUser   //! 玩家信息
	DimensionalRecord []*DimensionalRecord  //! 公告
}

// ! 事件请求
type RPC_CrossArenaActionReqAll struct {
	KeyId    int //! 各种KEY  int
	ServerId int //! 服务器Id
}

type RPC_CrossArenaAction64ReqAll struct {
	KeyId int64 //! 各种KEY  int64
}

// ! 事件返回
type RPC_CrossArenaActionReq struct {
	Uid       int64               //! 角色Id
	KeyId     int                 //!
	ServerId  int                 //! 服务器Id
	SelfInfo  *Js_CrossArenaUser  //!
	FightInfo *model.JS_FightInfo //!
}

type RPC_CrossArenaFightEndReq struct {
	Uid        int64               //! 角色Id
	KeyId      int                 //!
	ServerId   int                 //! 服务器Id
	Attack     *model.JS_FightInfo //!
	Defend     *model.JS_FightInfo //!
	BattleInfo model.BattleInfo
}

type RPC_CrossArenaActionRes struct {
	RetCode    int                                  //! 返回结果
	RankInfo   map[int]map[int][]*Js_CrossArenaUser //! 排行信息
	SelfInfo   *Js_CrossArenaUser                   //! 玩家信息
	Result     int                                  //!
	NewFightId int64                                //! 中心服对战报ID的新修正值
}

type RPC_CrossArenaGetDefenceRes struct {
	RetCode      int                   //! 返回结果
	Info         []*Js_CrossArenaUser  //! 排行信息
	FightInfo    []*model.JS_FightInfo //! 玩家信息
	TopInfo      []*Js_CrossArenaUser  //! 前十信息
	TopFightInfo []*model.JS_FightInfo //! 前十数据
}

// ! 事件返回
type RPC_CrossArenaGetInfoRes struct {
	RetCode      int                    //! 返回结果
	Info         *Js_CrossArenaUser     //! 信息
	FightInfo    *model.JS_FightInfo    //! 玩家信息
	LifeTreeInfo *model.JS_LifeTreeInfo //!
}

// ! 事件返回
type RPC_CrossArenaBattleInfoRes struct {
	RetCode    int //! 返回结果
	BattleInfo *model.BattleInfo
}

type RPC_CrossArenaBattleRecordRes struct {
	RetCode      int //! 返回结果
	BattleRecord *model.BattleRecord
}

// ConsumerTop
type RPC_ConsumerTopActionReqAll struct {
	KeyId    int //! 活动Key
	ServerId int //! 服务器Id
}

type RPC_ConsumerTopActionRes struct {
	RetCode    int                     //! 返回结果
	SelfInfo   *JS_ConsumerTopUser     //! 玩家信息
	TopUser    []*JS_ConsumerTopUser   //! 跨服个人排行数据
	TopSvr     []*JS_ConsumerTopServer //! 跨服服务器排行数据
	ServerUser []*JS_ConsumerTopUser   //! 跨服个人排行数据
}

type RPC_ConsumerTopActionReq struct {
	Uid      int64               //! 角色Id
	SelfInfo *JS_ConsumerTopUser //!
}

type RPC_DestroyMonsterActionReqAll struct {
	KeyId    int //! 活动Key
	ActType  int
	ServerId int //! 服务器Id
}

type RPC_DestroyMonsterActionRes struct {
	RetCode    int                        //! 返回结果
	SelfInfo   *JS_DestroyMonsterUser     //! 玩家信息
	TopUser    []*JS_DestroyMonsterUser   //! 跨服个人排行数据
	TopSvr     []*JS_DestroyMonsterServer //! 跨服服务器排行数据
	ServerUser []*JS_DestroyMonsterUser   //! 跨服个人排行数据
}

type RPC_DestroyMonsterActionReq struct {
	Uid      int64 //! 角色Id
	ActType  int
	SelfInfo *JS_DestroyMonsterUser //!
}

// 获取排行信息
func (self *RPC_Match) GetGeneralAllRankInfo(req *RPC_GeneralActionReqAll, res *RPC_GeneralActionRes) error {
	GetGeneralMgr().GetAllRank(req, res)
	return nil
}

// 上传玩家信息
func (self *RPC_Match) UpdateGeneralInfo(req *RPC_GeneralActionReq, res *RPC_GeneralActionRes) error {
	GetGeneralMgr().UpdatePoint(req, res)
	return nil
}

// 获取排行信息
func (self *RPC_Match) GetDimensionalAllRankInfo(req *RPC_DimensionalActionReqAll, res *RPC_DimensionalActionRes) error {
	GetDimensionalMgr().GetAllRank(req, res)
	return nil
}

// 上传玩家信息
func (self *RPC_Match) UpdateDimensionalInfo(req *RPC_DimensionalActionReq, res *RPC_DimensionalActionRes) error {
	GetDimensionalMgr().UpdatePoint(req, res)
	return nil
}

// 获取排行信息
func (self *RPC_Match) GetCrossArenaAllRankInfo(req *RPC_CrossArenaActionReqAll, res *RPC_CrossArenaActionRes) error {
	GetCrossArenaMgr().GetAllRank(req, res)
	return nil
}

func (self *RPC_Match) CrossArenaAdd(req *RPC_CrossArenaActionReq, res *RPC_CrossArenaActionRes) error {
	GetCrossArenaMgr().AddInfo(req, res)
	return nil
}

func (self *RPC_Match) CrossArenaGetDefence(req *RPC_CrossArenaActionReq, res *RPC_CrossArenaGetDefenceRes) error {
	GetCrossArenaMgr().GetDefence(req, res)
	return nil
}

func (self *RPC_Match) CrossArenaGetInfo(req *RPC_CrossArenaActionReq, res *RPC_CrossArenaGetInfoRes) error {
	GetCrossArenaMgr().GetPlayerInfo(req, res)
	return nil
}

func (self *RPC_Match) CrossArenaFightEnd(req *RPC_CrossArenaFightEndReq, res *RPC_CrossArenaActionRes) error {
	GetCrossArenaMgr().FightEnd(req, res)
	return nil
}

func (self *RPC_Match) CrossArenaGetBattleInfo(req *RPC_CrossArenaAction64ReqAll, res *RPC_CrossArenaBattleInfoRes) error {
	GetCrossArenaMgr().GetBattleInfo(req, res)
	return nil
}

func (self *RPC_Match) CrossArenaGetBattleRecord(req *RPC_CrossArenaAction64ReqAll, res *RPC_CrossArenaBattleRecordRes) error {
	GetCrossArenaMgr().GetBattleRecord(req, res)
	return nil
}

// 获取排行信息
func (self *RPC_Match) GetConsumerTopAllRankInfo(req *RPC_ConsumerTopActionReqAll, res *RPC_ConsumerTopActionRes) error {
	GetConsumerTopMgr().GetAllRank(req, res)
	return nil
}

// 上传玩家信息
func (self *RPC_Match) UpdateConsumerTopInfo(req *RPC_ConsumerTopActionReq, res *RPC_ConsumerTopActionRes) error {
	GetConsumerTopMgr().UpdatePoint(req, res)
	return nil
}

// 获取排行信息
func (self *RPC_Match) GetDestroyMonsterAllRankInfo(req *RPC_DestroyMonsterActionReqAll, res *RPC_DestroyMonsterActionRes) error {
	GetDestroyMonsterMgr().GetAllRank(req, res)
	return nil
}

// 上传玩家信息
func (self *RPC_Match) UpdateDestroyMonsterInfo(req *RPC_DestroyMonsterActionReq, res *RPC_DestroyMonsterActionRes) error {
	GetDestroyMonsterMgr().UpdatePoint(req, res)
	return nil
}
