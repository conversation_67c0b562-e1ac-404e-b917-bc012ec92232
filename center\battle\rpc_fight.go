package battle

import (
	"fmt"
	"log"
	"master/core"
	"master/model"
	"master/utils"
	"net/rpc"
	"time"
)

const (
	RPC_GETFIGHT = "RPC_Fight.GetFight" //! 游戏服战斗发送去转发服
	RPC_SETFIGHT = "RPC_Fight.SetFight" //! 转发服战斗结果投递游戏服
)

const FIGHT_CHAN_MAX = 64

type RPC_FightGetReq struct {
	ServerID  int
	FightNode *FightServerNode
}

type RPC_FightGetRes struct {
	RetCode int //! 结果码
}

type RPC_FightSetReq struct {
	ServerID int
}

type RPC_FightSetRes struct {
	RetCode   int //! 结果码
	FightNode *model.FightResultNode
	LeftCount int
}

func (self *FightMgr) OnTimer() {
	ticker := time.NewTicker(time.Second * 1)
	for {
		select {
		case <-ticker.C:
			self.CheckDelay()
			self.DelResultExceed()
			self.ResumeService(false)
		}
	}
	ticker.Stop()
}

func (self *FightMgr) GetFightNode() {
	ticker := time.NewTicker(time.Millisecond * 100)
	for {
		select {
		case <-ticker.C:
			var req RPC_FightSetReq
			req.ServerID = core.GetMasterApp().GetConfig().ServerId
			var res RPC_FightSetRes
			if self.Client != nil {
				err := self.CallEx(self.Client, RPC_SETFIGHT, req, &res)
				// 投递成功
				if err == nil {
					if res.RetCode == model.LOGIC_TRUE {
						GetFightMgr().SetResult(res.FightNode)
						if res.LeftCount > 0 {
							for i := 0; i < FIGHT_CHAN_MAX; i++ {
								var res2 RPC_FightSetRes
								err := self.CallEx(self.Client, RPC_SETFIGHT, req, &res2)
								// 投递成功
								if err == nil && res2.RetCode == model.LOGIC_TRUE {
									GetFightMgr().SetResult(res2.FightNode)
								} else {
									break
								}
								if res2.LeftCount <= 0 {
									break
								}
							}
						}
					}
				} else {
					self.CheckErr(err)
				}
			}
		}
	}
	ticker.Stop()
}

func (self *FightMgr) CheckErr(err error) {
	if self.Client == nil {
		self.ResumeService(true)
	}

	//! 断开连接返回
	if err == nil {
		return
	}

	if err.Error() == "connection is shut down" || err.Error() == "timeout" {
		//! 连接丢失,重新初始化
		self.ResumeService(true)
	}
}

func (self *FightMgr) ResumeService(force bool) bool {
	if self.Client == nil || force == true {
		//log.Println("FightMgr => ResumeService : ", force, self.Init)
		conn, err := rpc.DialHTTP("tcp", core.GetMasterApp().GetConfig().BattleSvr)
		if err != nil {
			self.Client = nil
		} else {
			//! 重新处理连接
			self.Client = conn
			log.Println("ResumeService Fight Ok !")
		}
		return true
	}

	return false
}

func (self *FightMgr) CheckDelay() {
	if len(self.WaitForGet) <= 0 {
		return
	}

	self.FightLock.Lock()
	defer self.FightLock.Unlock()

	var needDelete = false
	for _, v := range self.WaitForGet {
		//延迟时间未到则跳过
		if v.DelayTime != 0 && model.TimeServer().Unix() < v.DelayTime {
			continue
		}
		//! 如果战斗没有取出，就返回
		if v.GetNum == 0 || model.TimeServer().Unix()-v.GetTime > 50 {
			v.GetNum++
			v.GetTime = model.TimeServer().Unix()

			if v.GetNum > 30 {
				v.NeedDelete = true
				v.DeleteMap = true
				if !needDelete {
					needDelete = true
				}
				continue
			}

			if info, ok := self.MapAutoFight[v.FightId]; ok {
				// 尝试投递
				var req RPC_FightGetReq
				req.ServerID = core.GetMasterApp().GetConfig().ServerId
				req.FightNode = new(FightServerNode)
				req.FightNode.Id = info.Id
				req.FightNode.Att = info.Fight[0]
				req.FightNode.Def = info.Fight[1]
				req.FightNode.Random = info.Random
				var res RPC_FightGetRes
				err := self.CallEx(self.Client, RPC_GETFIGHT, req, &res)
				// 投递成功
				if err == nil && res.RetCode == model.LOGIC_TRUE {
					//! 投递成功删除
					if !needDelete {
						needDelete = true
					}
					v.NeedDelete = true
				} else if err != nil {
					self.CheckErr(err)
				}
			} else {
				//! 不存在则删除
				if !needDelete {
					needDelete = true
				}
				v.NeedDelete = true
			}
		}
	}

	if needDelete == true {
		if len(self.WaitForGet) == 1 {
			if self.WaitForGet[0].DeleteMap {
				delete(self.MapAutoFight, self.WaitForGet[0].FightId)
			}
			self.WaitForGet = make([]*WaitForGetInfo, 0)
		} else {
			for i := 0; i < len(self.WaitForGet); {
				//! 删除元素
				if self.WaitForGet[i].NeedDelete == true {
					if len(self.WaitForGet) == 1 {
						if self.WaitForGet[0].DeleteMap {
							delete(self.MapAutoFight, self.WaitForGet[0].FightId)
						}
						self.WaitForGet = make([]*WaitForGetInfo, 0)
					} else {
						if self.WaitForGet[i].DeleteMap {
							delete(self.MapAutoFight, self.WaitForGet[i].FightId)
						}
						self.WaitForGet[i] = self.WaitForGet[len(self.WaitForGet)-1]
						self.WaitForGet = self.WaitForGet[:len(self.WaitForGet)-1]
					}
				} else {
					i++
				}
			}
		}
	}
	return
}

func (self *FightMgr) DelResultExceed() {
	self.FightLock.Lock()

	nowTime := model.TimeServer().Unix()
	for key, value := range self.MapAutoFight {
		if int64(value.Time) < nowTime-5*utils.MIN_SECS {
			delete(self.MapAutoFight, key)
		}
	}
	self.FightLock.Unlock()
}

func (self *FightMgr) AddFightID(attack *model.JS_FightInfo, defence *model.JS_FightInfo, random int, id int64, delay int64, fightType int, levelID int) int64 {
	record := id
	if id == 0 {
		record = self.GetFightInfoID()
	}

	if self.Client == nil {
		return record
	}

	self.FightLock.Lock()
	defer self.FightLock.Unlock()
	utils.LogDebug("fightRecordId = ", record)

	client := new(model.FightResult)
	client.Id = record
	client.Fight[0] = attack
	client.Fight[1] = defence
	client.Random = random
	client.Time = model.TimeServer().Unix()
	client.FightType = fightType
	client.LevelID = levelID
	self.MapAutoFight[record] = client
	if delay > 0 {
		// 暂缓投递
		self.WaitForGet = append(self.WaitForGet, &WaitForGetInfo{FightId: record, DelayTime: model.TimeServer().Unix() + delay})
		utils.LogDebug("战报ID：", record, "--数据：", utils.HF_JtoA(client))
	} else {
		var req RPC_FightGetReq
		req.ServerID = core.GetMasterApp().GetConfig().ServerId
		req.FightNode = new(FightServerNode)
		req.FightNode.Id = record
		req.FightNode.Att = attack
		req.FightNode.Def = defence
		req.FightNode.Random = random
		var res RPC_FightGetRes
		err := self.CallEx(self.Client, RPC_GETFIGHT, req, &res)
		if err != nil || res.RetCode == model.LOGIC_FALSE {
			// 储存等待再次投递
			self.WaitForGet = append(self.WaitForGet, &WaitForGetInfo{FightId: record})
			if err != nil {
				self.CheckErr(err)
			}
		}
		utils.LogDebug("战报ID：", record, "--数据：", utils.HF_JtoA(req))
	}
	return record
}

func (self *FightMgr) CallExBlocking(client *rpc.Client, serviceMethod string, args interface{}, reply interface{}) error {
	done := make(chan error, 1)
	go func() {
		call := <-client.Go(serviceMethod, args, reply, make(chan *rpc.Call, 1)).Done
		done <- call.Error
	}()

	select {
	case err := <-done:
		if err != nil {
			if err.Error() == "connection is shut down" || err.Error() == "i/o timeout" {
				client.Close()
			}
			return err
		}
	}
	return nil
}

func (self *FightMgr) CallEx(client *rpc.Client, serviceMethod string, args interface{}, reply interface{}) error {
	timeout := time.Duration(3 * time.Second)
	done := make(chan error, 1)
	go func() {
		call := <-client.Go(serviceMethod, args, reply, make(chan *rpc.Call, 1)).Done
		done <- call.Error
	}()

	select {
	case <-time.After(timeout):
		//if self.WarnTimeFroTimeOut < TimeServer().Unix() {
		//	self.WarnTimeFroTimeOut = TimeServer().Unix() + MIN_SECS*10
		//warnStr := self.MakeWarnStr(WARN_MASTER_TIME_OUT)
		//GetWechatWarningMgr().SendWarning(warnStr)
		//}
		client.Close()
		return fmt.Errorf("timeout")
	case err := <-done:
		if err != nil {
			if err.Error() == "connection is shut down" || err.Error() == "i/o timeout" {
				client.Close()
			}
			return err
		}
	}
	return nil
}
