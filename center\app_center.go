package center

import (
	"master/center/battle"
	"master/center/crossserver"
	"master/center/match"
	"master/center/player"
	"master/center/server"
	"master/core"
	"master/model"
	"master/utils"
	"net/rpc"
	"time"
)

const (
	PER_SAVE_TIME      = 60 //! 60s保存一次
	INIT_WORLD_CHANNEL = 11 //! 默认初始化10个聊天频道

)

// ! 登录模块
type CenterApp struct {
	StartTime    int64 //! 服务器开始时间
	LastSave     int64 //! 上次保存时间
	LastLongSave int64 //! 上次保存时间

	EventArr chan *server.ServerEvent //! 事件推送
}

var s_centerapp *CenterApp

func GetCenterApp() *CenterApp {
	if s_centerapp == nil {
		s_centerapp = new(CenterApp)
		s_centerapp.StartTime = model.TimeServer().Unix()
		s_centerapp.LastSave = model.TimeServer().Unix()
		s_centerapp.LastLongSave = model.TimeServer().Unix()
		s_centerapp.EventArr = make(chan *server.ServerEvent, 10000)
		core.CenterApp = s_centerapp
	}

	return s_centerapp
}

// RegisterService ! 注册RPC服务,该消息是阻塞模式
func (self *CenterApp) RegisterService() {
	//! 采用HTTP作为调用载体
	rpc.HandleHTTP()

	//! 注册远程服务对象
	rpc.Register(new(crossserver.RPC_Chat))        //! 聊天
	rpc.Register(new(player.RPC_Player))           //! 角色
	rpc.Register(new(player.RPC_Friend))           //! 好友
	rpc.Register(new(player.RPC_Union))            //! 工会
	rpc.Register(new(player.RPC_Tower))            //! 爬塔
	rpc.Register(new(server.RPC_Server))           //! 服务器，事件
	rpc.Register(new(match.RPC_Match))             //! 跨服竞技
	rpc.Register(new(crossserver.RPC_CrossServer)) //! 新跨服
	rpc.Register(new(battle.RPC_Battle))           //! 战斗回放
}

func (self *CenterApp) StartService() {
	player.GetPlayerMgr().Init()

	//! 初始化所有的公会
	//union.GetUnionMgr().GetAllData()
	//tower.GetTowerMgr().GetAllData()
	match.GetGeneralMgr().GetAllData()
	match.GetConsumerTopMgr().GetAllData()
	match.GetDestroyMonsterMgr().GetAllData()
	match.GetDimensionalMgr().GetAllData()
	//match.GetCrossArenaMgr().GetAllData()
	crossserver.GetRecordMgr().GetAllData()
	crossserver.GetServerGroupMgr().GetAllData()
	crossserver.GetCrossServerArenaMgr().GetAllData()
	crossserver.GetCityBrokenMgr().GetAllData()
	crossserver.GetRankMgr().GetAllData()
	crossserver.GetCrossServerLevelArenaMgr().GetAllData()
	crossserver.GetCosmicArenaMgr().GetAllData()
	crossserver.GetOfflineInfoMgr().GetData()
	crossserver.GetChampionAllMgr().GetData()
	crossserver.GetPeakArenaMgr().GetAllData()
	match.GetCrossArenaMgr().GetAllData()
}

// ! 保存所有的数据
func (self *CenterApp) StopService() {

}

func (self *CenterApp) OnTimer() {
	core.GetMasterApp().CheckWait()
	//! 定时逻辑
	ticker := time.NewTicker(time.Second * 1)
	for {
		<-ticker.C
		self.OnLogic()
	}

	ticker.Stop()
}

// ! 逻辑处理
func (self *CenterApp) OnLogic() {
	tNow := model.TimeServer().Unix()
	if tNow-self.LastSave > PER_SAVE_TIME {
		self.LastSave = tNow

		//! 服务器数据保存
		server.GetServerMgr().OnSave()
		//! 角色数据保存
		player.GetPlayerMgr().OnSave()

		//! 公会数据保存
		//union.GetUnionMgr().OnSave()

		//跨服限时神将信息保存
		match.GetGeneralMgr().OnSave()
		match.GetDimensionalMgr().OnSave()
		match.GetConsumerTopMgr().OnSave()
		match.GetDestroyMonsterMgr().OnSave()
		//跨服竞技场信息保存
		match.GetCrossArenaMgr().OnSave()
		//顶上战争
		crossserver.GetServerGroupMgr().OnSave()
		crossserver.GetServerGroupMgr().OnSaveZone()
		crossserver.GetCrossServerArenaMgr().OnSave()
		crossserver.GetCrossServerLevelArenaMgr().OnSave()
		crossserver.GetCosmicArenaMgr().OnSave()
		crossserver.GetOfflineInfoMgr().OnSave()
		crossserver.GetCityBrokenMgr().Save()
		crossserver.GetChampionAllMgr().OnSave()
		crossserver.GetPeakArenaMgr().OnSave()
	}
	if tNow-self.LastLongSave > PER_SAVE_TIME*10 {
		self.LastLongSave = tNow
		//排行榜保存
		crossserver.GetRankMgr().OnSave()
	}
}

func (self *CenterApp) AddEvent(sid int, code int, uid int64, target int64, param1 int, param2 string) {
	if sid > 0 {
		server := server.GetServerMgr().GetServer(sid, true)
		if server != nil {
			server.PushEvent(code, uid, target, param1, param2)
		}
	} else {
		//! 中心服事件
		evt := &server.ServerEvent{
			EventCode: code,
			UId:       uid,
			Target:    target,
			Param1:    param1,
			Param2:    param2,
		}

		self.EventArr <- evt
	}

}

func (self *CenterApp) AddEvnet(int, interface{}) {

}

// ! 100ms处理一次事件
func (self *CenterApp) OnLogicEvent() {
	ticker := time.NewTicker(time.Millisecond * 100)
	for {
		select {
		case <-ticker.C:
		case event := <-self.EventArr:
			self.ProcessEvent(event)
		}
	}

	ticker.Stop()
}

func (self *CenterApp) ProcessEvent(evt *server.ServerEvent) {
	utils.LogDebug("Proc Event...", evt.EventCode, evt.Target, evt.Param1, evt.Param2)
	switch evt.EventCode {
	/*case core.SYSTEM_EVENT_CHAT_SERVER_OPEN:
	chat.GetChatMgr().AddServerChannel(evt.Param1)*/
	}
}
