package crossserver

import (
	"fmt"
	"log"
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
	"runtime/debug"
	"sort"
	"sync"
	"time"
)

// 中心服的排行榜，目前并未导入跨服机制,看后续修改
const (
	RANK_TYPE_NEW_BOSS                       = 1
	RANK_TYPE_FIGHT                          = 2  //战力
	RANK_TYPE_HERO                           = 3  //英雄
	RANK_TYPE_NEW_BOSS2                      = 4  //紧急悬赏
	RANK_TYPE_UNION_TEAMSLAY                 = 5  //团队狩猎
	RANK_TYPE_UNION_SPIDER_MOUNTAIN_UNION    = 6  //蛛山攻略公会
	RANK_TYPE_UNION_SPIDER_MOUNTAIN_PERSONAL = 7  //蛛山攻略个人
	RANK_TYPE_UNION_GHOST_DAWN_UNION         = 8  //鬼灭黎明公会排行
	RANK_TYPE_UNION_GHOST_DAWN_PERSONAL      = 9  //鬼灭黎明个人排行
	RANK_TYPE_HREAT_RANK                     = 10 //心愿冲榜跨服排行
	RANK_TYPE_CITY_BROKEN_POINT1             = 11 //千城破个人积分1
	RANK_TYPE_CITY_BROKEN_POINT2             = 12 //千城破个人积分2
	RANK_TYPE_CITY_BROKEN_SERVER_POINT2      = 13 //千城破服务器积分2
	RANK_TYPE_MAX                            = 14
)

const (
	TABLE_RANK = "tbl_rank"
	RANK_MAX   = 1000
	NOTICE_MAX = 30
)

type RankInfoMgr struct {
	Id     int //唯一ID
	Period int
	//RankData map[int64]*RankInfoDB //
	//RankArr  map[int]*RankInfoArr   //带分组的排行 key:groupId
	RankData *sync.Map
	RankArr  *sync.Map //带分组的排行 key:groupId
}

type RankMgr struct {
	//Locker *sync.RWMutex
	//RankMap   map[int]map[int]*RankInfoMgr //! 排行类型   Period期数
	//NoticeMap map[int]map[int]map[int][]*core.NoticeInfo

	RankMap   *sync.Map //! 排行类型   Period期数 RankInfoMgr
	NoticeMap *sync.Map
	InitOk    bool
}

var rankMgr *RankMgr = nil

func GetRankMgr() *RankMgr {
	if rankMgr == nil {
		rankMgr = new(RankMgr)
		rankMgr.RankMap = new(sync.Map)
		rankMgr.NoticeMap = new(sync.Map)
	}
	return rankMgr
}

func (self *RankMgr) MakeRankKey(rankId, period int) string {
	return fmt.Sprintf("%d_%d", rankId, period)
}

func (self *RankMgr) MakeNoticeKey(rankId, period, group int) string {
	return fmt.Sprintf("%d_%d_%d", rankId, period, group)
}

func (self *RankMgr) IsOldRank(period int) bool {
	//如果期数是时间戳，并且超过7天
	if period > 1000000000 && int64(period)+7*utils.DAY_SECS < model.TimeServer().Unix() {
		return true
	}
	return false
}

func (self *RankMgr) ResetSortRank() {
	res := make([]*RankInfoDB, 0)
	self.RankMap.Range(func(key, data interface{}) bool {
		//有期数的为限时活动，不需要保存在内存中
		rankInfoMgr := data.(*RankInfoMgr)
		if self.IsOldRank(rankInfoMgr.Period) {
			return true
		}
		rankInfoMgr.RankData.Range(func(uid, rankData interface{}) bool {
			v := rankData.(*RankInfoDB)
			if v.InfoData == nil {
				return true
			}
			res = append(res, v)
			return true
		})
		return true
	})

	self.RankMap = new(sync.Map)

	for i := 0; i < len(res); i++ {
		data := res[i]
		if data.Uid == 0 {
			continue
		}
		if data.Id <= 0 {
			continue
		}
		if data.InfoData == nil {
			continue
		}
		rankId := data.InfoData.RankId
		period := data.InfoData.Period
		key := self.MakeRankKey(rankId, period)

		randData, ok := self.RankMap.Load(key)
		if !ok {
			rankInfoMap := NewRankInfoMap(rankId, period)
			self.RankMap.Store(key, rankInfoMap)
		}
		randData, ok = self.RankMap.Load(key)
		if !ok {
			continue
		}
		rankInfoMap := randData.(*RankInfoMgr)
		rankInfoMap.RankData.Store(data.InfoData.Uid, data)
	}

	self.RankMap.Range(func(key, data interface{}) bool {
		rankInfoMgr := data.(*RankInfoMgr)
		if rankInfoMgr.RankData == nil {
			return true
		}
		rankInfoMgr.RankArr = new(sync.Map)
		rankInfoMgr.RankData.Range(func(uid, rankData interface{}) bool {
			v := rankData.(*RankInfoDB)
			if v.InfoData == nil {
				return true
			}
			groupId := GetServerGroupMgr().GetGroupId(v.InfoData.SvrId)
			if rankInfoMgr.Id == RANK_TYPE_UNION_TEAMSLAY {
				groupId = GetServerGroupMgr().GetZoneId(v.InfoData.SvrId)
			} else if rankInfoMgr.Id == RANK_TYPE_CITY_BROKEN_POINT1 ||
				rankInfoMgr.Id == RANK_TYPE_CITY_BROKEN_POINT2 ||
				rankInfoMgr.Id == RANK_TYPE_CITY_BROKEN_SERVER_POINT2 {
				groupId = GetCityBrokenMgr().GetGroupId(v.InfoData.SvrId)
			}
			rankInfoMgr.AddRank(groupId, v.InfoData, false, false)
			return true
		})
		rankInfoMgr.SortRank()
		return true
	})
	return
}

func NewRankInfoArr(groupId int) *RankInfoArrInfo {
	data := new(RankInfoArrInfo)
	data.GroupId = groupId
	data.RankInfoArr = make([]*model.RankInfo, 0)
	data.RankInfoArrWorking = make([]*model.RankInfo, 0)
	return data
}

func NewRankInfoMap(id int, period int) *RankInfoMgr {
	data := new(RankInfoMgr)
	data.Id = id
	data.Period = period
	data.RankData = new(sync.Map)
	data.RankArr = new(sync.Map)
	return data
}

func (self *RankMgr) Run() {
	defer func() {
		x := recover()
		if x != nil {
			log.Println(x, string(debug.Stack()))
			utils.LogError(x, string(debug.Stack()))
		}
	}()
	core.GetMasterApp().CheckWait()
	self.InitOk = true
	ticker := time.NewTicker(time.Second * 10)
	for {
		select {
		case <-ticker.C:
			self.RankSort()
		}
	}
	ticker.Stop()
}

func (self *RankMgr) RankSort() {
	self.RankMap.Range(func(key, data interface{}) bool {
		//有期数的为限时活动，不需要保存在内存中
		rankInfoMgr := data.(*RankInfoMgr)
		if rankInfoMgr.RankData == nil {
			return true
		}
		rankInfoMgr.SortRank()
		return true
	})
}

func (self *RankMgr) GetAllData() {
	core.GetMasterApp().StartWait()
	self.RankMap = new(sync.Map)

	startTime := model.TimeServer().UnixMilli()

	oldTime := model.TimeServer().Unix() - 7*utils.DAY_SECS
	queryStr := fmt.Sprintf("select * from %s where period<1000000000 or period> %d ;", TABLE_RANK, oldTime)
	var msg RankInfoDB
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	dBEndTime := model.TimeServer().UnixMilli()
	for i := 0; i < len(res); i++ {
		data := res[i].(*RankInfoDB)
		data.Decode()
		if data.Uid == 0 {
			continue
		}
		if data.Id <= 0 {
			continue
		}
		if data.InfoData == nil {
			continue
		}
		rankId := data.InfoData.RankId
		period := data.InfoData.Period
		if self.IsOldRank(period) {
			continue
		}
		key := self.MakeRankKey(rankId, period)

		randData, ok := self.RankMap.Load(key)
		if !ok {
			rankInfoMap := NewRankInfoMap(rankId, period)
			self.RankMap.Store(key, rankInfoMap)
		}
		randData, ok = self.RankMap.Load(key)
		if !ok {
			continue
		}
		rankInfoMap := randData.(*RankInfoMgr)
		rankInfoMap.RankData.Store(data.InfoData.Uid, data)
		data.Init(TABLE_RANK, data, false)
	}
	dataEndTime := model.TimeServer().UnixMilli()
	self.RankMap.Range(func(key, data interface{}) bool {
		rankInfoMgr := data.(*RankInfoMgr)
		if rankInfoMgr.RankData == nil {
			return true
		}
		rankInfoMgr.RankData.Range(func(uid, rankData interface{}) bool {
			v := rankData.(*RankInfoDB)
			if v.InfoData == nil {
				return true
			}
			groupId := GetServerGroupMgr().GetGroupId(v.InfoData.SvrId)
			if rankInfoMgr.Id == RANK_TYPE_UNION_TEAMSLAY {
				groupId = GetServerGroupMgr().GetZoneId(v.InfoData.SvrId)
			} else if rankInfoMgr.Id == RANK_TYPE_CITY_BROKEN_POINT1 ||
				rankInfoMgr.Id == RANK_TYPE_CITY_BROKEN_POINT2 ||
				rankInfoMgr.Id == RANK_TYPE_CITY_BROKEN_SERVER_POINT2 {
				groupId = GetCityBrokenMgr().GetGroupId(v.InfoData.SvrId)
			}
			rankInfoMgr.AddRank(groupId, v.InfoData, false, false)
			return true
		})
		rankInfoMgr.SortRank()
		return true
	})
	sortEndTime := model.TimeServer().UnixMilli()
	core.GetMasterApp().StartDone()

	log.Println("排行榜startTime:", startTime)
	log.Println("排行榜dBEndTime:", dBEndTime)
	log.Println("排行榜dataEndTime:", dataEndTime)
	log.Println("排行榜sortEndTime:", sortEndTime)
	return
}

func (self *RankMgr) OnSave() {
	self.RankMap.Range(func(key, data interface{}) bool {
		//有期数的为限时活动，不需要保存在内存中
		rankInfoMgr := data.(*RankInfoMgr)
		if rankInfoMgr.RankData == nil {
			return true
		}
		rankInfoMgr.RankData.Range(func(uid, rankData interface{}) bool {
			v := rankData.(*RankInfoDB)
			v.Encode()
			v.Update(true, false)
			return true
		})
		return true
	})
}

func (self *RankMgr) MgrGetRank(rankId, period int) *RankInfoMgr {
	key := self.MakeRankKey(rankId, period)
	randData, ok := self.RankMap.Load(key)
	if !ok {
		rankInfoMap := NewRankInfoMap(rankId, period)
		self.RankMap.Store(key, rankInfoMap)
	}
	randData, ok = self.RankMap.Load(key)
	if !ok {
		return nil
	}
	return randData.(*RankInfoMgr)
}
func (self *RankMgr) UploadRank(req *RPC_UploadRankReq, res *RPC_UploadRankRes) {
	rankId := req.RankId
	period := 0
	if req.RankInfo != nil {
		period = req.RankInfo.Period
	}
	rankInfoMap := self.MgrGetRank(rankId, period)
	if rankInfoMap == nil {
		return
	}
	rankInfoMap.UpdateData(req.RankInfo, req.GetItem)
}

func (self *RankMgr) UploadNotice(req *RPC_UploadRankReq, res *RPC_UploadRankRes) {
	rankId := req.RankId
	rankInfo := req.RankInfo
	if rankInfo == nil {
		return
	}
	groupId := GetServerGroupMgr().GetGroupId(req.ServerId)
	if rankId == RANK_TYPE_UNION_TEAMSLAY {
		groupId = GetServerGroupMgr().GetZoneId(req.ServerId)
	} else if rankId == RANK_TYPE_CITY_BROKEN_POINT1 ||
		rankId == RANK_TYPE_CITY_BROKEN_POINT2 ||
		rankId == RANK_TYPE_CITY_BROKEN_SERVER_POINT2 {
		groupId = GetCityBrokenMgr().GetGroupId(req.ServerId)
	}

	key := self.MakeNoticeKey(rankId, rankInfo.Period, groupId)

	noticeListData, ok := self.NoticeMap.Load(key)
	if !ok {
		noticeList := make([]*model.NoticeInfo, 0)
		self.NoticeMap.Store(key, noticeList)
	}
	noticeListData, ok = self.NoticeMap.Load(key)
	if !ok {
		return
	}
	noticeList := noticeListData.([]*model.NoticeInfo)

	//增加通知
	if len(noticeList) >= NEW_NOTICE_MAX {
		noticeList = noticeList[1:NEW_NOTICE_MAX]
	}
	newNotice := new(model.NoticeInfo)
	newNotice.ServerId = req.RankInfo.SvrId
	newNotice.LevelID = req.RankInfo.LevelId
	newNotice.Name = req.RankInfo.UName
	newNotice.RankId = req.RankInfo.RankId
	newNotice.Time = model.TimeServer().Unix()
	newNotice.GetItem = req.GetItem
	noticeList = append(noticeList, newNotice)
	self.NoticeMap.Store(key, noticeList)
}

func (self *RankInfoMgr) UpdateData(data *model.RankInfo, getItem []model.PassItem) {
	groupId := GetServerGroupMgr().GetGroupId(data.SvrId)
	if self.Id == RANK_TYPE_UNION_TEAMSLAY {
		groupId = GetServerGroupMgr().GetZoneId(data.SvrId)
	} else if self.Id == RANK_TYPE_CITY_BROKEN_POINT1 ||
		self.Id == RANK_TYPE_CITY_BROKEN_POINT2 ||
		self.Id == RANK_TYPE_CITY_BROKEN_SERVER_POINT2 {
		groupId = GetCityBrokenMgr().GetGroupIdSafe(data.SvrId)
	} else if self.Id == RANK_TYPE_HREAT_RANK {
		groupId = 0
	}

	add := false
	dataDB, ok := self.RankData.Load(data.Uid)
	if !ok {
		dataNew := new(RankInfoDB)
		dataNew.InfoData = data
		dataNew.RankId = self.Id
		dataNew.Period = self.Period
		dataNew.Uid = data.Uid
		dataNew.Id = int(db.InsertTable(TABLE_RANK, dataNew, 0, false))
		dataNew.Init(TABLE_RANK, dataNew, false)
		self.RankData.Store(dataNew.Uid, dataNew)
		dataDB, ok = self.RankData.Load(data.Uid)
		add = false
	}
	if !ok {
		return
	}
	userData := dataDB.(*RankInfoDB)
	if userData == nil {
		return
	}
	if userData.InfoData != nil {
		userData.InfoData.SvrId = data.SvrId
		userData.InfoData.SvrName = data.SvrName
	}
	userData.InfoData = self.AddRank(groupId, data, true, add)
	return
}

func (self *RankMgr) UpdateBeLike(rankInfos []*model.RankInfo) {
	for index, v := range rankInfos {
		if index >= 3 {
			break
		}
		p := core.GetPlayerMgr().GetCorePlayer(v.Uid, true)
		if utils.IsNil(p) {
			continue
		}
		v.BeLike = p.GetDataInt(core.PLAYER_ATT_LIKE)
	}
}
func (self *RankMgr) GetRank(req *RPC_GetRankReq, res *RPC_GetRankRes) {
	rankInfoMap := self.MgrGetRank(req.RankId, req.Period)
	if rankInfoMap == nil {
		return
	}
	groupId := GetServerGroupMgr().GetGroupId(req.ServerId)
	if req.RankId == RANK_TYPE_UNION_TEAMSLAY {
		groupId = GetServerGroupMgr().GetZoneId(req.ServerId)
	} else if req.RankId == RANK_TYPE_CITY_BROKEN_POINT1 ||
		req.RankId == RANK_TYPE_CITY_BROKEN_POINT2 ||
		req.RankId == RANK_TYPE_CITY_BROKEN_SERVER_POINT2 {
		groupId = GetCityBrokenMgr().GetGroupId(req.ServerId)
	}
	//获取排行
	res.RankInfo = rankInfoMap.GetRankArr(groupId)
	//更新点赞
	self.UpdateBeLike(res.RankInfo)
	//获取个人信息
	res.SelfInfo = rankInfoMap.GetUserRankInfo(req.Uid)
}

func (self *RankMgr) GetMyRank(rankId, period int, uid int64) *model.RankInfo {
	rankInfoMap := self.MgrGetRank(rankId, period)
	if rankInfoMap == nil {
		return nil
	}
	return rankInfoMap.GetUserRankInfo(uid)
}

func (self *RankMgr) GetRankReward(req *RPC_GetRankReq, res *RPC_GetRankRes) {
	rankInfoMap := self.MgrGetRank(req.RankId, req.Period)
	if rankInfoMap == nil {
		return
	}
	groupId := GetServerGroupMgr().GetGroupId(req.ServerId)
	if req.RankId == RANK_TYPE_UNION_TEAMSLAY {
		groupId = GetServerGroupMgr().GetZoneId(req.ServerId)
	} else if req.RankId == RANK_TYPE_CITY_BROKEN_POINT1 ||
		req.RankId == RANK_TYPE_CITY_BROKEN_POINT2 ||
		req.RankId == RANK_TYPE_CITY_BROKEN_SERVER_POINT2 {
		groupId = GetCityBrokenMgr().GetGroupId(req.ServerId)
	}
	rankArrData, ok := rankInfoMap.RankArr.Load(groupId)
	if !ok {
		return
	}
	rankArr := rankArrData.(*RankInfoArrInfo)
	if rankArr == nil {
		return
	}
	for _, v := range rankArr.RankInfoArr {
		if v.SvrId != req.ServerId {
			continue
		}
		res.RankInfo = append(res.RankInfo, v)
	}
}

func (self *RankMgr) GetNotice(req *RPC_GetNoticeReq, res *RPC_GetNoticeRes) {

	groupId := GetServerGroupMgr().GetGroupId(req.ServerId)
	if req.NoticeId == RANK_TYPE_UNION_TEAMSLAY {
		groupId = GetServerGroupMgr().GetZoneId(req.ServerId)
	} else if req.NoticeId == RANK_TYPE_CITY_BROKEN_POINT1 ||
		req.NoticeId == RANK_TYPE_CITY_BROKEN_POINT2 ||
		req.NoticeId == RANK_TYPE_CITY_BROKEN_SERVER_POINT2 {
		groupId = GetCityBrokenMgr().GetGroupId(req.ServerId)
	}
	key := self.MakeNoticeKey(req.NoticeId, req.Period, groupId)

	noticeListData, ok := self.NoticeMap.Load(key)
	if !ok {
		return
	}
	info := noticeListData.([]*model.NoticeInfo)
	if len(info) > NOTICE_MAX {
		res.NoticeList = info[0:NOTICE_MAX]
	} else {
		res.NoticeList = info
	}
}

func (self *RankMgr) RankDoLike(req *RPC_RankDoLikeReq, res *RPC_RankDoLikeRes) {

	rankInfoMap := self.MgrGetRank(req.RankId, req.Period)
	if rankInfoMap == nil {
		return
	}
	p := core.GetPlayerMgr().GetCorePlayer(req.TargetUid, true)
	if utils.IsNil(p) {
		res.RetCode = RETCODE_DATA_CROSS_PARAM_ERROR
		return
	}
	res.TargetLike = p.AddDataInt(core.PLAYER_ATT_LIKE, 1)
	res.TargetUid = req.TargetUid
}

func (self *RankMgr) DeletePlayerRecord(req *RPC_RankDoLikeReq) {
	groupId := GetServerGroupMgr().GetGroupId(req.ServerId)
	self.RankMap.Range(func(key, data interface{}) bool {
		periodMap := data.(*RankInfoMgr)
		infoData, ok := periodMap.RankData.Load(req.Uid)
		if !ok {
			return true
		}
		info := infoData.(*RankInfoDB)
		if info == nil || info.InfoData == nil {
			return true
		}
		if periodMap.Id == RANK_TYPE_UNION_TEAMSLAY {
			groupId = GetServerGroupMgr().GetZoneId(req.ServerId)
		} else if periodMap.Id == RANK_TYPE_CITY_BROKEN_POINT1 ||
			periodMap.Id == RANK_TYPE_CITY_BROKEN_POINT2 ||
			periodMap.Id == RANK_TYPE_CITY_BROKEN_SERVER_POINT2 {
			groupId = GetCityBrokenMgr().GetGroupId(req.ServerId)
		}
		info.InfoData.Num = 0
		info.InfoData.LevelId = 0
		periodMap.SetSortNeed(groupId, true)
		return true
	})
}

func (self *RankInfoMgr) AddRank(groupId int, rankInfo *model.RankInfo, isFind bool, add bool) *model.RankInfo {
	if rankInfo == nil {
		return rankInfo
	}
	rankArrInfoData, arrOk := self.RankArr.Load(groupId)
	if !arrOk {
		newRankArrInfo := NewRankInfoArr(groupId)
		self.RankArr.Store(newRankArrInfo.GroupId, newRankArrInfo)
		rankArrInfoData, _ = self.RankArr.Load(groupId)
	}
	if rankArrInfoData == nil {
		return rankInfo
	}
	rankArrInfo := rankArrInfoData.(*RankInfoArrInfo)
	if rankArrInfo == nil {
		return rankInfo
	}
	if isFind {
		for i := 0; i < len(rankArrInfo.RankInfoArrWorking); i++ {
			if rankArrInfo.RankInfoArrWorking[i] == nil {
				continue
			}
			if rankArrInfo.RankInfoArrWorking[i].Uid == rankInfo.Uid {
				if rankInfo.RankId == RANK_TYPE_NEW_BOSS {
					//紧急悬赏的特殊判断
					if rankArrInfo.RankInfoArrWorking[i].LevelId < 800131 || rankInfo.LevelId < 800131 {
						if rankArrInfo.RankInfoArrWorking[i].LevelId < rankInfo.LevelId {
							rankArrInfo.NeedSort = true
							rankArrInfo.RankInfoArrWorking[i] = rankInfo
						} else if rankArrInfo.RankInfoArrWorking[i].LevelId == rankInfo.LevelId &&
							rankArrInfo.RankInfoArrWorking[i].Num < rankInfo.Num {
							rankArrInfo.NeedSort = true
							rankArrInfo.RankInfoArrWorking[i] = rankInfo
						}
					} else {
						if rankArrInfo.RankInfoArrWorking[i].Num < rankInfo.Num {
							rankArrInfo.NeedSort = true
							rankArrInfo.RankInfoArrWorking[i] = rankInfo
						}
					}
				} else if rankInfo.RankId == RANK_TYPE_NEW_BOSS2 {
					if rankArrInfo.RankInfoArrWorking[i].LevelId < 700198 || rankInfo.LevelId < 700198 {
						if rankArrInfo.RankInfoArrWorking[i].LevelId < rankInfo.LevelId {
							rankArrInfo.NeedSort = true
							rankArrInfo.RankInfoArrWorking[i] = rankInfo
						} else if rankArrInfo.RankInfoArrWorking[i].LevelId == rankInfo.LevelId &&
							rankArrInfo.RankInfoArrWorking[i].LeftTime < rankInfo.LeftTime {
							rankArrInfo.NeedSort = true
							rankArrInfo.RankInfoArrWorking[i] = rankInfo
						}
					} else {
						if rankArrInfo.RankInfoArrWorking[i].LeftTime < rankInfo.LeftTime {
							rankArrInfo.NeedSort = true
							rankArrInfo.RankInfoArrWorking[i] = rankInfo
						}
					}
				} else if rankInfo.RankId == RANK_TYPE_UNION_SPIDER_MOUNTAIN_UNION ||
					rankInfo.RankId == RANK_TYPE_CITY_BROKEN_POINT1 ||
					rankInfo.RankId == RANK_TYPE_CITY_BROKEN_POINT2 ||
					rankInfo.RankId == RANK_TYPE_CITY_BROKEN_SERVER_POINT2 {
					if add {
						rankArrInfo.NeedSort = true
						rankArrInfo.RankInfoArrWorking[i] = rankInfo
					} else {
						oldNum := rankArrInfo.RankInfoArrWorking[i].Num
						rankInfo.Num += oldNum
						rankArrInfo.NeedSort = true
						rankArrInfo.RankInfoArrWorking[i] = rankInfo
					}
				} else {
					if rankArrInfo.RankInfoArrWorking[i].Num != rankInfo.Num {
						rankArrInfo.NeedSort = true
						rankArrInfo.RankInfoArrWorking[i] = rankInfo
					}
				}
				return rankArrInfo.RankInfoArrWorking[i]
			}
		}
	}
	rankArrInfo.NeedSort = true
	rankArrInfo.RankInfoArrWorking = append(rankArrInfo.RankInfoArrWorking, rankInfo)
	return rankInfo
}

func (self *RankInfoMgr) SortRank() {
	self.RankArr.Range(func(key, rankArr interface{}) bool {
		rankInfoArrInfo := rankArr.(*RankInfoArrInfo)
		if !rankInfoArrInfo.NeedSort {
			return true
		}
		if self.Id == RANK_TYPE_UNION_TEAMSLAY {
			utils.LogError(fmt.Sprintf("rank %d start time: %d group : %d", self.Id, model.TimeServer().Unix(), rankInfoArrInfo.GroupId))
			WriterLog(5, fmt.Sprintf("rank %d start time: %d group : %d", self.Id, model.TimeServer().Unix(), rankInfoArrInfo.GroupId))
		}
		sort.Sort(rankInfoArrInfo.RankInfoArrWorking)
		for index, v := range rankInfoArrInfo.RankInfoArrWorking {
			v.Rank = index + 1
		}
		rankInfoArrInfo.RankInfoArr = rankInfoArrInfo.RankInfoArrWorking
		rankInfoArrInfo.NeedSort = false
		if self.Id == RANK_TYPE_UNION_TEAMSLAY {
			utils.LogError(fmt.Sprintf("rank %d end time: %d group : %d", self.Id, model.TimeServer().Unix(), rankInfoArrInfo.GroupId))
			WriterLog(5, fmt.Sprintf("rank %d end time: %d group : %d", self.Id, model.TimeServer().Unix(), rankInfoArrInfo.GroupId))
		}
		return true
	})
}

func (self *RankInfoMgr) SetSortNeed(groupId int, needSort bool) {
	rankArrData, ok := self.RankArr.Load(groupId)
	if !ok {
		return
	}
	rankArr := rankArrData.(*RankInfoArrInfo)
	if rankArr == nil {
		return
	}
	rankArr.NeedSort = needSort
	return
}

func (self *RankInfoMgr) GetRankArr(groupId int) []*model.RankInfo {
	rankArrData, ok := self.RankArr.Load(groupId)
	if !ok {
		return nil
	}
	rankArr := rankArrData.(*RankInfoArrInfo)
	if rankArr == nil {
		return nil
	}
	if len(rankArr.RankInfoArr) > 50 {
		return rankArr.RankInfoArr[:50]
	}
	return rankArr.RankInfoArr
}

func (self *RankInfoMgr) GetUserRankInfo(uid int64) *model.RankInfo {
	userRankInfoData, ok := self.RankData.Load(uid)
	if !ok {
		return nil
	}
	userRankInfo := userRankInfoData.(*RankInfoDB)
	if userRankInfo == nil {
		return nil
	}
	return userRankInfo.InfoData
}
