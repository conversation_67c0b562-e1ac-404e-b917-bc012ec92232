package crossserver

import "master/utils"

type DemonslayerWorldMgr struct {
}

var demonslayerWorldMgr *DemonslayerWorldMgr = nil

func GetDemonslayerWorldMgr() *DemonslayerWorldMgr {
	if demonslayerWorldMgr == nil {
		demonslayerWorldMgr = new(DemonslayerWorldMgr)
	}
	return demonslayerWorldMgr
}

func (self *DemonslayerWorldMgr) GetConfig(serverId int) string {
	zoneConfig := GetServerGroupMgr().GetZoneConfig()
	return utils.HF_JtoA(zoneConfig)
}
