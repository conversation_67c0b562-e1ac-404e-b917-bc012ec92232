package match

import (
	"fmt"
	json "github.com/bytedance/sonic"
	"master/center/crossserver"
	"master/db"
	"master/model"
	"master/utils"
	"net/http"
	"sort"
	"sync"
)

type Js_DimensionalUser struct {
	Uid     int64  `json:"uid"`
	SvrId   int    `json:"svrid"`
	SvrName string `json:"svrname"`
	ActType int    `json:"act_type"`
	UName   string `json:"uname"`
	Level   int    `json:"level"`
	Vip     int    `json:"vip"`
	Icon    int    `json:"icon"`
	Point   int    `json:"point"`
	Rank    int    `json:"rank"`
	KeyId   int    `json:"keyid"`
	Time    int64  `json:"time"`
	OpenDay int    `json:"open_day"`
}
type Js_DimensionalUserDB struct {
	Uid   int64  `json:"uid"`
	KeyId int    `json:"keyid"`
	SvrId int    `json:"svrid"`
	Info  string `json:"info"`

	info *Js_DimensionalUser
	db.DataUpdate
}

func (self *Js_DimensionalUserDB) Encode() {
	self.Info = utils.HF_JtoA(self.info)
}

func (self *Js_DimensionalUserDB) Decode() {
	json.Unmarshal([]byte(self.Info), &self.info)
}

type DimensionalRecord struct {
	Uid        int64  `json:"uid"`
	SvrId      int    `json:"svrid"`
	ActType    int    `json:"act_type"`
	UName      string `json:"uname"`
	Item       int    `json:"item"`
	Num        int    `json:"num"`
	Time       int64  `json:"time"`
	RecordType int    `json:"recordtype"`
}

type DimensionalUserArr []*Js_DimensionalUser

func (s DimensionalUserArr) Len() int      { return len(s) }
func (s DimensionalUserArr) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s DimensionalUserArr) Less(i, j int) bool {
	if s[i].Point == s[j].Point {
		return s[i].Time < s[j].Time
	}
	return s[i].Point > s[j].Point
}

type DimensionalInfo struct {
	KeyId int `json:"keyid"`

	Mu                        *sync.RWMutex
	dimensionalUserTop        map[int64]*Js_DimensionalUserDB
	dimensionalUserTopNodeArr map[int]DimensionalUserArr
	dimensionalRecord         map[int][]*DimensionalRecord
}

type DimensionalMgr struct {
	Locker          *sync.RWMutex
	DimensionalInfo map[int]*DimensionalInfo
}

var dimensionalMgr *DimensionalMgr = nil

func GetDimensionalMgr() *DimensionalMgr {
	if dimensionalMgr == nil {
		dimensionalMgr = new(DimensionalMgr)
		dimensionalMgr.DimensionalInfo = make(map[int]*DimensionalInfo)
		dimensionalMgr.Locker = new(sync.RWMutex)
	}

	return dimensionalMgr
}

// 存储数据库
func (self *DimensionalMgr) OnSave() {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	for _, v := range self.DimensionalInfo {
		v.Save()
	}
}

func (self *DimensionalInfo) Save() {
	self.Mu.Lock()
	defer self.Mu.Unlock()
	for _, v := range self.dimensionalUserTop {
		v.Encode()
		v.UpdateEx("keyid", v.KeyId)
	}
}

func (self *DimensionalMgr) NewDimensionalInfo(KeyId int) *DimensionalInfo {
	data := new(DimensionalInfo)
	data.KeyId = KeyId
	data.Mu = new(sync.RWMutex)
	data.dimensionalUserTop = make(map[int64]*Js_DimensionalUserDB)
	data.dimensionalUserTopNodeArr = make(map[int]DimensionalUserArr)
	data.dimensionalRecord = make(map[int][]*DimensionalRecord)
	return data
}

func (self *DimensionalMgr) GetAllData() {
	self.LoadDimensional()
}

func (self *DimensionalMgr) LoadDimensional() {
	self.Locker.Lock()
	defer self.Locker.Unlock()

	queryStr := fmt.Sprintf("select uid,keyid,svrid,info from `tbl_dimensionaluser`;")
	var msg Js_DimensionalUserDB
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	for i := 0; i < len(res); i++ {
		data := res[i].(*Js_DimensionalUserDB)
		if data.KeyId > 0 {
			_, ok := self.DimensionalInfo[data.KeyId]
			if !ok {
				self.DimensionalInfo[data.KeyId] = self.NewDimensionalInfo(data.KeyId)
			}

			if self.DimensionalInfo[data.KeyId] == nil {
				continue
			}
			data.Decode()
			if data.info == nil {
				continue
			}

			data.Init("tbl_dimensionaluser", data, true)
			if self.DimensionalInfo[data.KeyId].dimensionalUserTop == nil {
				self.DimensionalInfo[data.KeyId].dimensionalUserTop = make(map[int64]*Js_DimensionalUserDB)
			}
			_, ok3 := self.DimensionalInfo[data.KeyId].dimensionalUserTop[data.Uid]
			if !ok3 {
				self.DimensionalInfo[data.KeyId].dimensionalUserTop[data.Uid] = data
			}
		}
	}
	for _, data := range self.DimensionalInfo {
		data.MakeArr()
	}
}

func (self *DimensionalMgr) GetUserGroupSafe(serverId int, openday int, keyid int) int {
	//if keyid < 10000 && openday > 0 {
	//	return openday
	//} else {
	//	if serverId <= 14 {
	//		return 1
	//	} else {
	//		return 2
	//	}
	//}

	return keyid
}

func (self *DimensionalMgr) GetUserGroupNoSafe(serverId int, openday int, keyid int) int {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	//if keyid < 10000 && openday > 0 {
	//	return openday
	//} else {
	//	if serverId <= 14 {
	//		return 1
	//	} else {
	//		return 2
	//	}
	//}

	return keyid
}

func (self *DimensionalMgr) GetUserZoneSafe(serverId int) int {
	return crossserver.GetServerGroupMgr().GetZoneId(serverId)
	//return serverId
}

func (self *DimensionalMgr) GetUserZoneNoSafe(serverId int) int {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	return crossserver.GetServerGroupMgr().GetZoneId(serverId)
	//return serverId
}

func (self *DimensionalInfo) MakeArr() {
	self.Mu.Lock()
	defer self.Mu.Unlock()

	self.dimensionalUserTopNodeArr = make(map[int]DimensionalUserArr, 0)
	for _, v := range self.dimensionalUserTop {
		group := GetDimensionalMgr().GetUserGroupSafe(v.SvrId, v.info.OpenDay, self.KeyId)
		self.dimensionalUserTopNodeArr[group] = append(self.dimensionalUserTopNodeArr[group], v.info)
	}
	for _, v := range self.dimensionalUserTopNodeArr {
		sort.Sort(v)
	}

	for _, v := range self.dimensionalUserTopNodeArr {
		for i := 0; i < len(v); i++ {
			v[i].Rank = i + 1
		}
	}
}

func (self *DimensionalInfo) MakeArrSafe() {
	self.dimensionalUserTopNodeArr = make(map[int]DimensionalUserArr, 0)
	for _, v := range self.dimensionalUserTop {
		group := GetDimensionalMgr().GetUserGroupSafe(v.SvrId, v.info.OpenDay, self.KeyId)
		self.dimensionalUserTopNodeArr[group] = append(self.dimensionalUserTopNodeArr[group], v.info)
	}
	for _, v := range self.dimensionalUserTopNodeArr {
		sort.Sort(v)
	}

	for _, v := range self.dimensionalUserTopNodeArr {
		for i := 0; i < len(v); i++ {
			v[i].Rank = i + 1
		}
	}
}

func (self *DimensionalMgr) UpdatePoint(req *RPC_DimensionalActionReq, res *RPC_DimensionalActionRes) {
	if req.SelfInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	self.Locker.Lock()
	info, ok := self.DimensionalInfo[req.SelfInfo.KeyId]
	if !ok {
		info = self.NewDimensionalInfo(req.SelfInfo.KeyId)
		self.DimensionalInfo[info.KeyId] = info
	}
	self.Locker.Unlock()

	info.UpdatePoint(req, res)
}

func (self *DimensionalInfo) UpdatePoint(req *RPC_DimensionalActionReq, res *RPC_DimensionalActionRes) {
	if req.SelfInfo == nil {
		res.RetCode = RETCODE_DATA_ERROR
		return
	}
	group := GetDimensionalMgr().GetUserGroupSafe(req.SelfInfo.SvrId, req.SelfInfo.OpenDay, self.KeyId)

	self.Mu.Lock()
	defer self.Mu.Unlock()

	info, ok := self.dimensionalUserTop[req.SelfInfo.Uid]
	if ok {
		info.info.UName = req.SelfInfo.UName
		info.info.Level = req.SelfInfo.Level
		info.info.Vip = req.SelfInfo.Vip
		info.info.Icon = req.SelfInfo.Icon
		info.info.SvrName = req.SelfInfo.SvrName
		// 补丁 修复dimensionalUserTopNodeArr没有玩家数据的问题
		if info.info.OpenDay != req.SelfInfo.OpenDay {
			find := false
			for _, value := range self.dimensionalUserTopNodeArr[group] {
				if value.Uid == info.Uid {
					find = true
					break
				}
			}
			//if !find {
			//	self.dimensionalUserTopNodeArr[group] = append(self.dimensionalUserTopNodeArr[group], info.info)
			//}
			//oldgroup := GetDimensionalMgr().GetUserGroupSafe(req.SelfInfo.SvrId, info.info.OpenDay, self.KeyId)
			//for i, value := range self.dimensionalUserTopNodeArr[oldgroup] {
			//	if value.Uid == info.Uid {
			//		self.dimensionalUserTopNodeArr[oldgroup] = append(self.dimensionalUserTopNodeArr[oldgroup][:i], self.dimensionalUserTopNodeArr[oldgroup][i+1:]...)
			//		break
			//	}
			//}
			info.info.OpenDay = req.SelfInfo.OpenDay
			if !find {
				self.MakeArrSafe()
			}
		}

		if info.info.Point > req.SelfInfo.Point {
			return
		}
		info.info.Point = req.SelfInfo.Point
		info.info.Time = model.TimeServer().Unix()
	} else {
		dbData := new(Js_DimensionalUserDB)
		dbData.info = req.SelfInfo
		dbData.KeyId = self.KeyId
		dbData.SvrId = req.SelfInfo.SvrId
		dbData.Uid = req.SelfInfo.Uid
		dbData.info.Rank = len(self.dimensionalUserTopNodeArr[group]) + 1
		dbData.Encode()
		db.InsertTable("tbl_dimensionaluser", dbData, 0, true)
		dbData.Init("tbl_dimensionaluser", dbData, true)
		self.dimensionalUserTop[req.SelfInfo.Uid] = dbData
		self.dimensionalUserTopNodeArr[group] = append(self.dimensionalUserTopNodeArr[group], dbData.info)
		info = dbData
	}

	for i := info.info.Rank - 2; i >= 0 && i < self.dimensionalUserTopNodeArr[group].Len(); i-- {
		if info.info.Point > self.dimensionalUserTopNodeArr[group][i].Point {
			self.dimensionalUserTopNodeArr[group][i].Rank++
			info.info.Rank--
			self.dimensionalUserTopNodeArr[group].Swap(info.info.Rank-1, self.dimensionalUserTopNodeArr[group][i].Rank-1)
		} else {
			break
		}
	}

	self.dimensionalRecord[group] = append(self.dimensionalRecord[group], req.DimensionalRecord...)
	size := len(self.dimensionalRecord[group])
	if size > RECORD_MAX {
		self.dimensionalRecord[group] = self.dimensionalRecord[group][size-RECORD_MAX:]
	}

	res.RetCode = RETCODE_OK
	if len(self.dimensionalUserTopNodeArr[group]) > 50 {
		res.RankInfo = self.dimensionalUserTopNodeArr[group][:50]
	} else {
		res.RankInfo = self.dimensionalUserTopNodeArr[group]
	}
	res.SelfInfo = self.dimensionalUserTop[req.Uid].info
	res.DimensionalRecord = self.dimensionalRecord[group]
	return
}

func (self *DimensionalMgr) GetAllRank(req *RPC_DimensionalActionReqAll, res *RPC_DimensionalActionRes) {
	self.Locker.RLock()
	info, ok := self.DimensionalInfo[req.KeyId]
	self.Locker.RUnlock()
	if ok {
		info.GetAllRank(req, res)
	}
}

func (self *DimensionalInfo) GetAllRank(req *RPC_DimensionalActionReqAll, res *RPC_DimensionalActionRes) {
	res.RetCode = RETCODE_OK
	group := GetDimensionalMgr().GetUserGroupNoSafe(req.ServerId, req.OpenDay, self.KeyId)

	self.Mu.RLock()
	defer self.Mu.RUnlock()
	_, ok := self.dimensionalUserTopNodeArr[group]
	if ok {
		res.RankInfo = self.dimensionalUserTopNodeArr[group]
		res.DimensionalRecord = self.dimensionalRecord[group]
	}
}

func (self *DimensionalMgr) GmSortRank(w http.ResponseWriter, r *http.Request) {
	self.Locker.Lock()
	defer self.Locker.Unlock()

	stringkeyid := r.FormValue("keyid")
	keyid := utils.HF_Atoi(stringkeyid)

	for _, data := range self.DimensionalInfo {
		if keyid != 0 && data.KeyId == keyid {
			data.MakeArr()
			break
		} else if keyid == 0 {
			data.MakeArr()
		}
	}
}
