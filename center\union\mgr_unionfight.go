package union

import (
	"master/core"
	"master/csvs"
	"master/db"
	"master/model"
	"sync"
	"time"
)

// UnionActivityInfo ! 跨服公会战-分组-对象
type UnionActivityInfo struct {
	Id         int    //! 活动Id
	Period     int    //! 期数
	Group      int    //! 对应跨服组
	Stage      int    //! 阶段
	StartTime  int64  //! 开始时间
	AttackTime int64  //! 攻击时间
	EndTime    int64  //! 结束时间
	Info       string //! 数据

	UnionFight *sync.Map //! 公会战实例,map<teamid>*unionfight
	UnionMap   *sync.Map //! 公会Map,分组Id，原型：map<unionid>teamid
	db.DataUpdate
}

func (self *UnionActivityInfo) InitData() {
	if self == nil {
		return
	}

	self.Period++
	nowTime := model.TimeServer()
	currentYear, currentMonth, currentDay := nowTime.Date()
	currentLocation := nowTime.Location()
	self.StartTime = time.Date(currentYear, currentMonth, currentDay, 0, 0, 0, 0, currentLocation).Unix()

	// 如果这个时间小于现在，更改计算的基准时间
	if self.StartTime+10 < nowTime.Unix() {
		nowTime = nowTime.AddDate(0, 0, 1)
		currentYear, currentMonth, currentDay = nowTime.Date()
		currentLocation = nowTime.Location()
		self.StartTime = time.Date(currentYear, currentMonth, currentDay, 0, 0, 0, 0, currentLocation).Unix()
	}

	judgeIndex := int64(nowTime.Weekday() + 1)
	dis := int64(7)
	for _, v := range csvs.TimeResetConfigArr {
		if v.System != core.TIME_RESET_SYSTEM_UNIONACTIVITY {
			continue
		}
		for _, index := range v.Time {
			if index == 0 {
				continue
			}
			indexDis := (index - judgeIndex + 7) % 7
			if indexDis < dis {
				dis = indexDis
			}
		}
	}
	self.StartTime += dis * core.DAY_SECS
	self.Stage = core.UNIONACTIVITY_STAGE_INVALID
	self.AttackTime = self.StartTime + 12*core.HOUR_SECS
	self.EndTime = self.AttackTime + 11*core.HOUR_SECS + 45*core.MIN_SECS

	self.Save()
	return
}

func (self *UnionActivityInfo) Decode() {

}

func (self *UnionActivityInfo) Encode() {

}

func (self *UnionActivityInfo) Save() {
	self.Encode()
	self.Update(true, false)

	if self.UnionFight != nil {
		self.UnionFight.Range(func(key, unionFight interface{}) bool {
			v := unionFight.(*UnionFight)
			v.Encode()
			v.Update(true, false)
			return true
		})
	}
}

// GetUnionMember ! 获取公会成员
func (self *UnionActivityInfo) GetUnionMember(unionId int, uid int64) *UnionFightMemberNode {
	teamId, ok := self.UnionMap.Load(unionId)
	if !ok {
		return nil
	}

	unionFight, ok1 := self.UnionFight.Load(teamId)
	if ok1 {
		return nil
	}

	v := unionFight.(*UnionFight)
	var member *UnionFightMemberNode = nil
	if v.AttackSide.UnionId == unionId {
		for i := 0; i < len(v.AttackSide.UnionFightMemberNode); i++ {
			if v.AttackSide.UnionFightMemberNode[i].Uid == uid {
				member = v.AttackSide.UnionFightMemberNode[i]
			}
		}
	} else if v.DefenceSide.UnionId == unionId {
		for i := 0; i < len(v.DefenceSide.UnionFightMemberNode); i++ {
			if v.DefenceSide.UnionFightMemberNode[i].Uid == uid {
				member = v.DefenceSide.UnionFightMemberNode[i]
			}
		}
	}
	return member
}

func (self *UnionActivityInfo) OnTimer() {
	if self == nil {
		return
	}

	/*jsOpen := csvs.ServerIsModOpen(core.OPEN_UNION_ACTIVITY)
	if !jsOpen {
		return
	}*/
	if self.StartTime == 0 {
		self.InitData()
	}
	nowTime := model.TimeServer().Unix()
	switch self.Stage {
	case core.UNIONACTIVITY_STAGE_INVALID:
		//尚未开始
		if nowTime < self.StartTime {
			return
		}
		self.ChangeStage(core.UNIONACTIVITY_STAGE_WAIT)
	case core.UNIONACTIVITY_STAGE_WAIT:
		if nowTime < self.AttackTime {
			return
		}
		self.ChangeStage(core.UNIONACTIVITY_STAGE_ATTACK)
	case core.UNIONACTIVITY_STAGE_ATTACK:
		if nowTime < self.EndTime {
			return
		}
		//结算
		self.SendReward()
		GetUnionActivityMgr().MakeHistory()
		self.InitData()
	}
}

// ChangeStage 切换阶段
func (self *UnionActivityInfo) ChangeStage(stage int) {
	self.Stage = stage
	//可能需要广播
	switch self.Stage {
	case core.UNIONACTIVITY_STAGE_INVALID:
	case core.UNIONACTIVITY_STAGE_WAIT:
		//生成公会数据
		self.UnionFight, self.UnionMap = GetUnionMgr().MakeUnionActivityData(self.Period)
	case core.UNIONACTIVITY_STAGE_ATTACK:
	default:
		self.SendReward()
		self.InitData()
	}
	self.SendSynMessage()
}

// SendSynMessage 阶段同步
func (self *UnionActivityInfo) SendSynMessage() {
	/*var msg protocol.S2C_UnionActivitySyn
	msg.Cid = MSG_UNION_ACTIVITY_SYN
	msg.Stage = self.Stage
	msg.StartTime = self.StartTime
	msg.AttackTime = self.AttackTime
	msg.EndTime = self.EndTime
	core.GetPlayerMgr().BroadcastMsg(msg.Cid, utils.HF_JtoB(&msg))*/
}

// SendReward 发送奖励
func (self *UnionActivityInfo) SendReward() {
	if self == nil {
		return
	}
	if self.UnionFight == nil {
		return
	}
	self.UnionFight.Range(func(key, unionFight interface{}) bool {
		if unionFight == nil {
			return true
		}
		v := unionFight.(*UnionFight)
		if v == nil {
			return true
		}
		v.CalResult()
		if v.AttackSide == nil || v.DefenceSide == nil {
			return true
		}
		for _, rankInfo := range v.AttackSide.UnionActivityRank {
			GetUnionActivityMgr().SendRankAward(v.AttackSide.ServerId, rankInfo.Uid, rankInfo.RankPos)
			/*config := csvs.GetRankAwardConfig(models.ARENA_REWARD_TYPE_UNION_ACTIVITY, 1, rankInfo.RankPos, 0)
			if nil == config {
				continue
			}

			var out []models.PassItem

			itemLen := len(config.Items)
			if itemLen != len(config.Nums) {
				continue
			}

			for j := 0; j < itemLen; j++ {
				if config.Items[j] != 0 {
					out = append(out, models.PassItem{ItemID: config.Items[j], Num: config.Nums[j]})
				}
			}
			lang := GetOfflineInfoMgr().GetLang(rankInfo.Uid)
			mailConfig := csvs.GetMailConfig(config.MailId, lang)
			if mailConfig != nil {
				context := csvs.GetText(fmt.Sprintf(mailConfig.Mailtxt, v.AttackSide.MasterName), lang)
				GetMailMgr().AddPlayerMail(rankInfo.Uid, mailConfig.Flag, 1, 1, 0,
					mailConfig.Mailtitle, context, csvs.GetText("STR_SYS", lang), out, true, 0, mailConfig)
			}*/
		}
		for _, rankInfo := range v.DefenceSide.UnionActivityRank {
			GetUnionActivityMgr().SendRankAward(v.AttackSide.ServerId, rankInfo.Uid, rankInfo.RankPos)
			/*config := csvs.GetRankAwardConfig(models.ARENA_REWARD_TYPE_UNION_ACTIVITY, 1, rankInfo.RankPos, 0)
			if nil == config {
				continue
			}

			var out []models.PassItem

			itemLen := len(config.Items)
			if itemLen != len(config.Nums) {
				continue
			}

			for j := 0; j < itemLen; j++ {
				if config.Items[j] != 0 {
					out = append(out, models.PassItem{config.Items[j], config.Nums[j]})
				}
			}
			lang := GetOfflineInfoMgr().GetLang(rankInfo.Uid)
			mailConfig := csvs.GetMailConfig(config.MailId, lang)
			if mailConfig != nil {
				context := csvs.GetText(fmt.Sprintf(mailConfig.Mailtxt, v.DefenceSide.MasterName), lang)
				GetMailMgr().AddPlayerMail(rankInfo.Uid, mailConfig.Flag, 1, 1, 0,
					mailConfig.Mailtitle, context, csvs.GetText("STR_SYS", lang), out, true, 0, mailConfig)
			}*/
		}
		//发送胜负奖励
		//configAttack := csvs.GetResultAwardConfigMap(2, 1)
		unionAttack := GetUnionMgr().GetUnion(v.AttackSide.UnionId)
		if unionAttack != nil {
			if v.Result != model.ATTACK_WIN {
				//configAttack = csvs.GetResultAwardConfigMap(2, 4)
				unionAttack.UnionActivityLose++
			} else {
				unionAttack.UnionActivityWin++
			}
		}
		for _, canPlayUid := range v.AttackSide.CanPlayList {

			GetUnionActivityMgr().SendResultAward(unionAttack.ServerID, canPlayUid, v.Result)
			//mailTextAttack := ""
			/*lang := GetOfflineInfoMgr().GetLang(canPlayUid)
			mailConfigAttack := csvs.GetMailConfig(configAttack.MailId, lang)
			if mailConfigAttack == nil {
				continue
			}
			bestAttackPlayerName := ""
			if len(v.AttackSide.UnionActivityRank) > 0 {
				bestAttackPlayerName = v.AttackSide.UnionActivityRank[0].BaseInfoSimple.Name
			}
			mailTextAttack = fmt.Sprintf(mailConfigAttack.Mailtxt, v.AttackSide.MasterName, bestAttackPlayerName)
			var out []models.PassItem
			itemLen := len(configAttack.ResultAward)
			if itemLen != len(configAttack.AwardNum) {
				continue
			}

			for j := 0; j < itemLen; j++ {
				if configAttack.ResultAward[j] != 0 {
					out = append(out, models.PassItem{ItemID: configAttack.ResultAward[j], Num: configAttack.AwardNum[j]})
				}
			}
			GetMailMgr().AddPlayerMail(canPlayUid, mailConfigAttack.Flag, 1, 1, 0,
				mailConfigAttack.Mailtitle, mailTextAttack, csvs.GetText("STR_SYS", lang), out, true, 0, mailConfigAttack)
			*/
		}
		//configDefence := csvs.GetResultAwardConfigMap(2, 1)
		unionDefence := GetUnionMgr().GetUnion(v.AttackSide.UnionId)
		if unionDefence != nil {
			if v.Result != model.DEFENCE_WIN {
				//configDefence = csvs.GetResultAwardConfigMap(2, 4)
				unionDefence.UnionActivityLose++
			} else {
				unionDefence.UnionActivityWin++
			}
		}
		for _, canPlayUid := range v.DefenceSide.CanPlayList {
			/*if nil == configDefence {
				break
			}*/

			GetUnionActivityMgr().SendResultAward(unionAttack.ServerID, canPlayUid, v.Result)
			/*mailTextDefence := ""
			lang := GetOfflineInfoMgr().GetLang(canPlayUid)
			mailConfigDefence := csvs.GetMailConfig(configDefence.MailId, lang)
			if mailConfigDefence == nil {
				continue
			}
			bestDefencePlayerName := ""
			if len(v.DefenceSide.UnionActivityRank) > 0 {
				bestDefencePlayerName = v.DefenceSide.UnionActivityRank[0].BaseInfoSimple.Name
			}
			mailTextDefence = fmt.Sprintf(mailConfigDefence.Mailtxt, v.DefenceSide.MasterName, bestDefencePlayerName)
			var out []models.PassItem

			itemLen := len(configDefence.ResultAward)
			if itemLen != len(configDefence.AwardNum) {
				continue
			}

			for j := 0; j < itemLen; j++ {
				if configDefence.ResultAward[j] != 0 {
					out = append(out, models.PassItem{ItemID: configDefence.ResultAward[j], Num: configDefence.AwardNum[j]})
				}
			}
			GetMailMgr().AddPlayerMail(canPlayUid, mailConfigDefence.Flag, 1, 1, 0,
				mailConfigDefence.Mailtitle, mailTextDefence, csvs.GetText("STR_SYS", lang), out, true, 0, mailConfigDefence)*/
		}
		return true
	})
	self.Save()
}

func (self *UnionActivityInfo) GetUnionFight(unionid int) *UnionFight {
	teamIdItr, ok := self.UnionMap.Load(unionid)
	if !ok {
		return nil
	}

	unionFightItr, ok := self.UnionFight.Load(teamIdItr.(int))
	if !ok {
		return unionFightItr.(*UnionFight)
	}
	return nil
}

func (self *UnionActivityInfo) IsSameUnionFight(unionid1, unionid2 int) bool {
	var teamId1, teamId2 int
	if teamIdItr1, ok := self.UnionMap.Load(unionid1); !ok {
		return false
	} else {
		teamId1 = teamIdItr1.(int)
	}

	if teamIdItr2, ok := self.UnionMap.Load(unionid2); !ok {
		return false
	} else {
		teamId2 = teamIdItr2.(int)
	}

	if teamId1 != teamId2 {
		return false
	}

	if _, ok := self.UnionFight.Load(teamId1); !ok {
		return false
	}

	return true
}
