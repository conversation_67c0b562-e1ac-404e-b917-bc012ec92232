package crossserver

import (
	"fmt"
	json "github.com/bytedance/sonic"
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
	"sync"
	"time"
)

const (
	REDIS_TABLE_RECORD_NEWBOSS_LIST    = "record_newboss_list"
	REDIS_TABLE_RECORD_NEWBOSS_INFO    = "record_newboss_info"
	REDIS_TABLE_RECORD_NEWBOSS_RECORD  = "record_newboss_record"
	REDIS_TABLE_RECORD_NEWBOSS2_LIST   = "record_newboss2_list"
	REDIS_TABLE_RECORD_NEWBOSS2_INFO   = "record_newboss2_info"
	REDIS_TABLE_RECORD_NEWBOSS2_RECORD = "record_newboss2_record"
)

const (
	NEW_RECORD_MAX = 10
	NEW_NOTICE_MAX = 30
)

type RecordMgr struct {
	Locker         *sync.RWMutex
	NewBossRecord  map[int][]*model.BattleInfo
	NewBossRecord2 map[int][]*model.BattleInfo
	MaxKey         int64
	MaxKey2        int64
}

var recordMgr *RecordMgr = nil

func GetRecordMgr() *RecordMgr {
	if recordMgr == nil {
		recordMgr = new(RecordMgr)
		recordMgr.Locker = new(sync.RWMutex)
		recordMgr.NewBossRecord = make(map[int][]*model.BattleInfo)
		recordMgr.NewBossRecord2 = make(map[int][]*model.BattleInfo)
	}
	return recordMgr
}

func (self *RecordMgr) LoadData() {

}

func (self *RecordMgr) GetAllData() {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	self.GetNewBoss()
	self.GetNewBoss2()
}

func (self *RecordMgr) GetNewBoss() {
	res, err := db.GetAllRedis(REDIS_TABLE_RECORD_NEWBOSS_LIST)
	if res == nil {
		return
	}

	if err != nil {
		return
	}

	for _, v := range res {
		var data []*model.BattleInfo
		err = json.Unmarshal([]byte(v), &data)
		if err == nil && len(data) > 0 {
			self.NewBossRecord[data[0].LevelID] = data
		}
	}
}

func (self *RecordMgr) GetNewBoss2() {
	res, err := db.GetAllRedis(REDIS_TABLE_RECORD_NEWBOSS2_LIST)
	if res == nil {
		return
	}

	if err != nil {
		return
	}

	for _, v := range res {
		var data []*model.BattleInfo
		err = json.Unmarshal([]byte(v), &data)
		if err == nil && len(data) > 0 {
			self.NewBossRecord2[data[0].LevelID] = data
		}
	}
}

func (self *RecordMgr) UploadRecord(req *RPC_UploadRecordReq, res *RPC_UploadRecordRes) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	battleInfo := req.BattleInfo
	battleRecord := req.BattleRecord
	param := req.Param
	switch req.RecordType {
	case core.BATTLE_TYPE_RECORD_NEW_BOSS:
		self.MaxKey++
		newId := time.Now().Unix()*1000 + self.MaxKey%1000
		battleInfo.Id = newId
		battleRecord.Id = newId
		res.NewBattleId = newId
		if param == model.LOGIC_TRUE {
			if len(self.NewBossRecord[battleInfo.LevelID]) >= NEW_RECORD_MAX {
				self.NewBossRecord[battleInfo.LevelID] = self.NewBossRecord[battleInfo.LevelID][1:NEW_RECORD_MAX]
			}
			//给战报生成新的id
			self.NewBossRecord[battleInfo.LevelID] = append(self.NewBossRecord[battleInfo.LevelID], battleInfo)
			db.HMSetRedisEx(REDIS_TABLE_RECORD_NEWBOSS_LIST, int64(battleInfo.LevelID), self.NewBossRecord[battleInfo.LevelID], utils.DAY_SECS*7)
		}
		db.HMSetRedisEx(REDIS_TABLE_RECORD_NEWBOSS_INFO, battleInfo.Id, battleInfo, utils.DAY_SECS*7)
		db.HMSetRedisEx(REDIS_TABLE_RECORD_NEWBOSS_RECORD, battleRecord.Id, battleRecord, utils.DAY_SECS*7)
	case core.BATTLE_TYPE_RECORD_NEW_BOSS2:
		self.MaxKey2++
		newId := time.Now().Unix()*1000 + self.MaxKey2%1000
		battleInfo.Id = newId
		battleRecord.Id = newId
		res.NewBattleId = newId
		if param == model.LOGIC_TRUE {
			if len(self.NewBossRecord2[battleInfo.LevelID]) >= NEW_RECORD_MAX {
				self.NewBossRecord2[battleInfo.LevelID] = self.NewBossRecord2[battleInfo.LevelID][1:NEW_RECORD_MAX]
			}
			//给战报生成新的id
			self.NewBossRecord2[battleInfo.LevelID] = append(self.NewBossRecord2[battleInfo.LevelID], battleInfo)
			db.HMSetRedisEx(REDIS_TABLE_RECORD_NEWBOSS2_LIST, int64(battleInfo.LevelID), self.NewBossRecord2[battleInfo.LevelID], utils.DAY_SECS*7)
		}
		db.HMSetRedisEx(REDIS_TABLE_RECORD_NEWBOSS2_INFO, battleInfo.Id, battleInfo, utils.DAY_SECS*7)
		db.HMSetRedisEx(REDIS_TABLE_RECORD_NEWBOSS2_RECORD, battleRecord.Id, battleRecord, utils.DAY_SECS*7)
	}
}

func (self *RecordMgr) GetBattleInfo(id int64, battleType int) *model.BattleInfo {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	switch battleType {
	case core.BATTLE_TYPE_RECORD_NEW_BOSS:
		var battleInfo model.BattleInfo
		value, flag, err := db.HGetRedisEx(REDIS_TABLE_RECORD_NEWBOSS_INFO, id, fmt.Sprintf("%d", id))
		if err != nil || !flag {
			return nil
		}
		if flag {
			err := json.Unmarshal([]byte(value), &battleInfo)
			if err != nil {
				return &battleInfo
			}
		}

		if battleInfo.Id != 0 {
			return &battleInfo
		}
	case core.BATTLE_TYPE_RECORD_NEW_BOSS2:
		var battleInfo model.BattleInfo
		value, flag, err := db.HGetRedisEx(REDIS_TABLE_RECORD_NEWBOSS2_INFO, id, fmt.Sprintf("%d", id))
		if err != nil || !flag {
			return nil
		}
		if flag {
			err := json.Unmarshal([]byte(value), &battleInfo)
			if err != nil {
				return &battleInfo
			}
		}

		if battleInfo.Id != 0 {
			return &battleInfo
		}
	default:
		return nil
	}

	return nil
}

func (self *RecordMgr) GetBattleRecord(id int64, battleType int) *model.BattleRecord {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	switch battleType {
	case core.BATTLE_TYPE_RECORD_NEW_BOSS:
		var battleRecord model.BattleRecord
		value, flag, err := db.HGetRedisEx(REDIS_TABLE_RECORD_NEWBOSS_RECORD, id, fmt.Sprintf("%d", id))
		if err != nil || !flag {
			return nil
		}
		if flag {
			err := json.Unmarshal([]byte(value), &battleRecord)
			if err != nil {
				return &battleRecord
			}
		}
		if battleRecord.Id != 0 {
			return &battleRecord
		}
	case core.BATTLE_TYPE_RECORD_NEW_BOSS2:
		var battleRecord model.BattleRecord
		value, flag, err := db.HGetRedisEx(REDIS_TABLE_RECORD_NEWBOSS2_RECORD, id, fmt.Sprintf("%d", id))
		if err != nil || !flag {
			return nil
		}
		if flag {
			err := json.Unmarshal([]byte(value), &battleRecord)
			if err != nil {
				return &battleRecord
			}
		}
		if battleRecord.Id != 0 {
			return &battleRecord
		}
	default:
		return nil
	}

	return nil
}
