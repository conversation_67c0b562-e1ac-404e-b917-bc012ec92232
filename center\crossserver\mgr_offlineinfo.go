package crossserver

import (
	"fmt"
	json "github.com/bytedance/sonic"
	"master/core"
	"master/db"
	"master/utils"
	"sync"
)

const (
	TABLE_OFFLINEINFO = "tbl_offlineinfo"
)

type PlayerPublicInfo struct {
	LevelArenaScoreLvHistory int `json:"levelarenascorelvhistory"` // 段位赛历史成绩
	ArenaPeriod              int `json:"arenaperiod"`              //
	ArenaRank                int `json:"arenarank"`                // 顶上战争记录
	LevelAreanPeriod         int `json:"levelareanperiod"`         // 段位赛的期数
	LevelAreanRank           int `json:"levelareanrank"`           // 段位赛的排行
	PeakPeriod               int `json:"peakperiod"`               //
	PeakRank                 int `json:"peakrank"`                 // 顶上战争记录
	CosmicScoreLvHistory     int `json:"cosmicscorelvhistory"`     // 段位赛历史成绩
	CosmicPeriod             int `json:"cosmicperiod"`             // 段位赛的期数
	CosmicRank               int `json:"cosmicrank"`               // 段位赛的排行
}

// 离线信息管理
type OfflineInfo struct {
	Uid  int64
	Info string // 公共数据

	DataInfo *PlayerPublicInfo

	db.DataUpdate
}

// 将数据库数据写入dataf
func (self *OfflineInfo) Decode() {
	err2 := json.Unmarshal([]byte(self.Info), &self.DataInfo)
	if err2 != nil {
		utils.LogError("OfflineInfo Decode error:", err2.Error())
	}
}

func (self *OfflineInfo) Check() {
	if self.DataInfo == nil {
		self.DataInfo = new(PlayerPublicInfo)
	}
}

// 将data数据写入数据库
func (self *OfflineInfo) Encode() {
	self.Info = utils.HF_JtoA(&self.DataInfo)
}

// 竞技场管理器
type OfflineInfoMgr struct {
	OfflineInfo *sync.Map //
}

var offlineinfomgr *OfflineInfoMgr = nil

func GetOfflineInfoMgr() *OfflineInfoMgr {
	if offlineinfomgr == nil {
		offlineinfomgr = new(OfflineInfoMgr)
		offlineinfomgr.OfflineInfo = new(sync.Map)
	}
	return offlineinfomgr
}

func (self *OfflineInfoMgr) GetData() {
	core.GetMasterApp().StartWait()
	var info OfflineInfo
	tableName := TABLE_OFFLINEINFO
	sql := fmt.Sprintf("select * from `%s`", tableName)
	res := db.GetDBMgr().DBUser.GetAllData(sql, &info)
	for i := 0; i < len(res); i++ {
		data := res[i].(*OfflineInfo)
		data.Init(tableName, data, false)
		data.Decode()
		data.Check()
		self.OfflineInfo.Store(data.Uid, data)
	}
	core.GetMasterApp().StartDone()
}

func (self *OfflineInfoMgr) OnSave() {
	self.OfflineInfo.Range(func(key, data interface{}) bool {
		offlineInfo := data.(*OfflineInfo)
		offlineInfo.Encode()
		offlineInfo.Update(true, false)
		return true
	})
}

func (self *OfflineInfoMgr) GetInfo(uid int64) *OfflineInfo {

	data, ok := self.OfflineInfo.Load(uid)
	if !ok {
		newData := new(OfflineInfo)
		newData.Uid = uid
		newData.Check()
		newData.Encode()
		db.InsertTable(TABLE_OFFLINEINFO, newData, 0, false)
		newData.Init(TABLE_OFFLINEINFO, newData, false)
		self.OfflineInfo.Store(newData.Uid, newData)
		data, ok = self.OfflineInfo.Load(uid)
		if !ok {
			return nil
		}
	}
	return data.(*OfflineInfo)
}
func (self *OfflineInfoMgr) SetLevelArenaScoreLvHistory(uid int64, value int) {
	data := self.GetInfo(uid)
	if data == nil {
		return
	}
	if data.DataInfo == nil {
		data.DataInfo = new(PlayerPublicInfo)
	}
	data.DataInfo.LevelArenaScoreLvHistory = value
	return
}

func (self *OfflineInfoMgr) GetLevelArenaScoreLvHistory(uid int64) int {
	data := self.GetInfo(uid)
	if data == nil {
		return 0
	}
	if data.DataInfo == nil {
		data.DataInfo = new(PlayerPublicInfo)
	}
	return data.DataInfo.LevelArenaScoreLvHistory
}
func (self *OfflineInfoMgr) SetCosmicScoreLvHistory(uid int64, value int) {
	data := self.GetInfo(uid)
	if data == nil {
		return
	}
	if data.DataInfo == nil {
		data.DataInfo = new(PlayerPublicInfo)
	}
	data.DataInfo.CosmicScoreLvHistory = value
	return
}

func (self *OfflineInfoMgr) GetCosmicScoreLvHistory(uid int64) int {
	data := self.GetInfo(uid)
	if data == nil {
		return 0
	}
	if data.DataInfo == nil {
		data.DataInfo = new(PlayerPublicInfo)
	}
	return data.DataInfo.CosmicScoreLvHistory
}
func (self *OfflineInfoMgr) SetArenaRank(uid int64, period int, value int) {
	data := self.GetInfo(uid)
	if data == nil {
		return
	}
	if data.DataInfo == nil {
		data.DataInfo = new(PlayerPublicInfo)
	}
	data.DataInfo.ArenaPeriod = period
	data.DataInfo.ArenaRank = value
	return
}

func (self *OfflineInfoMgr) SetPeakRank(uid int64, period int, value int) {
	data := self.GetInfo(uid)
	if data == nil {
		return
	}
	if data.DataInfo == nil {
		data.DataInfo = new(PlayerPublicInfo)
	}
	data.DataInfo.PeakPeriod = period
	data.DataInfo.PeakRank = value
	return
}

func (self *OfflineInfoMgr) GetArenaRank(period int, uid int64) int {
	data := self.GetInfo(uid)
	if data == nil {
		return 0
	}
	if data.DataInfo == nil {
		data.DataInfo = new(PlayerPublicInfo)
	}
	if data.DataInfo.ArenaPeriod == period {
		return data.DataInfo.ArenaRank
	}
	return 0
}

func (self *OfflineInfoMgr) GetPeakRank(period int, uid int64) int {
	data := self.GetInfo(uid)
	if data == nil {
		return 0
	}
	if data.DataInfo == nil {
		data.DataInfo = new(PlayerPublicInfo)
	}
	if data.DataInfo.PeakPeriod == period {
		return data.DataInfo.PeakRank
	}
	return 0
}

func (self *OfflineInfoMgr) SetLevelAreanRank(uid int64, period int, value int) {
	data := self.GetInfo(uid)
	if data == nil {
		return
	}
	if data.DataInfo == nil {
		data.DataInfo = new(PlayerPublicInfo)
	}
	data.DataInfo.LevelAreanPeriod = period
	data.DataInfo.LevelAreanRank = value
	return
}

func (self *OfflineInfoMgr) GetLevelAreanRank(uid int64, period int) int {
	data := self.GetInfo(uid)
	if data == nil {
		return 0
	}
	if data.DataInfo == nil {
		data.DataInfo = new(PlayerPublicInfo)
	}
	if data.DataInfo.LevelAreanPeriod == period {
		return data.DataInfo.LevelAreanRank
	}
	return 0
}
func (self *OfflineInfoMgr) SetCosmicRank(uid int64, period int, value int) {
	data := self.GetInfo(uid)
	if data == nil {
		return
	}
	if data.DataInfo == nil {
		data.DataInfo = new(PlayerPublicInfo)
	}
	data.DataInfo.CosmicPeriod = period
	data.DataInfo.CosmicRank = value
	return
}

func (self *OfflineInfoMgr) GetCosmicRank(uid int64, period int) int {
	data := self.GetInfo(uid)
	if data == nil {
		return 0
	}
	if data.DataInfo == nil {
		data.DataInfo = new(PlayerPublicInfo)
	}
	if data.DataInfo.CosmicPeriod == period {
		return data.DataInfo.CosmicRank
	}
	return 0
}
