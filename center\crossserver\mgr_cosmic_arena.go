package crossserver

import (
	"fmt"
	json "github.com/bytedance/sonic"
	"log"
	"master/center/battle"
	"master/center/conf"
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
	"net/http"
	"runtime/debug"
	"sort"
	"sync"
	"time"
)

const (
	TABLE_COSMICARENA             = "tbl_cosmicarena"
	COSMICARENA_SCORE_START       = 1000
	COSMICARENA_SECONDKILL_PARAM  = 20000
	COSMICARENA_RANK_MAX          = 100 //
	COSMICARENA_ENEMY_CHOOSE_LIST = 10  //对手备选数量，提高效率
	COSMICARENA_SCORELV_FIND      = 12  //大于这个段位，采取扩大搜索
)

const (
	COSMIC_ARENA_CAMP_1 = 1
	COSMIC_ARENA_CAMP_2 = 2
)

type lstCosmicArenaRank []*CosmicArenaUser

func (s lstCosmicArenaRank) Len() int      { return len(s) }
func (s lstCosmicArenaRank) Swap(i, j int) { s[i], s[j] = s[j], s[i] }
func (s lstCosmicArenaRank) Less(i, j int) bool {
	if s[i].IsSign == s[j].IsSign {
		if s[i].Score == s[j].Score {
			return s[i].Uid < s[j].Uid
		} else {
			return s[i].Score >= s[j].Score
		}
	}
	return s[i].IsSign >= s[j].IsSign
}

type CosmicArenaDB struct {
	Id         int    `json:"id"`
	Uid        int64  `json:"uid"`
	Periods    int    `json:"periods"`
	GroupId    int    `json:"groupid"`
	SvrId      int    `json:"svrid"`
	Info       string `json:"info"`
	FightInfos string `json:"fightinfos"`

	info       *CosmicArenaUser
	fightInfos *model.JS_FightInfo
	db.DataUpdate
}

type CosmicArenaUser struct {
	Uid             int64                     `json:"uid"`
	SvrId           int                       `json:"svrid"`
	SvrName         string                    `json:"svrname"`
	UName           string                    `json:"uname"`
	UnionName       string                    `json:"unionname"`
	Score           int                       `json:"score"`
	ScoreLv         int                       `json:"scorelv"`
	Camp            int                       `json:"camp"`
	ScoreLvMax      int                       `json:"scorelvmax"`
	RankPos         int                       `json:"rankpos"`
	Level           int                       `json:"level"`
	Vip             int                       `json:"vip"`
	Icon            int                       `json:"icon"`
	Portrait        int                       `json:"portrait"`
	Title           int                       `json:"title"`
	Fight           int64                     `json:"fight"`
	Robot           int                       `json:"robot"`
	LevelArenaFight []*model.CosmicArenaFight `json:"arenafight"` //战报集
	CantList        map[int64]int             `json:"-"`
	IsSign          int                       `json:"issign"`   //是否正式参赛
	AllTimes        int                       `json:"alltimes"` //总场
	WinTimes        int                       `json:"wintimes"` //胜场
	HeroId          int                       `json:"heroid"`   //! 给客户端显示半身像
}

type CosmicArenaEnd struct {
	UserInfo     *CosmicArenaUser    `json:"userinfo"`
	BattleRecord *model.BattleRecord `json:"battlerecord"`
	Record       []byte              `json:"record"`
	AllScore     []int               `json:"all_score"`
}

// 跨服段位赛 跨服 阵营对决
type CosmicArenaMgr struct {
	CosmicArenaInfo   *sync.Map                       //key: group CosmicArenaInfo
	JJCRobotConfigMap map[int]map[int]*JJCRobotConfig //key:  段位scorelv
	RecordId          int64
	Config            CosmicArenaConfig
	BattleListen      *sync.Map
	BattleListenRoll  *sync.Map
	Mu                *sync.RWMutex //sync.map 不能规避逻辑上的互斥
}

type CosmicArenaConfig struct {
	ScoreStart      int //50 玩家初次进入需求积分
	SecondKillParam int //51 碾压战力相差万分比系数
}

type CosmicArenaInfo struct {
	groupId      int
	periods      int
	AllScore     []int
	rankInfo     []*CosmicArenaUser       //排行数据    key:rank
	db_list      map[int64]*CosmicArenaDB //数据存储
	Locker       *sync.RWMutex
	rankInfoShow []*CosmicArenaUser //给玩家看的排行
}

var cosmicArenaMgr *CosmicArenaMgr = nil

func GetCosmicArenaMgr() *CosmicArenaMgr {
	if cosmicArenaMgr == nil {
		cosmicArenaMgr = new(CosmicArenaMgr)
		cosmicArenaMgr.CosmicArenaInfo = new(sync.Map)
		cosmicArenaMgr.BattleListen = new(sync.Map)
		cosmicArenaMgr.BattleListenRoll = new(sync.Map)
		cosmicArenaMgr.Mu = new(sync.RWMutex)
		cosmicArenaMgr.LoadCsv()
	}
	return cosmicArenaMgr
}

func CosmicGetRollFight(fightInfos *model.JS_FightInfo) int64 {
	count := 0
	fightAll := int64(0)
	for _, v := range fightInfos.Heroinfo {
		if v.Heroid == 0 {
			continue
		}
		count += 1
		fightAll += v.Fight
	}
	if count == 0 {
		return 0
	}
	return fightAll / int64(count)
}

func GetRankInfoCosmicArena(data *CosmicArenaUser) *model.RankInfo {
	rel := new(model.RankInfo)
	if data == nil {
		return rel
	}
	rel.Uid = data.Uid
	rel.SvrId = data.SvrId
	rel.UName = data.UName
	rel.Level = data.Level
	rel.Vip = data.Vip
	rel.Icon = data.Icon
	rel.Portrait = data.Portrait
	rel.Title = data.Title
	rel.Fight = data.Fight
	rel.Num = int64(data.Score)
	rel.UnionName = data.UnionName
	rel.Param = int64(data.ScoreLv)
	rel.HeroId = data.HeroId
	rel.Camp = data.Camp
	//if data.IsSign == core.LOGIC_TRUE {
	rel.Rank = data.RankPos
	//}
	return rel
}

func (self *CosmicArenaMgr) Run() {
	defer func() {
		x := recover()
		if x != nil {
			log.Println(x, string(debug.Stack()))
			utils.LogError(x, string(debug.Stack()))
		}
	}()

	ticker := time.NewTicker(time.Second * 1)
	for {
		select {
		case <-ticker.C:
			self.CheckBattleFinish()
			//先用实时算法
			//if core.TimeServer().Unix()%30 == 0 {
			//	self.SortRank()
			//}
		}
	}
	ticker.Stop()
}

func (self *CosmicArenaInfo) GetRobotUser(config *JJCRobotConfig) *CosmicArenaUser {
	data := new(CosmicArenaUser)
	data.Uid = int64(config.Jjcscore)
	return data
}

func (self *CosmicArenaMgr) LoadCsv() {
	self.JJCRobotConfigMap = make(map[int]map[int]*JJCRobotConfig, 0)
	JJCRobotConfigTemp := make([]*JJCRobotConfig, 0)
	utils.GetCsvUtilMgr().LoadCsv("Jjc_Robot", &JJCRobotConfigTemp)
	for _, v := range JJCRobotConfigTemp {
		if v.Type != 6 {
			continue
		}
		_, ok := self.JJCRobotConfigMap[v.Jjcdan]
		if !ok {
			self.JJCRobotConfigMap[v.Jjcdan] = make(map[int]*JJCRobotConfig)
		}
		self.JJCRobotConfigMap[v.Jjcdan][v.Id] = v
	}

	configNum := 0
	//50 玩家初次进入需求积分
	self.Config.ScoreStart = COSMICARENA_SCORE_START
	configNum = conf.GetInitNum(50)
	if configNum > 0 {
		self.Config.ScoreStart = configNum
		configNum = 0
	}
	//51 碾压战力相差万分比系数
	self.Config.SecondKillParam = COSMICARENA_SECONDKILL_PARAM
	configNum = conf.GetInitNum(51)
	if configNum > 0 {
		self.Config.SecondKillParam = configNum
		configNum = 0
	}
	return
}

func (self *CosmicArenaDB) Encode() {
	self.Info = utils.HF_JtoA(self.info)
	self.FightInfos = utils.HF_JtoA(self.fightInfos)
}

func (self *CosmicArenaDB) Decode() {
	json.Unmarshal([]byte(self.Info), &self.info)
	json.Unmarshal([]byte(self.FightInfos), &self.fightInfos)
}

// 存储数据库
func (self *CosmicArenaMgr) OnSave() {
	self.CosmicArenaInfo.Range(func(key, info interface{}) bool {
		if info == nil {
			return true
		}

		serverGroup := info.(*CosmicArenaInfo)
		serverGroup.Locker.RLock()
		for _, user := range serverGroup.db_list {
			index := user.info.RankPos - 1
			if index < 0 {
				continue
			}
			user.info = serverGroup.rankInfo[user.info.RankPos-1]
			user.Encode()
			user.UpdateEx("periods", user.Periods)
		}
		serverGroup.Locker.RUnlock()
		return true
	})
}

func (self *CosmicArenaMgr) GetAllData() {
	periods := GetServerGroupMgr().GetCosmicArenaPeriods()
	serverGroupMap := GetServerGroupMgr().GetServerZoneMap()

	queryStr := fmt.Sprintf("select * from `%s` where periods = %d;", TABLE_COSMICARENA, periods)
	var msg CosmicArenaDB
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &msg)

	resCorrect := make(map[int64]*CosmicArenaDB)
	for i := 0; i < len(res); i++ {
		data := res[i].(*CosmicArenaDB)
		//看看是否匹配分组
		groupId, groupOk := serverGroupMap[data.SvrId]
		if groupOk && groupId != data.GroupId {
			continue
		}
		data.Decode()
		if data.info == nil {
			continue
		}
		oldInfo, ok := resCorrect[data.Uid]
		if ok {
			if oldInfo.info.Score > data.info.Score {
				continue
			}
		}
		resCorrect[data.Uid] = data
	}

	for _, data := range resCorrect {

		info, ok := self.CosmicArenaInfo.Load(data.GroupId)
		if !ok {
			info = self.NewCosmicArenaInfo(data.GroupId, periods)
			self.CosmicArenaInfo.Store(data.GroupId, info)
		}
		infoData := info.(*CosmicArenaInfo)
		if data.info == nil {
			data.info = new(CosmicArenaUser)
			data.info.Uid = data.Uid
			data.info.SetScore(self.Config.ScoreStart)
			switch data.info.Camp {
			case COSMIC_ARENA_CAMP_1:
				infoData.AllScore[0] += data.info.Score
			case COSMIC_ARENA_CAMP_2:
				infoData.AllScore[1] += data.info.Score
			}

			continue
		}

		data.Init(TABLE_COSMICARENA, data, false)
		infoData.db_list[data.Uid] = data
		switch data.info.Camp {
		case COSMIC_ARENA_CAMP_1:
			infoData.AllScore[0] += data.info.Score
		case COSMIC_ARENA_CAMP_2:
			infoData.AllScore[1] += data.info.Score
		}
	}
	self.CosmicArenaInfo.Range(func(key, info interface{}) bool {
		if info == nil {
			return true
		}

		serverGroup := info.(*CosmicArenaInfo)
		serverGroup.InitRank()
		return true
	})
}
func (self *CosmicArenaUser) SetScore(score int) {
	self.Score = score
	if self.ScoreLv == 0 {
		self.ScoreLv = conf.CalCosmicScoreLv(score, 0)
	}
}
func (self *CosmicArenaInfo) InitRank() {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	self.rankInfo = make([]*CosmicArenaUser, 0)
	for _, v := range self.db_list {
		self.rankInfo = append(self.rankInfo, v.info)
	}
	sort.Sort(lstCosmicArenaRank(self.rankInfo))
	for index, v := range self.rankInfo {
		v.RankPos = index + 1
		v.ScoreLv = conf.CalCosmicScoreLv(v.Score, v.RankPos)
	}
	return
}
func (self *CosmicArenaMgr) NewCosmicArenaInfo(groupId int, periods int) *CosmicArenaInfo {
	data := new(CosmicArenaInfo)
	data.groupId = groupId
	data.periods = periods
	data.rankInfo = make([]*CosmicArenaUser, 0)
	data.AllScore = make([]int, COSMIC_ARENA_CAMP_2)
	data.db_list = make(map[int64]*CosmicArenaDB)
	data.Locker = new(sync.RWMutex)
	return data
}
func (self *CosmicArenaMgr) GetCosmicArenaInfo(serverId int) *CosmicArenaInfo {
	self.Mu.Lock()
	defer self.Mu.Unlock()
	//获得该服务器分组
	groupId := GetServerGroupMgr().GetZoneId(serverId)
	periods := GetServerGroupMgr().GetCosmicArenaPeriods()

	info, ok := self.CosmicArenaInfo.Load(groupId)
	if !ok {
		info = self.NewCosmicArenaInfo(groupId, periods)
		self.CosmicArenaInfo.Store(groupId, info)
	}

	return info.(*CosmicArenaInfo)
}
func (self *CosmicArenaMgr) GetCosmicArenaInfoByGroupId(groupId int) *CosmicArenaInfo {
	info, ok := self.CosmicArenaInfo.Load(groupId)
	if !ok {
		return nil
	}

	return info.(*CosmicArenaInfo)
}
func (self *CosmicArenaMgr) GetGroupConfig() string {
	//config := GetServerGroupMgr().GetLevelArenaConfig()
	groupConfig := GetServerGroupMgr().GetZoneConfig()
	return utils.HF_JtoA(groupConfig)
}
func (self *CosmicArenaMgr) GetConfig(serverId int) (string, string, string, string, string) {
	config := GetServerGroupMgr().GetPeakConfig()
	groupConfig := GetServerGroupMgr().GetZoneConfig()
	groups := GetServerGroupMgr().GetZones(serverId)
	cosmicArenaConfig := GetServerGroupMgr().GetCosmicArenaConfig()
	score := ""
	cosmicArenaInfo := self.GetCosmicArenaInfo(serverId)
	if cosmicArenaInfo != nil {
		score = utils.HF_JtoA(cosmicArenaInfo.AllScore)
	}

	return utils.HF_JtoA(config), utils.HF_JtoA(groupConfig), utils.HF_JtoA(groups), utils.HF_JtoA(cosmicArenaConfig), score
}
func (self *CosmicArenaMgr) GetInfo(uid int64, serverId int, fightInfos *model.JS_FightInfo, camp int) (int, string) {
	if fightInfos == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	cosmicArenaInfo := self.GetCosmicArenaInfo(serverId)
	if cosmicArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	info := cosmicArenaInfo.GetInfo(uid, fightInfos, camp)
	infoStr := ""
	if info != nil {
		infoStr = utils.HF_JtoA(info)
	}
	return RETCODE_DATA_CROSS_OK, infoStr
}

func (self *CosmicArenaMgr) FightStart(uid int64, serverId int, fightInfos *model.JS_FightInfo, camp int) (int, string, string) {
	if fightInfos == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	cosmicArenaInfo := self.GetCosmicArenaInfo(serverId)
	if cosmicArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	selfInfo := cosmicArenaInfo.GetInfoDB(uid, fightInfos, camp)
	if selfInfo == nil || selfInfo.fightInfos == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	//寻找对手并加入战斗服计算
	_, enemyFightInfo := cosmicArenaInfo.GetEnemy(uid)
	//检查对手是机器人的话
	if enemyFightInfo == nil || enemyFightInfo.Uid == 0 {
		enemyFightInfo = self.GetRobotFightInfo(selfInfo.info.ScoreLv)
		enemyFightInfo.Param = int64(selfInfo.info.ScoreLv)
	}
	//如果触发碾压 则不进入战斗服
	isRolling := true
	attackFight := CosmicGetRollFight(selfInfo.fightInfos)
	defenceFight := CosmicGetRollFight(enemyFightInfo)
	defenceFight = defenceFight * int64(self.Config.SecondKillParam) / 10000
	if attackFight < defenceFight {
		isRolling = false
	}
	//添加战斗给战斗服
	random := model.TimeServer().Unix()
	if isRolling == true {
		//ret := battle.GetFightMgr().AddFightID(selfInfo.fightInfos, enemyFightInfo, int(random), 0, 0)
		battleData := &model.ArenaFightList{
			Type:    0,
			FightId: battle.GetFightMgr().GetFightInfoID(),
			Random:  random,
			Time:    model.TimeServer().Unix(),
			Attack:  selfInfo.fightInfos,
			Defend:  enemyFightInfo,
			BossId:  0}
		//用回调函数去处理
		self.BattleListenRoll.Store(battleData.FightId, battleData)
	} else {
		ret := battle.GetFightMgr().AddFightID(selfInfo.fightInfos, enemyFightInfo, int(random), 0, 0, core.BATTLE_TYPE_COSMICARENA, 0)
		battleData := &model.ArenaFightList{
			Type:    0,
			FightId: ret,
			Random:  random,
			Time:    model.TimeServer().Unix(),
			Attack:  selfInfo.fightInfos,
			Defend:  enemyFightInfo,
			BossId:  0}
		self.BattleListen.Store(battleData.FightId, battleData)
	}
	//获取随机人物给客户端做演示
	matchInfo := cosmicArenaInfo.GetMatchShow(uid)
	matchInfoStr := ""
	if len(matchInfo) > 0 {
		matchInfoStr = utils.HF_JtoA(matchInfo)
	}
	selfInfoStr := utils.HF_JtoA(selfInfo.info)
	return RETCODE_DATA_CROSS_OK, matchInfoStr, selfInfoStr
}
func (self *CosmicArenaInfo) GetInfoDB(uid int64, fightInfos *model.JS_FightInfo, camp int) *CosmicArenaDB {
	if fightInfos == nil {
		return nil
	}
	self.Locker.Lock()
	defer self.Locker.Unlock()
	infoDB, ok := self.db_list[uid]
	if !ok {
		infoDB = new(CosmicArenaDB)
		infoDB.Uid = uid
		infoDB.Periods = self.periods
		infoDB.GroupId = self.groupId
		infoDB.SvrId = fightInfos.Server
		infoDB.fightInfos = fightInfos
		infoDB.info = self.NewInfo(fightInfos, camp)
		switch infoDB.info.Camp {
		case COSMIC_ARENA_CAMP_1:
			self.AllScore[0] += infoDB.info.Score
		case COSMIC_ARENA_CAMP_2:
			self.AllScore[1] += infoDB.info.Score
		}
		infoDB.Encode()
		id := db.InsertTable(TABLE_COSMICARENA, infoDB, 0, false)
		infoDB.Id = int(id)
		infoDB.Init(TABLE_COSMICARENA, infoDB, false)
		infoDB.info.RankPos = len(self.rankInfo) + 1
		self.rankInfo = append(self.rankInfo, infoDB.info)
		self.db_list[uid] = infoDB
	}
	infoDB.SetFightInfo(fightInfos)
	return infoDB
}
func (self *CosmicArenaInfo) GetInfo(uid int64, fightInfos *model.JS_FightInfo, camp int) *CosmicArenaUser {
	infoDB := self.GetInfoDB(uid, fightInfos, camp)
	if infoDB == nil {
		return nil
	}
	infoDB.SetFightInfo(fightInfos)
	if infoDB.info != nil && fightInfos != nil {
		infoDB.info.Fight = 0
		infoDB.info.UName = infoDB.fightInfos.Uname
		infoDB.info.Icon = infoDB.fightInfos.Iconid
		infoDB.info.Portrait = infoDB.fightInfos.Portrait
		infoDB.info.Title = infoDB.fightInfos.Title
		infoDB.info.Level = infoDB.fightInfos.Level
		realValue := infoDB.fightInfos.Deffight / 100
		infoDB.info.Fight += realValue * 100
		if len(infoDB.fightInfos.Heroinfo) > 0 {
			fight := int64(0)
			heroid := 0
			for _, value := range infoDB.fightInfos.Heroinfo {
				if fight == 0 {
					fight = value.Fight
					heroid = value.Heroid
				} else {
					if value.Fight > fight {
						fight = value.Fight
						heroid = value.Heroid
					}
				}
			}
			infoDB.info.HeroId = heroid
		}
	}
	return infoDB.info
}

func (self *CosmicArenaInfo) GetDanInitScore(oldScoreLv int) int {
	score := GetCosmicArenaMgr().Config.ScoreStart
	config := conf.GetCosmicDanConfig(oldScoreLv)
	if config != nil {
		score = config.DanScore
	}
	return score
}
func (self *CosmicArenaInfo) NewInfo(fightInfo *model.JS_FightInfo, camp int) *CosmicArenaUser {
	data := new(CosmicArenaUser)
	data.Uid = fightInfo.Uid
	data.SvrId = fightInfo.Server
	data.UName = fightInfo.Uname
	data.UnionName = fightInfo.UnionName
	data.Camp = camp
	//oldScoreLv := GetOfflineInfoMgr().GetLevelArenaScoreLvHistory(data.Uid)
	//score := self.GetDanInitScore(oldScoreLv)
	score := self.GetDanInitScore(0)
	data.SetScore(score)
	data.Level = fightInfo.Level
	data.Vip = fightInfo.Vip
	data.Icon = fightInfo.Iconid
	if len(fightInfo.Heroinfo) > 0 {
		fight := int64(0)
		heroid := 0
		for _, value := range fightInfo.Heroinfo {
			if fight == 0 {
				fight = value.Fight
				heroid = value.Heroid
			} else {
				if value.Fight > fight {
					fight = value.Fight
					heroid = value.Heroid
				}
			}
		}
		data.HeroId = heroid
	}
	data.Portrait = fightInfo.Portrait
	data.Title = fightInfo.Title
	realValue := fightInfo.Deffight / 100
	data.Fight += realValue * 100
	return data
}

func (self *CosmicArenaInfo) GetEnemy(uid int64) (*CosmicArenaUser, *model.JS_FightInfo) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	relUser := new(CosmicArenaUser)
	relFight := new(model.JS_FightInfo)
	info, ok := self.db_list[uid]
	if !ok {
		return nil, nil
	}
	if info.info == nil {
		return nil, nil
	}
	index := info.info.RankPos - 1
	if index < 0 || index >= len(self.rankInfo) {
		return nil, nil
	}
	//先把自己2个档位差的人全部取出来，作为备选列表
	score := self.rankInfo[index].Score
	scoreLv := self.rankInfo[index].ScoreLv
	scoreLvMap := make(map[int]map[int64]int)

	for i := index - 1; i >= 0; i-- {
		//if self.rankInfo[i].IsSign == core.LOGIC_FALSE {
		//	continue
		//}
		nowScoreLv := self.rankInfo[i].ScoreLv
		if self.IsStopFind(scoreLv, nowScoreLv-scoreLv) {
			break
		}
		_, ok := scoreLvMap[nowScoreLv]
		if !ok {
			scoreLvMap[nowScoreLv] = make(map[int64]int)
		}
		if len(scoreLvMap[nowScoreLv]) >= COSMICARENA_ENEMY_CHOOSE_LIST {
			continue
		}
		scoreLvMap[nowScoreLv][self.rankInfo[i].Uid] = model.LOGIC_TRUE
	}
	for i := index + 1; i < len(self.rankInfo); i++ {
		//if self.rankInfo[i].IsSign == core.LOGIC_FALSE {
		//	continue
		//}
		nowScoreLv := self.rankInfo[i].ScoreLv
		if self.IsStopFind(scoreLv, scoreLv-nowScoreLv) {
			break
		}
		_, ok := scoreLvMap[nowScoreLv]
		if !ok {
			scoreLvMap[nowScoreLv] = make(map[int64]int)
		}
		if len(scoreLvMap[nowScoreLv]) >= COSMICARENA_ENEMY_CHOOSE_LIST {
			continue
		}
		scoreLvMap[nowScoreLv][self.rankInfo[i].Uid] = model.LOGIC_TRUE
	}
	cantList := make(map[int64]int)
	for k := range self.rankInfo[index].CantList {
		cantList[k] = model.LOGIC_TRUE
	}
	cantList[uid] = model.LOGIC_TRUE

	enemy, cantList := self.GetFirstEnemy(scoreLvMap, cantList, scoreLv, score)
	relUser = enemy
	for k := range self.rankInfo[index].CantList {
		delete(cantList, k)
	}
	self.rankInfo[index].CantList = cantList

	enemyInfo, ok := self.db_list[relUser.Uid]
	if ok {
		relFight = enemyInfo.fightInfos
	} else {

	}

	return relUser, relFight
}

// 根据当前段位，和目标段位差，判断是否要停止寻找
func (self *CosmicArenaInfo) IsStopFind(nowScoreLv, disScoreLv int) bool {
	//这个段位之上的人一定是扩大搜索
	if nowScoreLv >= COSMICARENA_SCORELV_FIND && disScoreLv > 2 {
		return true
	}
	if nowScoreLv < COSMICARENA_SCORELV_FIND && disScoreLv > 0 {
		return true
	}
	return false
}

func (self *CosmicArenaInfo) GetFirstEnemy(scoreLvMap map[int]map[int64]int, cantList map[int64]int, scoreLv int, score int) (*CosmicArenaUser, map[int64]int) {

	for k := range scoreLvMap[scoreLv] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv+1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv+2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	//12以上段位一定要找个真人
	if scoreLv >= COSMICARENA_SCORELV_FIND {
		for _, v := range self.rankInfo {
			k := v.Uid
			_, ok := cantList[k]
			if ok {
				continue
			}
			cantList[k] = model.LOGIC_TRUE
			return self.db_list[k].info, cantList
		}
	}
	//前面全部没有选中，开始补机器人
	robotInfo := new(CosmicArenaUser)
	robotInfo.Score = score
	return robotInfo, cantList
}

// 1. 低一个档位
// 2. 低两个档位
// 3. 当前档位
// 4. 补机器人
func (self *CosmicArenaInfo) GetThirdEnemy(scoreLvMap map[int]map[int64]int, cantList map[int64]int, scoreLv int, score int) (*CosmicArenaUser, map[int64]int) {
	for k := range scoreLvMap[scoreLv-1] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv-2] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	for k := range scoreLvMap[scoreLv] {
		_, ok := cantList[k]
		if ok {
			continue
		}
		cantList[k] = model.LOGIC_TRUE
		return self.db_list[k].info, cantList
	}
	//前面全部没有选中，开始补机器人
	robotInfo := new(CosmicArenaUser)
	robotInfo.Score = score
	return robotInfo, cantList
}

func (self *CosmicArenaInfo) CalAttackEnd(attack *model.JS_FightInfo, defend *model.JS_FightInfo,
	battleInfo *model.BattleInfo, record []byte) {

	self.Locker.Lock()
	defer self.Locker.Unlock()
	attackStartScore := 1000 //初始积分
	attackScore := attackStartScore
	defenceStartScore := 1000 //初始积分
	defenceScore := defenceStartScore
	scoreLv := 1
	attackPoint := 0    //变化的积分
	defencePoint := 0   //变化的积分
	attackTimes := 0    //
	attackWinTimes := 0 //
	attackUid := int64(0)
	if attack.Uid > 0 {
		attackUid = attack.Uid
		attackInfo, ok := self.db_list[attackUid]
		if ok && attackInfo.info != nil {
			attackStartScore = attackInfo.info.Score
			attackInfo.info.IsSign = model.LOGIC_TRUE
			scoreLv = attackInfo.info.ScoreLv
		}
	}
	//防守方需要排除机器人
	defendUid := int64(0)
	if defend.Uid > 0 {
		defendUid = defend.Uid
		defendInfo, ok := self.db_list[defendUid]
		if ok && defendInfo.info != nil {
			defenceStartScore = defendInfo.info.Score
		}
	}
	config := conf.GetCosmicDanConfig(scoreLv)
	if config == nil {
		return
	}
	attackTimes += 1
	if battleInfo.Result == model.ATTACK_WIN {
		attackWinTimes += 1
		attackPoint = int(conf.CountAddPoint(true, config, int64(attackStartScore), int64(defenceStartScore)))
		defencePoint = int(conf.CountMinPoint(true, config, int64(attackStartScore), int64(defenceStartScore)))
	} else {
		attackPoint = int(conf.CountMinPoint(false, config, int64(attackStartScore), int64(defenceStartScore)))
		defencePoint = int(conf.CountAddPoint(false, config, int64(attackStartScore), int64(defenceStartScore)))
	}
	if attackUid > 0 {
		attackInfo, ok := self.db_list[attackUid]
		if !ok || attackInfo.info == nil {
			return
		}
		attackInfo.info.Score += attackPoint
		if attackInfo.info.Score < 0 {
			attackInfo.info.Score = 0
		}
		switch attackInfo.info.Camp {
		case COSMIC_ARENA_CAMP_1:
			self.AllScore[0] += attackPoint
			if self.AllScore[0] < 0 {
				self.AllScore[0] = 0
			}
		case COSMIC_ARENA_CAMP_2:
			self.AllScore[1] += attackPoint
			if self.AllScore[1] < 0 {
				self.AllScore[1] = 0
			}
		}

		attackScore = attackInfo.info.Score
		attackInfo.info.AllTimes += attackTimes
		attackInfo.info.WinTimes += attackWinTimes
	}
	if defendUid > 0 {
		defendInfo, ok := self.db_list[defendUid]
		if ok && defendInfo.info != nil && defendInfo.info.IsSign == model.LOGIC_TRUE {
			defendInfo.info.Score += defencePoint
			if defendInfo.info.Score < 0 {
				defendInfo.info.Score = 0
			}
			switch defendInfo.info.Camp {
			case COSMIC_ARENA_CAMP_1:
				self.AllScore[0] += defencePoint
				if self.AllScore[0] < 0 {
					self.AllScore[0] = 0
				}
			case COSMIC_ARENA_CAMP_2:
				self.AllScore[1] += defencePoint
				if self.AllScore[1] < 0 {
					self.AllScore[1] = 0
				}
			}

			if defendInfo.info.Score+defencePoint < 0 {
				defenceScore = 0
			} else {
				defenceScore = defendInfo.info.Score + defencePoint
			}
		}
	} else {
		defenceScore += defencePoint
	}

	self.SortRank()

	if battleInfo.UserInfo[0] != nil {
		battleInfo.UserInfo[0].Score = attackScore
		battleInfo.UserInfo[0].Param1 = attackPoint
	}
	if battleInfo.UserInfo[1] != nil {
		battleInfo.UserInfo[1].Score = defenceScore
		battleInfo.UserInfo[1].Param1 = defencePoint
	}

	battleRecord := model.BattleRecord{}
	battleRecord.Id = battleInfo.Id
	battleRecord.LevelID = battleInfo.LevelID
	battleRecord.Result = battleInfo.Result
	battleRecord.RandNum = battleInfo.Random
	battleRecord.FightInfo[0] = attack
	battleRecord.FightInfo[1] = defend
	db.HMSetRedisEx(REDIS_TABLE_NAME_BATTLERECORD, battleRecord.Id, &battleRecord, utils.DAY_SECS*1)
	if attackUid > 0 {
		attackInfo, ok := self.db_list[attackUid]
		if ok {
			//生成战报
			attackCosmicArenaFight := self.NewCosmicArenaFight(battleInfo.Id, attack, defend, battleInfo, battleInfo.Result, 0, attackPoint, defend)
			attackInfo.info.LevelArenaFight = append(attackInfo.info.LevelArenaFight, attackCosmicArenaFight)
			if len(attackInfo.info.LevelArenaFight) > 10 {
				attackInfo.info.LevelArenaFight = attackInfo.info.LevelArenaFight[1:]
			}
			//通知给攻击方
			msg := new(CosmicArenaEnd)
			msg.UserInfo = attackInfo.info
			msg.BattleRecord = &battleRecord
			msg.Record = record
			msg.AllScore = self.AllScore
			core.GetCenterApp().AddEvent(attack.Server, core.MATCH_COSMICARENA_ATTACKEND, attack.Uid,
				0, 0, utils.HF_JtoA(msg))
		}
	}
	if defendUid > 0 {
		defendInfo, ok := self.db_list[defendUid]
		if ok {
			//生成战报
			defendCosmicArenaFight := self.NewCosmicArenaFight(battleInfo.Id, attack, defend, battleInfo, battleInfo.Result, 1, defencePoint, attack)
			defendInfo.info.LevelArenaFight = append(defendInfo.info.LevelArenaFight, defendCosmicArenaFight)
			if len(defendInfo.info.LevelArenaFight) > 10 {
				defendInfo.info.LevelArenaFight = defendInfo.info.LevelArenaFight[1:]
			}
			//通知给攻击方
			msg := new(CosmicArenaEnd)
			msg.UserInfo = defendInfo.info
			msg.BattleRecord = &battleRecord
			msg.Record = record
			msg.AllScore = self.AllScore
			core.GetCenterApp().AddEvent(defend.Server, core.MATCH_COSMICARENA_BE_ATTACK, defend.Uid,
				0, 0, utils.HF_JtoA(msg))
		}
	}
	return
}

// 战斗服没能返回结果，返回客户端未匹配成功
func (self *CosmicArenaInfo) FightError(attack *model.JS_FightInfo) {
	if attack == nil {
		return
	}
	attackUid := int64(0)
	if attack.Uid > 0 {
		attackUid = attack.Uid
		attackInfo, ok := self.db_list[attackUid]
		if ok && attackInfo.info != nil {
			core.GetCenterApp().AddEvent(attack.Server, core.MATCH_COSMICARENA_FIGHT_ERROR, attack.Uid,
				0, int(attackUid), "")
		}
	}
	return
}

func (self *CosmicArenaInfo) NewCosmicArenaFight(fightId int64, attack *model.JS_FightInfo, defend *model.JS_FightInfo,
	battleInfo *model.BattleInfo, result int, side int, point int, fightInfo *model.JS_FightInfo) *model.CosmicArenaFight {

	data := new(model.CosmicArenaFight)
	data.FightId = fightId
	data.Side = side
	data.Result = result
	data.Point = point
	data.Uid = fightInfo.Uid
	data.IconId = fightInfo.Iconid
	data.Portrait = fightInfo.Portrait
	data.Title = fightInfo.Title
	data.Name = fightInfo.Uname
	data.Time = model.TimeServer().Unix()
	data.Level = fightInfo.Level
	if side == 0 {
		data.Fight += attack.Deffight
	} else {
		data.Fight += defend.Deffight
	}
	data.BattleInfo = battleInfo
	return data
}

func (self *CosmicArenaMgr) GetRank(uid int64, serverId int, fightInfos *model.JS_FightInfo, camp int) (int, string, string, string, string) {
	cosmicArenaInfo := self.GetCosmicArenaInfo(serverId)
	if cosmicArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", "", "", ""
	}
	selfInfo, rankInfo, selfInfoServer, rankInfoServer := cosmicArenaInfo.GetRank(uid, fightInfos, serverId, camp)
	selfInfoStr := ""
	rankInfoStr := ""
	selfInfoServerStr := ""
	rankInfoServerStr := ""
	if selfInfo != nil {
		selfInfoStr = utils.HF_JtoA(selfInfo)
	}
	if rankInfo != nil {
		rankInfoStr = utils.HF_JtoA(rankInfo)
	}
	if selfInfoServer != nil {
		selfInfoServerStr = utils.HF_JtoA(selfInfoServer)
	}
	if rankInfoServer != nil {
		rankInfoServerStr = utils.HF_JtoA(rankInfoServer)
	}
	return RETCODE_DATA_CROSS_OK, selfInfoStr, rankInfoStr, selfInfoServerStr, rankInfoServerStr
}

func (self *CosmicArenaInfo) GetRank(uid int64, fightInfos *model.JS_FightInfo, serverId int, camp int) (*model.RankInfo, []*model.RankInfo, *model.RankInfo, []*model.RankInfo) {
	selfInfo := self.GetInfoDB(uid, fightInfos, camp)
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rankInfo := make([]*model.RankInfo, 0)
	rankInfoServer := make([]*model.RankInfo, 0)
	for _, v := range self.rankInfo {
		//出现没打过的人 不出现在排行榜上
		//if v.IsSign == core.LOGIC_FALSE {
		//	break
		//}
		//本服的榜满了直接跳出
		if len(rankInfoServer) > COSMICARENA_RANK_MAX {
			break
		}
		if v.SvrId == serverId {
			rankUserServer := GetRankInfoCosmicArena(v)
			rankInfoServer = append(rankInfoServer, rankUserServer)
		}
		if len(rankInfo) > COSMICARENA_RANK_MAX {
			continue
		}
		rankUser := GetRankInfoCosmicArena(v)
		if v.IsSign == model.LOGIC_FALSE {
			continue
		}
		rankInfo = append(rankInfo, rankUser)
	}
	if selfInfo == nil {
		return nil, rankInfo, nil, rankInfoServer
	}
	// 注意 rankInfo,rankInfoServer是浅拷贝 所以排名不能在这里修改，给游戏服去处理
	return GetRankInfoCosmicArena(selfInfo.info), rankInfo, GetRankInfoCosmicArena(selfInfo.info), rankInfoServer
}

func (self *CosmicArenaMgr) GetRecord(uid int64, serverId int, id int64) (int, string) {
	utils.LogDebug("CosmicArenaMgr,GetRecord:", id)
	var battleRecord model.BattleRecord
	value, flag, err := db.HGetRedisEx(REDIS_TABLE_NAME_BATTLERECORD, id, fmt.Sprintf("%d", id))
	if err != nil || !flag {
		return 0, ""
	}
	err1 := json.Unmarshal([]byte(value), &battleRecord)
	if err1 != nil {
		return 0, ""
	}
	if battleRecord.Id == 0 {
		return 0, ""
	}
	return 0, utils.HF_JtoA(battleRecord)
}

func (self *CosmicArenaMgr) GetReplay(uid int64, serverId int, id int64) (int, string) {
	utils.LogDebug("CosmicArenaMgr,GetRecord:", id)
	fightData := model.FightReplay{}
	value, flag, err := db.HGetRedisEx(battle.BATTLE_REPLAY_RECORD, id, fmt.Sprintf("%d", id))
	if err != nil || !flag {
		return 0, ""
	}
	if flag {
		err1 := json.Unmarshal([]byte(value), &fightData)
		if err1 != nil {
			return 0, ""
		}
	}

	return 0, utils.HF_JtoA(fightData)
}

func (self *CosmicArenaMgr) Look(uid int64, serverId int, targetUid int64) (int, string, string) {
	cosmicArenaInfo := self.GetCosmicArenaInfo(serverId)
	if cosmicArenaInfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", ""
	}
	userInfo, userFightInfo := cosmicArenaInfo.Look(targetUid)
	return RETCODE_DATA_CROSS_OK, utils.HF_JtoA(userInfo), utils.HF_JtoA(userFightInfo)
}

func (self *CosmicArenaInfo) Look(uid int64) (*CosmicArenaUser, *model.JS_FightInfo) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	info, ok := self.db_list[uid]
	if !ok {
		return nil, nil
	}
	return info.info, info.fightInfos
}

func (self *CosmicArenaMgr) GetHallOfGlory(groupId int, serverId int) *HallOfGlorys {
	info := self.GetCosmicArenaInfoByGroupId(groupId)
	if info == nil {
		return nil
	}
	return info.GetHallOfGlorys(serverId)
}

func (self *CosmicArenaInfo) GetHallOfGlorys(serverId int) *HallOfGlorys {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rel := new(HallOfGlorys)
	for i := 0; i <= len(self.rankInfo); i++ {
		if i >= len(self.rankInfo) {
			break
		}
		if serverId > 0 && serverId != self.rankInfo[i].SvrId {
			continue
		}
		data := self.MakeHallOfGloryInfo(self.rankInfo[i])
		rel.HallOfGlory = append(rel.HallOfGlory, data)
		if len(rel.HallOfGlory) >= 3 {
			break
		}
	}
	return rel
}
func (self *CosmicArenaInfo) MakeHallOfGloryInfo(data *CosmicArenaUser) *HallOfGloryInfo {
	hallOfGloryInfo := new(HallOfGloryInfo)
	if data == nil {
		return hallOfGloryInfo
	}
	hallOfGloryInfo.Uid = data.Uid
	hallOfGloryInfo.SvrId = data.SvrId
	hallOfGloryInfo.SvrName = data.SvrName
	hallOfGloryInfo.UName = data.UName
	hallOfGloryInfo.UnionName = data.UnionName
	hallOfGloryInfo.Score = data.Score
	hallOfGloryInfo.ScoreLv = data.ScoreLv
	hallOfGloryInfo.RankPos = data.RankPos
	hallOfGloryInfo.Level = data.Level
	hallOfGloryInfo.Vip = data.Vip
	hallOfGloryInfo.Icon = data.Icon
	hallOfGloryInfo.Portrait = data.Portrait
	hallOfGloryInfo.Title = data.Title
	hallOfGloryInfo.Fight = data.Fight
	return hallOfGloryInfo
}

// 这个功能暂缓  要保存历史分数，需要拉人上来  考虑做离线模块或放弃这个设定 wait
func (self *CosmicArenaMgr) ClearData() {
	self.CosmicArenaInfo.Range(func(key, info interface{}) bool {
		if info == nil {
			return true
		}
		data := info.(*CosmicArenaInfo)
		data.SaveScoreLvHistory()
		return true
	})

	self.CosmicArenaInfo = new(sync.Map)
}

func (self *CosmicArenaInfo) SaveScoreLvHistory() {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	for _, v := range self.db_list {
		if v == nil || v.info == nil {
			continue
		}
		if v.info.ScoreLv <= 0 {
			continue
		}
		GetOfflineInfoMgr().SetCosmicScoreLvHistory(v.Uid, v.info.ScoreLv)
	}
}

//
//func (self *CosmicArenaMgr) UpdateInfo(uid int64, serverId int, fightInfos *core.JS_FightInfo) (int, string) {
//	if fightInfos == nil {
//		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
//	}
//	cosmicArenaInfo := self.GetCosmicArenaInfo(serverId)
//	if cosmicArenaInfo == nil {
//		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
//	}
//	info := cosmicArenaInfo.UpdateInfo(uid, fightInfos)
//	infoStr := ""
//	if info != nil {
//		infoStr = utils.HF_JtoA(info)
//	}
//	return RETCODE_DATA_CROSS_OK, infoStr
//}
//func (self *CosmicArenaInfo) UpdateInfo(uid int64, fightInfos *core.JS_FightInfo) *CosmicArenaUser {
//	infoDB := self.GetInfoDB(uid, fightInfos)
//	if infoDB == nil {
//		return nil
//	}
//	infoDB.SetFightInfo(fightInfos)
//	if infoDB.info != nil && infoDB.fightInfos != nil {
//		infoDB.info.Fight = 0
//		infoDB.info.UName = infoDB.fightInfos.Uname
//		infoDB.info.Icon = infoDB.fightInfos.Iconid
//		infoDB.info.Portrait = infoDB.fightInfos.Portrait
//		infoDB.info.Title = infoDB.fightInfos.Title
//		infoDB.info.Level = infoDB.fightInfos.Level
//		infoDB.info.UnionName = infoDB.fightInfos.UnionName
//		infoDB.info.SvrId = infoDB.fightInfos.Server
//		realValue := infoDB.fightInfos.Deffight / 100
//		infoDB.info.Fight += realValue * 100
//	}
//	return infoDB.info
//}

func (self *CosmicArenaDB) SetFightInfo(fightInfos *model.JS_FightInfo) {
	if fightInfos == nil {
		return
	}
	if len(fightInfos.Heroinfo) == 0 {
		return
	}
	if self.info != nil {
		fightInfos.Param = int64(self.info.ScoreLv)
		self.info.Fight = fightInfos.Deffight
	}
	self.fightInfos = fightInfos
}
func (self *CosmicArenaMgr) SendRankReward() {
	period := GetServerGroupMgr().GetCosmicArenaPeriods()
	self.CosmicArenaInfo.Range(func(key, info interface{}) bool {
		if info == nil {
			return true
		}
		v := info.(*CosmicArenaInfo)
		//跨服记录
		rewardList := v.GetRewardList()
		HallOfGlorysInfo := GetCosmicArenaMgr().GetHallOfGlory(v.groupId, 0)
		if HallOfGlorysInfo != nil {
			HallOfGlorysInfo.StartTime = GetServerGroupMgr().ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.StartTime
			HallOfGlorysInfo.EndTime = GetServerGroupMgr().ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.EndTime
		}
		for _, rewardInfo := range rewardList {
			rewardInfo.Period = period
			data, ok := GetServerGroupMgr().ServerZone.Load(rewardInfo.ServerId)
			if ok {
				serverInfo := data.(*ServerZoneInfo)
				if serverInfo == nil {
					return true
				}
				if serverInfo.activityInfo == nil {
					serverInfo.activityInfo = new(ActivityInfo)
				}
				serverInfo.activityInfo.LevelArenaHallOfGlorys = append(serverInfo.activityInfo.LevelArenaHallOfGlorys, HallOfGlorysInfo)
				//本服奖励
				hallOfGlorysSelf := GetCosmicArenaMgr().GetHallOfGlory(serverInfo.ZoneId, serverInfo.ServerId)
				if hallOfGlorysSelf != nil {
					hallOfGlorysSelf.StartTime = GetServerGroupMgr().ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.StartTime
					hallOfGlorysSelf.EndTime = GetServerGroupMgr().ServerZoneConfig.crossServerPlayConfig.LevelArenaConfig.EndTime
					serverInfo.activityInfo.LevelArenaHallOfGlorysSelf = append(serverInfo.activityInfo.LevelArenaHallOfGlorysSelf, hallOfGlorysSelf)
				}
			}

			info := utils.HF_JtoA(rewardInfo)
			WriterLog(3, info)
			core.GetCenterApp().AddEvent(rewardInfo.ServerId, core.CROSS_SERVER_COSMICARENA_RANK_REWARD, 0,
				0, 0, info)
			//给玩家打上排行标记
			for _, user := range rewardInfo.RewardInfo {
				GetOfflineInfoMgr().SetCosmicRank(user.Uid, period, user.RankPos)
			}
		}
		return true
	})
}

func (self *CosmicArenaInfo) GetRewardList() map[int]*RewardList {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rewardMap := make(map[int]*RewardList)
	for _, v := range self.rankInfo {
		if v.IsSign == model.LOGIC_FALSE {
			break
		}
		info, ok := rewardMap[v.SvrId]
		if !ok {
			info = new(RewardList)
			info.ServerId = v.SvrId
			rewardMap[info.ServerId] = info
		}
		info.RewardInfo = append(info.RewardInfo, &RewardInfo{Uid: v.Uid, RankPos: v.RankPos})
	}
	return rewardMap
}
func (self *CosmicArenaMgr) DeletePlayerRecord(req *RPC_RankDoLikeReq) {
	cosmicArenaInfo := self.GetCosmicArenaInfo(req.ServerId)
	if cosmicArenaInfo == nil {
		return
	}
	cosmicArenaInfo.DeletePlayerRecord(req.Uid)
}
func (self *CosmicArenaInfo) DeletePlayerRecord(uid int64) {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	user, ok := self.db_list[uid]
	if !ok {
		return
	}
	if user.info == nil {
		return
	}
	if user.info.Score > 0 {
		switch user.info.Camp {
		case COSMIC_ARENA_CAMP_1:
			self.AllScore[0] -= user.info.Score
			if self.AllScore[0] < 0 {
				self.AllScore[0] = 0
			}
		case COSMIC_ARENA_CAMP_2:
			self.AllScore[1] -= user.info.Score
			if self.AllScore[1] < 0 {
				self.AllScore[1] = 0
			}
		}
	}
	user.info.Score = 0
	sort.Sort(lstCosmicArenaRank(self.rankInfo))
	for index, v := range self.rankInfo {
		v.RankPos = index + 1
	}
}

func (self *CosmicArenaInfo) GetMatchShow(uid int64) []*model.RankInfo {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	rankInfo := make([]*model.RankInfo, 0)
	for _, v := range self.rankInfo {
		//出现没打过的人 不出现在排行榜上
		//if v.IsSign == core.LOGIC_FALSE {
		//	break
		//}
		if len(rankInfo) > 10 {
			continue
		}
		if v.Uid == uid {
			continue
		}
		rankUser := GetRankInfoCosmicArena(v)
		rankInfo = append(rankInfo, rankUser)
	}
	return rankInfo
}

func (self *CosmicArenaMgr) CheckBattleFinish() {
	now := model.TimeServer().Unix()

	deleteList := make([]int64, 0)
	self.BattleListen.Range(func(key, battleData interface{}) bool {
		if battleData == nil {
			deleteList = append(deleteList, key.(int64))
			return true
		}
		value := battleData.(*model.ArenaFightList)
		//如果超时直接返回机器人
		if value.Time+10 < now {
			cosmicArenaInfo := self.GetCosmicArenaInfo(value.Attack.Server)
			if cosmicArenaInfo != nil {
				//cosmicArenaInfo.FightError(value.Attack)

				//生成一个机器人，必胜
				robotFightInfo := self.GetRobotFightInfo(value.Attack.Level)
				value.Defend = robotFightInfo

				battleInfo := new(model.BattleInfo)
				battleInfo.Id = value.FightId

				var attackHeroInfo []*model.BattleHeroInfo
				for i := 0; i < 5; i++ {
					level, star, skin, exclusiveLv, heroId := 0, 0, 0, 0, 0
					if i < len(value.Attack.Heroinfo) {
						level = value.Attack.Heroinfo[i].Levels
						star = value.Attack.Heroinfo[i].Stars
						skin = value.Attack.Heroinfo[i].Skin
						exclusiveLv = value.Attack.Heroinfo[i].HeroExclusiveLv
						heroId = value.Attack.Heroinfo[i].Heroid
					}
					attackHeroInfo = append(attackHeroInfo, &model.BattleHeroInfo{HeroID: heroId, HeroLv: level,
						HeroStar: star, HeroSkin: skin, Hp: 10000, Energy: 10000, Damage: 0,
						TakeDamage: 0, Healing: 0, ArmyInfo: nil, ExclusiveLv: exclusiveLv,
						UseSkill: nil})
				}
				defendHeroInfo := []*model.BattleHeroInfo{}
				for i := 0; i < 5; i++ {
					level, star, skin, exclusiveLv, heroId := 0, 0, 0, 0, 0
					if i < len(value.Defend.Heroinfo) {
						level = value.Defend.Heroinfo[i].Levels
						star = value.Defend.Heroinfo[i].Stars
						skin = value.Defend.Heroinfo[i].Skin
						exclusiveLv = value.Defend.Heroinfo[i].HeroExclusiveLv
						heroId = value.Defend.Heroinfo[i].Heroid
					}
					defendHeroInfo = append(defendHeroInfo, &model.BattleHeroInfo{HeroID: heroId, HeroLv: level,
						HeroStar: star, HeroSkin: skin, Hp: 0, Energy: 0, Damage: 0,
						TakeDamage: 0, Healing: 0, ArmyInfo: nil, ExclusiveLv: exclusiveLv,
						UseSkill: nil})
				}
				battleInfo.UserInfo[model.POS_ATTACK] = NewBattleUserInfo(value.Attack, attackHeroInfo)
				battleInfo.UserInfo[model.POS_DEFENCE] = NewBattleUserInfo(value.Defend, defendHeroInfo)

				battleInfo.Time = model.TimeServer().Unix()
				battleInfo.Random = value.Random
				battleInfo.LevelID = 600001
				battleInfo.Result = model.ATTACK_WIN
				battleInfo.Param = model.LOGIC_TRUE //触发碾压
				//battleInfo.Param = core.LOGIC_FALSE //触发碾压
				cosmicArenaInfo.CalAttackEnd(value.Attack, value.Defend, battleInfo, []byte{})

			}
			deleteList = append(deleteList, key.(int64))
			return true
		}
		// 尝试获取战斗结果
		FightResult := battle.GetFightMgr().GetResult(value.FightId)
		if FightResult == nil {
			return true
		}
		cosmicArenaInfo := self.GetCosmicArenaInfo(value.Attack.Server)
		if cosmicArenaInfo != nil {
			battleInfo := new(model.BattleInfo)
			battleInfo.Id = value.FightId

			var attackHeroInfo []*model.BattleHeroInfo
			for i, v := range FightResult.Info[model.POS_ATTACK] {
				level, star, skin, exclusiveLv := 0, 0, 0, 0
				if i < len(FightResult.Fight[model.POS_ATTACK].Heroinfo) {
					level = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Levels
					star = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Stars
					skin = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Skin
					exclusiveLv = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].HeroExclusiveLv
				}
				attackHeroInfo = append(attackHeroInfo, &model.BattleHeroInfo{HeroID: v.Heroid, HeroLv: level,
					HeroStar: star, HeroSkin: skin, Hp: v.Hp, Energy: v.Energy, Damage: v.Damage,
					TakeDamage: v.TakeDamage, Healing: v.Healing, ArmyInfo: nil, ExclusiveLv: exclusiveLv,
					UseSkill: nil})
			}
			defendHeroInfo := []*model.BattleHeroInfo{}
			for i, v := range FightResult.Info[model.POS_DEFENCE] {
				level, star, skin, exclusiveLv := 0, 0, 0, 0
				if i < len(FightResult.Fight[model.POS_DEFENCE].Heroinfo) {
					level = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Levels
					star = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Stars
					skin = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Skin
					exclusiveLv = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].HeroExclusiveLv
				}
				defendHeroInfo = append(defendHeroInfo, &model.BattleHeroInfo{v.Heroid, level, star, skin, v.Hp, v.Energy, v.Damage, v.TakeDamage, v.Healing, nil, exclusiveLv, nil})
			}
			battleInfo.UserInfo[model.POS_ATTACK] = NewBattleUserInfo(value.Attack, attackHeroInfo)
			battleInfo.UserInfo[model.POS_DEFENCE] = NewBattleUserInfo(value.Defend, defendHeroInfo)
			battleInfo.Time = value.Time
			battleInfo.Random = value.Random
			battleInfo.Result = FightResult.Result
			battleInfo.LevelID = 600003
			cosmicArenaInfo.CalAttackEnd(value.Attack, value.Defend, battleInfo, FightResult.ResultDetail.Record)
		}
		deleteList = append(deleteList, key.(int64))
		return true
	})

	for _, v := range deleteList {
		self.BattleListen.Delete(v)
		battle.GetFightMgr().DelResult(v)
	}
}

func (self *CosmicArenaMgr) RandRobotConfig(scoreLv int) *JJCRobotConfig {

	for _, v := range self.JJCRobotConfigMap[scoreLv] {
		return v
	}
	for _, v := range self.JJCRobotConfigMap[1] {
		return v
	}
	return nil
}

func (self *CosmicArenaMgr) GetRobotFightInfo(scoreLv int) *model.JS_FightInfo {

	cfg := GetCosmicArenaMgr().RandRobotConfig(scoreLv)
	//这里不会返回空，检查表
	if cfg == nil {
		return nil
	}

	data := new(model.JS_FightInfo)

	data.Portrait = 1000 //机器人边框  20190412 by zy
	data.Level = cfg.Level
	data.Uname = "Guard"
	data.Defhero = make([]int, 0)
	data.Heroinfo = make([]model.JS_HeroInfo, 0)
	data.HeroParam = make([]model.JS_HeroParam, 0)

	var heroes []*RobotInfo
	for i := 0; i < len(cfg.Hero); i++ {
		if cfg.Hero[i] == 0 {
			continue
		}

		heroes = append(heroes, &RobotInfo{cfg.Hero[i], cfg.NpcLv[i], cfg.NpcStar[i]})
	}
	heroes = HF_GetRandomArr(heroes, 5)

	maxValue := int(cfg.Fight[0] - cfg.Fight[1])
	if maxValue <= 0 {
		maxValue = 50000
	}
	nowIconId := 0
	data.Deffight = cfg.Fight[1] + int64(utils.HF_RandInt(1, maxValue))
	for i := 0; i < len(heroes); i++ {
		err := data.FightTeamPos.AddFightPos(i + 1)
		if err != nil {
			continue
		}
		data.Defhero = append(data.Defhero, i)
		var hero model.JS_HeroInfo
		if heroes[i] != nil {
			hero.Heroid = heroes[i].Hero
			if nowIconId == 0 {
				nowIconId = hero.Heroid
			}
			hero.Color = cfg.NpcQuality
			hero.HeroKeyId = i + 1
			hero.Stars = heroes[i].NpcStar
			hero.HeroQuality = heroes[i].NpcStar
			hero.Levels = heroes[i].NpcLv
			hero.Skin = 0
			hero.Soldiercolor = 6
			hero.Skilllevel1 = 0
			hero.Skilllevel2 = 0
			hero.Skilllevel3 = 0
			hero.Skilllevel4 = 0
			hero.Skilllevel5 = 0
			hero.Skilllevel6 = 0
			hero.Fervor1 = 0
			hero.Fervor2 = 0
			hero.Fervor3 = 0
			hero.Fervor4 = 0
			hero.Fight = data.Deffight / 5

			hero.ArmsSkill = make([]model.JS_ArmsSkill, 0)
			hero.MainTalent = 0

		}
		att, att_ext, energy := hero.CountFight(cfg.BaseTypes, cfg.BaseValues)

		data.Heroinfo = append(data.Heroinfo, hero)
		var param model.JS_HeroParam
		param.Heroid = hero.Heroid
		param.Param = att
		param.ExtAttr = att_ext
		param.Hp = param.Param[1]
		param.Energy = energy
		param.Energy = 0

		data.HeroParam = append(data.HeroParam, param)
	}
	if nowIconId != 0 {
		data.Iconid = nowIconId + 10000000
	} else {
		data.Iconid = 10001001
	}

	return data
}

func (self *CosmicArenaMgr) SortRank() {
	self.CosmicArenaInfo.Range(func(key, info interface{}) bool {
		if info == nil {
			return true
		}
		serverGroup := info.(*CosmicArenaInfo)
		serverGroup.SortRank()
		return true
	})
}

func (self *CosmicArenaInfo) SortRank() {
	sort.Sort(lstCosmicArenaRank(self.rankInfo))
	for index, v := range self.rankInfo {
		v.RankPos = index + 1
		v.ScoreLv = conf.CalCosmicScoreLv(v.Score, v.RankPos)
		if v.ScoreLvMax < v.ScoreLv {
			v.ScoreLvMax = v.ScoreLv
		}
	}
}

func (self *CosmicArenaMgr) CheckRoll() {
	deleteList := make([]int64, 0)
	self.BattleListenRoll.Range(func(key, battleData interface{}) bool {
		if battleData == nil {
			deleteList = append(deleteList, key.(int64))
			return true
		}
		value := battleData.(*model.ArenaFightList)

		cosmicArenaInfo := self.GetCosmicArenaInfo(value.Attack.Server)
		if cosmicArenaInfo != nil {
			battleInfo := new(model.BattleInfo)
			battleInfo.Id = value.FightId
			var attackHeroInfo []*model.BattleHeroInfo
			for i, v := range value.Attack.Heroinfo {
				level, star, skin, exclusiveLv := 0, 0, 0, 0
				if i < len(value.Attack.Heroinfo) {
					level = value.Attack.Heroinfo[i].Levels
					star = value.Attack.Heroinfo[i].Stars
					skin = value.Attack.Heroinfo[i].Skin
					exclusiveLv = value.Attack.Heroinfo[i].HeroExclusiveLv
				}
				attackHeroInfo = append(attackHeroInfo, &model.BattleHeroInfo{HeroID: v.Heroid, HeroLv: level,
					HeroStar: star, HeroSkin: skin, Hp: 10000, Energy: 10000, Damage: 0,
					TakeDamage: 0, Healing: 0, ArmyInfo: nil, ExclusiveLv: exclusiveLv,
					UseSkill: nil})
			}
			defendHeroInfo := []*model.BattleHeroInfo{}
			for i, v := range value.Defend.Heroinfo {
				level, star, skin, exclusiveLv := 0, 0, 0, 0
				if i < len(value.Defend.Heroinfo) {
					level = value.Defend.Heroinfo[i].Levels
					star = value.Defend.Heroinfo[i].Stars
					skin = value.Defend.Heroinfo[i].Skin
					exclusiveLv = value.Defend.Heroinfo[i].HeroExclusiveLv
				}
				defendHeroInfo = append(defendHeroInfo, &model.BattleHeroInfo{v.Heroid, level,
					star, skin, 0, 0, 0, 0,
					0, nil, exclusiveLv, nil})
			}
			battleInfo.UserInfo[model.POS_ATTACK] = NewBattleUserInfo(value.Attack, attackHeroInfo)
			battleInfo.UserInfo[model.POS_DEFENCE] = NewBattleUserInfo(value.Defend, defendHeroInfo)
			battleInfo.Time = value.Time
			battleInfo.Random = value.Random
			battleInfo.Result = battle.WIN_ATTCK

			battleInfo.Id = value.FightId
			battleInfo.Time = model.TimeServer().Unix()
			battleInfo.Random = value.Random
			battleInfo.LevelID = 600001
			battleInfo.Result = model.ATTACK_WIN
			battleInfo.Param = model.LOGIC_TRUE //触发碾压
			cosmicArenaInfo.CalAttackEnd(value.Attack, value.Defend, battleInfo, []byte{})
		}
		deleteList = append(deleteList, key.(int64))
		return true
	})

	for _, v := range deleteList {
		self.BattleListenRoll.Delete(v)
	}
}

func (self *CosmicArenaInfo) LevelArenaTest(uid int64, w http.ResponseWriter) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	info, ok := self.db_list[uid]
	if !ok {
		w.Write([]byte(fmt.Sprintf("找不到玩家数据 \n")))
		return
	}
	if info.info == nil {
		w.Write([]byte(fmt.Sprintf("找不到战斗数据 \n")))
		return
	}
	index := info.info.RankPos - 1
	if index < 0 || index >= len(self.rankInfo) {
		return
	}
	scoreLv := self.rankInfo[index].ScoreLv
	w.Write([]byte(fmt.Sprintf("玩家[%d]段位[%d] \n", uid, scoreLv)))
	scoreLvMap := make(map[int]map[int64]int)
	scoreLvMapDebug := make(map[int64]*CosmicArenaUser)
	for i := index - 1; i >= 0; i-- {
		//if self.rankInfo[i].IsSign == core.LOGIC_FALSE {
		//	continue
		//}
		nowScoreLv := self.rankInfo[i].ScoreLv
		if self.IsStopFind(scoreLv, nowScoreLv-scoreLv) {
			break
		}
		_, ok := scoreLvMap[nowScoreLv]
		if !ok {
			scoreLvMap[nowScoreLv] = make(map[int64]int)
		}
		if len(scoreLvMap[nowScoreLv]) >= COSMICARENA_ENEMY_CHOOSE_LIST {
			continue
		}
		scoreLvMapDebug[self.rankInfo[i].Uid] = self.rankInfo[i]
	}
	for i := index + 1; i < len(self.rankInfo); i++ {
		//if self.rankInfo[i].IsSign == core.LOGIC_FALSE {
		//	continue
		//}
		nowScoreLv := self.rankInfo[i].ScoreLv
		if self.IsStopFind(scoreLv, scoreLv-nowScoreLv) {
			break
		}
		_, ok := scoreLvMap[nowScoreLv]
		if !ok {
			scoreLvMap[nowScoreLv] = make(map[int64]int)
		}
		if len(scoreLvMap[nowScoreLv]) >= COSMICARENA_ENEMY_CHOOSE_LIST {
			continue
		}
		scoreLvMapDebug[self.rankInfo[i].Uid] = self.rankInfo[i]
	}
	w.Write([]byte(fmt.Sprintf("候选列表: \n")))
	for _, v := range scoreLvMapDebug {
		w.Write([]byte(fmt.Sprintf("玩家[%d]名字[%s]段位[%d]分数[%d]: \n", v.Uid, v.UName, v.ScoreLv, v.Score)))
	}

	cantList := make(map[int64]int)
	for k := range self.rankInfo[index].CantList {
		cantList[k] = model.LOGIC_TRUE
	}
	cantList[uid] = model.LOGIC_TRUE
	w.Write([]byte(fmt.Sprintf("BAN列表: \n")))
	for k, _ := range cantList {
		w.Write([]byte(fmt.Sprintf("玩家[%d]: \n", k)))
	}

	return
}

//func (self *CosmicArenaMgr) GameServerGetRankReward(serverId int, userList string) {
//
//	targetList := make(map[int64]int)
//	json.Unmarshal([]byte(userList), &targetList)
//	if len(targetList) == 0 {
//		return
//	}
//	targetPeriod := GetServerGroupMgr().GetCosmicArenaPeriods() - 1
//
//	rewardInfo := new(RewardList)
//	rewardInfo.ServerId = serverId
//	rewardInfo.Period = targetPeriod
//	rewardInfo.RewardInfo = make([]*RewardInfo, 0)
//	for uid, period := range targetList {
//		if period >= targetPeriod {
//			continue
//		}
//		rankPos := GetOfflineInfoMgr().GetLevelAreanRank(uid, targetPeriod)
//		if rankPos == 0 {
//			continue
//		}
//		rewardInfo.RewardInfo = append(rewardInfo.RewardInfo, &RewardInfo{Uid: uid, RankPos: rankPos})
//	}
//	core.GetCenterApp().AddEvent(rewardInfo.ServerId, core.CROSS_SERVER_COSMICARENA_RANK_REWARD, 0,
//		0, 0, utils.HF_JtoA(rewardInfo))
//}
