package crossserver

//争霸赛 跨服
import (
	"fmt"
	json "github.com/bytedance/sonic"
	"master/center/battle"
	"master/center/conf"
	"master/core"
	"master/db"
	"master/model"
	"master/utils"
	"math"
	"net/http"
	"sort"
	"sync"
	"time"
)

const (
	TABLE_NAME_CHAMPION                    = "tbl_champion"              // 主配置
	TABLE_NAME_CHAMPION_SCORE_INFO         = "tbl_championscoreinfo"     // 积分赛信息
	TABLE_NAME_CHAMPION_ELIMINATE_INFO     = "tbl_championeliminateinfo" // 淘汰赛信息
	TABLE_NAME_CHAMPION_FIGHTINFO          = "tbl_fightinfochampion"     // 玩家信息
	MAX_PLAYER_CHAMPION_SCORE              = 128
	MAX_PLAYER_CHAMPION_ELIMINATE          = 64
	MAX_PLAYER_CHAMPION_SCORE_MAX_TURN     = 6
	MAX_PLAYER_CHAMPION_ELIMINATE_MAX_TURN = 6
	CHAMPION_START_TIME_DIS                = 300  //300
	CHAMPION_START_TIME_DIS_END            = 3480 //2400
	ROBOT_ARENA_UID_END                    = 50000
	BATTLE_INFO_CHAMPION                   = "san_championbattleinfo"
	BATTLE_RECORD_CHAMPION                 = "san_championbattlerecord"
	CHAMPIONZONE_FIGHT_UPDATE              = "championzonefightupdate"
	CHAMPIONZONE_SYN                       = "championzonesyn" //状态同步
)

const (
	CHAMPION_ZONE_TYPE_1 = 1 // 来源跨服段位赛
)

const (
	CHAMPION_STAGE_INVALID   = 0
	CHAMPION_STAGE_SIGN      = 1 //生成对手
	CHAMPION_STAGE_SCORE     = 2 //积分赛
	CHAMPION_STAGE_ELIMINATE = 3 //淘汰赛
	CHAMPION_STAGE_END       = 4 //战斗结束
	CHAMPION_STAGE_NEXT      = 5 //清空数据时间
)

const (
	SCOREMATCH_STAGE_BET    = 1 //下注
	SCOREMATCH_STAGE_BATTLE = 2 //战斗
	SCOREMATCH_STAGE_END    = 3 //结束
)

const (
	CHAMPION_GROUP_TYPE_ZONE  = 1 //zone
	CHAMPION_GROUP_TYPE_GROUP = 2 //group
)

const (
	CHAMPION_MATCH_BET_SEND   = 1 //发放
	CHAMPION_MATCH_BET_RESULT = 2 //结算
	CHAMPION_MATCH_BET_CLEAR  = 3 //清理
)

const (
	CHAMPION_START_HOUR   = 19
	CHAMPION_START_MINUTE = 55
)

type S2C_ChampionUpdateBet struct {
	Cid       string `json:"cid"`
	Reason    int    `json:"reason"` //0发放    1结算
	TargetUid int64  `json:"targetuid"`
	TargetBet int    `json:"targetbet"`
	NowBet    int    `json:"nowbet"`
	BetResult int    `json:"betresult"`
	BetChange int    `json:"betchange"`
}

type S2C_ChampionZoneSyn struct {
	Cid              string                 `json:"cid"`
	ActType          int                    `json:"acttype"`
	Period           int                    `json:"period"`
	Stage            int                    `json:"stage"`
	SignTime         int64                  `json:"signtime"`
	ScoreTime        int64                  `json:"scoretime"`
	EliminateTime    int64                  `json:"eliminatetime"`
	EndTime          int64                  `json:"endtime"`
	NextTime         int64                  `json:"nexttime"`
	NextSignTime     int64                  `json:"nextsigntime"`     //展示用 预告开始时间
	NextEndTime      int64                  `json:"nextendtime"`      //展示用 预告结算时间
	ScoreSyn         *JS_ScoreMatchInfo     `json:"scoresyn"`         // 积分赛同步
	EliminateSyn     *JS_EliminateMatchInfo `json:"eliminatesyn"`     // 淘汰赛同步
	ScoreUserSyn     []*ScoreTurnUser       `json:"scoreusersyn"`     // 积分赛用户同步
	EliminateUserSyn []*EliminateUser       `json:"eliminateusersyn"` // 淘汰赛用户同步
	FightInfoList    []*JS_SimpleInfo       `json:"fightinfolist"`    // 玩家信息列表
	TopRank          []*ChampionTop         `json:"toprank"`
	SendTime         int64                  `json:"sendtime"`
}

type ChampionTypeInfo struct {
	ChampionAllMgr       map[int]*ChampionMgr //groupId:ChampionMgr // 每组数据
	ChampionSimpleConfig *ChampionSimpleConfig
	InfoLocker           *sync.RWMutex
}

type ChampionAllMgr struct {
	ChampionTypeInfoMap     map[int]*ChampionTypeInfo // [活动类型]
	ChampionSimpleConfigMap map[int]*ChampionSimpleConfig
	Locker                  *sync.RWMutex
}

type ChampionMgr struct {
	ZoneId            int
	ActType           int
	ChampionInfo      ChampionInfo
	DataScoreInfo     ChampionScoreInfo     // 积分赛信息
	DataEliminateInfo ChampionEliminateInfo // 淘汰赛信息
	ChampionFightInfo *sync.Map             //map[int64]*FightInfoCache  // 参与玩家战斗数据
	FightList         *sync.Map             //战报
	Locker            *sync.RWMutex         //! 操作保护
	NeedSyn           []int64
	MapBattleInfo     *sync.Map
	ScoreTime         map[int]int
	ScoreTimeAll      map[int]int
	EliminateTime     map[int]int
	EliminateAll      map[int]int
}

type ChampionSimpleConfig struct {
	Type                     int   `json:"type"`
	GroupType                int   `json:"group_type"`
	TimeReset                int   `json:"time_reset"`
	ScoreCalStandard         int   `json:"score_cal_standard"`           //117 冠军赛积分赛积分标准分值
	ScoreCalStandardFight    int   `json:"score_cal_standard_fight"`     //118 冠军赛积分赛战力加分差距百分比（配置值/100=实际值）
	ScoreCalStandardSuperWin int   `json:"score_cal_standard_super_win"` //119 冠军赛积分赛满血胜利额外加分上限值
	ScoreCalStandardHp       int   `json:"score_cal_standard_hp"`        //120 冠军赛积分赛血量减少分数百分比（配置值/100=实际值）
	ScoreWinTimeStandard     int   `json:"score_win_time_standard"`      //121 冠军赛积分赛时间获胜基准（秒）
	ScoreWinTimeSec          int   `json:"score_win_time_sec"`           //122 冠军赛积分赛每X秒扣1分
	BetTime                  int64 `json:"bet_time"`                     //123 冠军赛押注/阵容调整时间（秒）
	BattleTime               int64 `json:"battle_time"`                  //124 冠军赛战斗播放时间（秒）
	BetSend                  int   `json:"bet_send"`                     //125 冠军赛默认赠送竞猜货币数量
	BetWin                   int   `json:"bet_win"`                      //126 冠军赛竞猜胜利返还比利（配置值/100=实际值）
	BetLose                  int   `json:"bet_lose"`                     //127 冠军赛竞猜失败返还比例（配置值/100=实际值）
	BetMax                   int   `json:"bet_max"`                      //128 冠军赛竞猜下注货币上限
	RankGroup                int   `json:"rank_group"`                   //排行奖励
	FirstPlayerMax           int   `json:"first_player_max"`             //第1阶段人数
	SecondPlayerMax          int   `json:"second_player_max"`            //第2阶段人数
	FirstTurn                int   `json:"first_turn"`                   //第1阶段轮次
	SecondTurn               int   `json:"second_turn"`                  //第2阶段轮次
	StartTime                int64 `json:"start_time"`                   //报名到开始时间
	EndTime                  int64 `json:"end_time"`                     //开始到结束时间
	ChampionStartHour        int   `json:"champion_start_hour"`          //比赛开始小时
	ChampionStartMinute      int   `json:"champion_start_minute"`        //比赛开始分钟
}

type S2C_ChampionZoneFightUpdate struct {
	Cid        string              `json:"cid"`
	Stage      int                 `json:"stage"`
	TurnId     int                 `json:"turnid"`
	TeamId     int                 `json:"teamid"`
	BattleInfo []*model.BattleInfo `json:"battleinfo"`
	ActType    int                 `json:"acttype"`
}

var championAllMgr *ChampionAllMgr = nil

func GetChampionAllMgr() *ChampionAllMgr {
	if championAllMgr == nil {
		championAllMgr = new(ChampionAllMgr)
		championAllMgr.ChampionTypeInfoMap = make(map[int]*ChampionTypeInfo)
		championAllMgr.Locker = new(sync.RWMutex)
	}
	return championAllMgr
}

// 初始化
func (self *ChampionAllMgr) GetData() {
	championAllMgr.InitTypeInfo()
	for _, info := range self.ChampionTypeInfoMap {
		for _, v := range info.ChampionAllMgr {
			go v.Run()
		}
	}
}

func (self *ChampionAllMgr) OnSave() {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	for _, info := range self.ChampionTypeInfoMap {
		info.InfoLocker.RLock()
		for _, v := range info.ChampionAllMgr {
			v.Save()
		}
		info.InfoLocker.RUnlock()
	}
}

func (self *ChampionAllMgr) AddInfo(groupType int, groupid int) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	for actType, typeInfo := range self.ChampionTypeInfoMap {
		if typeInfo.ChampionSimpleConfig.GroupType == groupType {
			typeInfo.NewChampion(actType, groupid)
		}
	}
}

func (self *ChampionTypeInfo) NewChampion(actType, zoneid int) {
	self.InfoLocker.RLock()
	_, ok := self.ChampionAllMgr[zoneid]
	if ok {
		self.InfoLocker.RUnlock()
		return
	}
	self.InfoLocker.RUnlock()

	data := new(ChampionMgr)
	data.ZoneId = zoneid
	data.ActType = actType
	data.Locker = new(sync.RWMutex)
	data.ChampionFightInfo = new(sync.Map) //make(map[int64]*FightInfoCache)
	data.ScoreTime = make(map[int]int)
	data.ScoreTimeAll = make(map[int]int)
	data.EliminateTime = make(map[int]int)
	data.EliminateAll = make(map[int]int)
	data.GetData()
	self.InfoLocker.Lock()
	defer self.InfoLocker.Unlock()
	self.ChampionAllMgr[data.ZoneId] = data
}

func (self *ChampionAllMgr) GetChampionSimpleConfig(actType int) *ChampionSimpleConfig {
	_, ok := self.ChampionSimpleConfigMap[actType]
	if !ok {
		return nil
	}
	return self.ChampionSimpleConfigMap[actType]
}

func (self *ChampionAllMgr) InitTypeInfo() {
	self.ChampionSimpleConfigMap = make(map[int]*ChampionSimpleConfig, 0)
	utils.GetCsvUtilMgr().LoadCsv("Activity_Csc_config", &self.ChampionSimpleConfigMap)

	//self.ChampionSimpleConfigMap = make(map[int]*ChampionSimpleConfig)
	//for _, config := range ChampionSimpleConfigTemp {
	//	_, ok := self.ChampionSimpleConfigMap[config.Type]
	//	if !ok {
	//		self.ChampionSimpleConfigMap[config.Type] = config
	//	}
	//}

	for _, config := range self.ChampionSimpleConfigMap {
		info := new(ChampionTypeInfo)
		info.ChampionAllMgr = make(map[int]*ChampionMgr)
		info.InfoLocker = new(sync.RWMutex)
		info.ChampionSimpleConfig = config

		if config.GroupType == CHAMPION_GROUP_TYPE_ZONE {
			for zoneid := 1; zoneid <= GetServerGroupMgr().ServerZoneConfig.GroupMax; zoneid++ {
				info.NewChampion(config.Type, zoneid)
			}
		} else if config.GroupType == CHAMPION_GROUP_TYPE_GROUP {
			for groupid := 1; groupid <= GetServerGroupMgr().ServerGroupConfig.GroupMax; groupid++ {
				info.NewChampion(config.Type, groupid)
			}
		}

		self.ChampionTypeInfoMap[config.Type] = info
	}
}

func (self *ChampionMgr) Run() {
	ticker := time.NewTicker(time.Second * 1)
	for {
		select {
		case <-ticker.C:
			self.OnTimer()
			self.CheckBetResult()
		}
	}
}

func (self *ChampionAllMgr) GetChampionTypeInfo(actType int) *ChampionTypeInfo {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	_, ok := self.ChampionTypeInfoMap[actType]
	if !ok {
		return nil
	}
	return self.ChampionTypeInfoMap[actType]
}

func (self *ChampionAllMgr) GetChampion(actType int, serverid int) *ChampionMgr {
	info := self.GetChampionTypeInfo(actType)
	if info == nil {
		return nil
	}

	groupid := -1
	if info.ChampionSimpleConfig.GroupType == CHAMPION_GROUP_TYPE_ZONE {
		groupid = GetServerGroupMgr().GetZoneId(serverid)
	} else if info.ChampionSimpleConfig.GroupType == CHAMPION_GROUP_TYPE_GROUP {
		groupid = GetServerGroupMgr().GetGroupId(serverid)
	}
	if groupid < 0 {
		return nil
	}

	info.InfoLocker.RLock()
	defer info.InfoLocker.RUnlock()
	_, ok := info.ChampionAllMgr[groupid]
	if !ok {
		return nil
	}
	return info.ChampionAllMgr[groupid]
}

// 数据初始化
func (self *ChampionMgr) GetData() {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	self.LoadChampionLock()
	//积分赛信息
	self.LoadScoreMatchInfoLock()
	//淘汰赛信息
	self.LoadEliminateMatchInfoLock()
	//生成战报缓存
	self.InitBattleInfoSafe()
	self.Save()
	return
}

// 读取主配置
func (self *ChampionMgr) LoadChampionLock() {
	queryStr := fmt.Sprintf("select * from `%s` where zoneid=%d and acttype=%d", TABLE_NAME_CHAMPION, self.ZoneId, self.ActType)
	res := db.GetDBMgr().DBUser.GetAllData(queryStr, &self.ChampionInfo)

	if len(res) > 0 {
		self.ChampionInfo.Decode()
	} else {
		self.ChampionInfo.Encode()
		self.ChampionInfo.ZoneId = self.ZoneId
		self.ChampionInfo.ActType = self.ActType
		self.ChampionInfo.Id = int(db.InsertTable(TABLE_NAME_CHAMPION, &self.ChampionInfo, 0, false))
	}
	self.ChampionInfo.Init(TABLE_NAME_CHAMPION, &self.ChampionInfo, false)
}

func (self *ChampionMgr) loadUserChampionFightInfo(uid int64) (bool, *FightInfoCache) {
	var fightInfoCacheDB FightInfoCacheDB
	sql := fmt.Sprintf("select * from `%s` where uid = %d and acttype = %d limit 1", TABLE_NAME_CHAMPION_FIGHTINFO, uid, self.ActType)
	res := db.GetDBMgr().DBUser.GetAllData(sql, &fightInfoCacheDB)
	for i := 0; i < len(res); i++ {
		data := res[i].(*FightInfoCacheDB)
		data.Decode()
		data.Init(TABLE_NAME_CHAMPION_FIGHTINFO, data, false)

		fightInfoCache := new(FightInfoCache)
		fightInfoCache.NeedSave = false
		fightInfoCache.LastLiveTime = model.TimeServer().Unix()
		fightInfoCache.FightInfoCacheDB = data
		self.ChampionFightInfo.Store(fightInfoCache.FightInfoCacheDB.Uid, fightInfoCache)
		return true, fightInfoCache
	}
	return false, nil
}

func (self *ChampionMgr) LoadScoreMatchInfoLock() {
	var scoreMatchInfo ScoreMatchInfo
	sql := fmt.Sprintf("select * from `%s` where period=%d and zoneid=%d and acttype=%d", TABLE_NAME_CHAMPION_SCORE_INFO,
		self.ChampionInfo.Period, self.ZoneId, self.ActType)
	res := db.GetDBMgr().DBUser.GetAllData(sql, &scoreMatchInfo)

	userConfigIndex := 0
	turnMax := 0
	self.DataScoreInfo.ScoreMatchInfo = make(map[int]*ScoreMatchInfo)
	if len(res) > 0 {
		for i := 0; i < len(res); i++ {
			data := res[i].(*ScoreMatchInfo)
			data.Decode()
			data.Init(TABLE_NAME_CHAMPION_SCORE_INFO, data, true)
			if turnMax <= data.ScoreTurnId {
				turnMax = data.ScoreTurnId
				userConfigIndex = i
			}
			self.DataScoreInfo.ScoreMatchInfo[data.ScoreTurnId] = data
		}
		nowMatch := res[userConfigIndex].(*ScoreMatchInfo)
		self.DataScoreInfo.ScoreUser = nowMatch.ScoreUser
		self.DataScoreInfo.UserFight = nowMatch.UserFight
		if self.ChampionInfo.Stage == CHAMPION_STAGE_SCORE {
			self.DataScoreInfo.ScoreTurn = nowMatch.ScoreTurnId
		} else {
			self.DataScoreInfo.ScoreTurn = nowMatch.ScoreTurnId + 1
		}
	}
	return
}

func (self *ChampionMgr) LoadEliminateMatchInfoLock() {
	var eliminateMatchInfo EliminateMatchInfo
	sql := fmt.Sprintf("select * from `%s` where period=%d and zoneid=%d and acttype=%d", TABLE_NAME_CHAMPION_ELIMINATE_INFO,
		self.ChampionInfo.Period, self.ZoneId, self.ActType)
	res := db.GetDBMgr().DBUser.GetAllData(sql, &eliminateMatchInfo)

	userConfigIndex := 0
	turnMax := 0
	self.DataEliminateInfo.EliminateMatchInfo = make(map[int]*EliminateMatchInfo)
	if len(res) > 0 {
		for i := 0; i < len(res); i++ {
			data := res[i].(*EliminateMatchInfo)
			data.Decode()
			data.Init(TABLE_NAME_CHAMPION_ELIMINATE_INFO, data, true)
			if turnMax <= data.EliminateTurnId {
				turnMax = data.EliminateTurnId
				userConfigIndex = i
			}
			self.DataEliminateInfo.EliminateMatchInfo[data.EliminateTurnId] = data
		}
		nowMatch := res[userConfigIndex].(*EliminateMatchInfo)
		self.DataEliminateInfo.EliminateUser = nowMatch.EliminateUser
		if self.ChampionInfo.Stage == CHAMPION_STAGE_ELIMINATE {
			self.DataEliminateInfo.EliminateTurn = nowMatch.EliminateTurnId
		} else {
			self.DataEliminateInfo.EliminateTurn = nowMatch.EliminateTurnId + 1
		}
	}
	return
}

func (self *ChampionMgr) InitBattleInfoSafe() {
	self.MapBattleInfo = new(sync.Map)

	for _, scoreMatchInfo := range self.DataScoreInfo.ScoreMatchInfo {
		for _, teamInfo := range scoreMatchInfo.ScoreMatchTeam {
			if teamInfo.BattleId == 0 {
				continue
			}
			battleNow := GetBattleInfoRedis(teamInfo.BattleId)
			if battleNow == nil {
				continue
			}
			self.MapBattleInfo.Store(battleNow.Id, battleNow)
		}
	}

	for _, eliminateMatchInfo := range self.DataEliminateInfo.EliminateMatchInfo {
		for _, teamInfo := range eliminateMatchInfo.EliminateMatchTeam {
			if teamInfo.BattleId == 0 {
				continue
			}
			battleNow := GetBattleInfoRedis(teamInfo.BattleId)
			if battleNow == nil {
				continue
			}
			self.MapBattleInfo.Store(battleNow.Id, battleNow)
		}
	}

	if self.ChampionInfo.MapBetInfo == nil {
		self.ChampionInfo.MapBetInfo = make(map[int64]*ChampionBetInfo, 0)
	}
}

func (self *ChampionMgr) OnTimer() {
	if self.ChampionInfo.SignTime == 0 {
		self.InitData()
	}
	nowTime := model.TimeServer().Unix()
	switch self.ChampionInfo.Stage {
	case CHAMPION_STAGE_INVALID:
		if nowTime < self.ChampionInfo.SignTime {
			return
		}

		self.ChangeStage(CHAMPION_STAGE_SIGN)
	case CHAMPION_STAGE_SIGN:
		if nowTime < self.ChampionInfo.ScoreTime {
			return
		}
		self.ChangeStage(CHAMPION_STAGE_SCORE)
	case CHAMPION_STAGE_SCORE:
		if !self.StageScoreEnd() {
			self.ChampionScoreOnTimer()
			return
		}
		self.SetEliminateTime()
		self.ChangeStage(CHAMPION_STAGE_ELIMINATE)
	case CHAMPION_STAGE_ELIMINATE:
		if !self.StageEliminateEnd() {
			self.ChampionEliminateOnTimer()
			return
		}
		//积分赛结算 这个时间根据当前时间来算比较智能
		self.ChangeStage(CHAMPION_STAGE_END)
	case CHAMPION_STAGE_END:
		self.ChangeStage(CHAMPION_STAGE_NEXT)
		return
	case CHAMPION_STAGE_NEXT:
		if nowTime < self.ChampionInfo.NextTime {
			return
		}
		//生成下一期的数据  1926
		self.InitData()
	}
}

func (self *ChampionMgr) CheckBetResult() {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	// 不在战斗阶段
	if self.ChampionInfo.Stage != CHAMPION_STAGE_SCORE && self.ChampionInfo.Stage != CHAMPION_STAGE_ELIMINATE {
		return
	}

	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return
	}

	// 积分赛阶段
	if self.ChampionInfo.Stage == CHAMPION_STAGE_SCORE {
		turn := self.DataScoreInfo.ScoreTurn
		data, ok := self.DataScoreInfo.ScoreMatchInfo[turn]
		if !ok {
			return
		}
		if data.ScoreMatchStage != SCOREMATCH_STAGE_BATTLE {
			return
		}

		if self.DataScoreInfo.GetTempBetResult() != 0 {
			return
		}

		betFightId := int64(0)
		team := data.GetTeam(data.BetTeamId)
		if team != nil {
			betFightId = team.BattleId
		}

		FightResult := battle.GetFightMgr().GetResult(int64(betFightId))
		// 有结果
		if FightResult != nil && FightResult.Result != battle.WIN_NULL {
			battleInfo := self.GetFightUpdate(int64(betFightId))

			var msgRel S2C_ChampionZoneFightUpdate
			msgRel.Cid = CHAMPIONZONE_FIGHT_UPDATE
			msgRel.Stage = self.ChampionInfo.Stage
			msgRel.TurnId = turn
			msgRel.TeamId = int(betFightId)
			msgRel.ActType = self.ActType
			if battleInfo != nil {
				msgRel.BattleInfo = append(msgRel.BattleInfo, battleInfo)
			} else {
				battleInfo = new(model.BattleInfo)
				battleInfo.Init()
				msgRel.BattleInfo = append(msgRel.BattleInfo, battleInfo)
			}

			msgdata := utils.HF_JtoA(msgRel)
			if config.GroupType == CHAMPION_GROUP_TYPE_ZONE {
				serverGroupMap := GetServerGroupMgr().GetServerZoneMap()
				for serverid, serverGroup := range serverGroupMap {
					if serverGroup == self.ZoneId {
						core.GetCenterApp().AddEvent(serverid, core.CHAMPION_ZONE_EVENT_FIGHT_UPDATE, 0,
							0, 0, msgdata)
					}
				}
			} else if config.GroupType == CHAMPION_GROUP_TYPE_GROUP {
				serverGroupMap := GetServerGroupMgr().GetServerGroupMap()
				for serverid, serverGroup := range serverGroupMap {
					if serverGroup == self.ZoneId {
						core.GetCenterApp().AddEvent(serverid, core.CHAMPION_ZONE_EVENT_FIGHT_UPDATE, 0,
							0, 0, msgdata)
					}
				}
			}

			//core.GetPlayerMgr().BroadcastMsg(msgRel.Cid, utils.HF_JtoB(&msgRel))
			self.DataScoreInfo.SetTempBetResult(1)
		}
	} else {
		turn := self.DataEliminateInfo.EliminateTurn
		data, ok := self.DataEliminateInfo.EliminateMatchInfo[turn]
		if !ok {
			return
		}
		if data.EliminateMatchStage != SCOREMATCH_STAGE_BATTLE {
			return
		}

		if self.DataEliminateInfo.GetTempBetResult() != 0 {
			return
		}

		betFightId := int64(0)
		team := data.GetTeam(data.BetTeamId)
		if team != nil {
			betFightId = team.BattleId
		}
		FightResult := battle.GetFightMgr().GetResult(int64(betFightId))
		// 有结果
		if FightResult != nil && FightResult.Result != battle.WIN_NULL {
			battleInfo := self.GetFightUpdate(int64(betFightId))

			var msgRel S2C_ChampionZoneFightUpdate
			msgRel.Cid = CHAMPIONZONE_FIGHT_UPDATE
			msgRel.Stage = self.ChampionInfo.Stage
			msgRel.TurnId = turn
			msgRel.ActType = self.ActType
			msgRel.TeamId = int(betFightId)
			if battleInfo != nil {
				msgRel.BattleInfo = append(msgRel.BattleInfo, battleInfo)
			} else {
				battleInfo = new(model.BattleInfo)
				battleInfo.Init()
				msgRel.BattleInfo = append(msgRel.BattleInfo, battleInfo)
			}

			msgdata := utils.HF_JtoA(msgRel)
			if config.GroupType == CHAMPION_GROUP_TYPE_ZONE {
				serverGroupMap := GetServerGroupMgr().GetServerZoneMap()
				for serverid, serverGroup := range serverGroupMap {
					if serverGroup == self.ZoneId {
						core.GetCenterApp().AddEvent(serverid, core.CHAMPION_ZONE_EVENT_FIGHT_UPDATE, 0,
							0, 0, msgdata)
					}
				}
			} else if config.GroupType == CHAMPION_GROUP_TYPE_GROUP {
				serverGroupMap := GetServerGroupMgr().GetServerGroupMap()
				for serverid, serverGroup := range serverGroupMap {
					if serverGroup == self.ZoneId {
						core.GetCenterApp().AddEvent(serverid, core.CHAMPION_ZONE_EVENT_FIGHT_UPDATE, 0,
							0, 0, msgdata)
					}
				}
			}
			//core.GetPlayerMgr().BroadcastMsg(msgRel.Cid, utils.HF_JtoB(&msgRel))
			self.DataEliminateInfo.SetTempBetResult(1)
		}
	}
}

func (self *ChampionMgr) GetNowChampionScoreInfo() *ScoreMatchInfo {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	scoreInfo, ok := self.DataScoreInfo.ScoreMatchInfo[self.DataScoreInfo.ScoreTurn]
	if !ok {
		return nil
	}
	return scoreInfo
}
func (self *ChampionMgr) GetChampionScoreUser() []*ScoreTurnUser {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	return self.DataScoreInfo.ScoreUser
}
func (self *ChampionMgr) GetChampionEliminateUser() []*EliminateUser {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	return self.DataEliminateInfo.EliminateUser
}
func (self *ChampionMgr) ChampionScoreOnTimer() {

	championScoreInfo := self.GetNowChampionScoreInfo()
	if championScoreInfo == nil {
		utils.LogDebug("ChampionScoreOnTimer:当前赛场数据错误")
		return
	}
	nowTime := model.TimeServer().Unix()
	switch championScoreInfo.ScoreMatchStage {
	case SCOREMATCH_STAGE_BET:
		if nowTime < championScoreInfo.BetEnd {
			return
		}
		self.ChangeScoreMatchStage(championScoreInfo, SCOREMATCH_STAGE_BATTLE)
	case SCOREMATCH_STAGE_BATTLE:
		if nowTime < championScoreInfo.BattleEnd {
			return
		}
		self.ChangeScoreMatchStage(championScoreInfo, SCOREMATCH_STAGE_END)
	case SCOREMATCH_STAGE_END:
		self.ChangeScoreMatchStage(championScoreInfo, SCOREMATCH_STAGE_BET)
	}
	return
}
func (self *ChampionMgr) ChangeScoreMatchStage(scoreMatchInfo *ScoreMatchInfo, stage int) {
	if scoreMatchInfo == nil {
		return
	}
	scoreMatchInfo.ScoreMatchStage = stage
	needSyn := true
	//delay := int64(60 * (core.GetZoneApp().GetConfig().ServerId % 3))
	//delay := int64(0)
	switch scoreMatchInfo.ScoreMatchStage {
	case SCOREMATCH_STAGE_BET:
		needSyn = self.ChampionScoreNext(scoreMatchInfo.BattleEnd)
	case SCOREMATCH_STAGE_BATTLE:
		//战斗加入战斗服
		self.FightList = new(sync.Map)
		for _, v := range scoreMatchInfo.ScoreMatchTeam {
			attackFightInfo := self.GetFightInfo(v.AttackUid)
			defenceFightInfo := self.GetFightInfo(v.DefenceUid)
			//_attack, _defence := models.ReBuildJSFightInfoByCondition(attackFightInfo, defenceFightInfo)
			//attackFightInfo = _attack
			//defenceFightInfo = _defence
			if attackFightInfo == nil || defenceFightInfo == nil {
				utils.LogDebug("数据异常:ChangeScoreMatchStage 分组时，战斗结构找不到")
				continue
			}
			ret := battle.GetFightMgr().AddFightID(attackFightInfo, defenceFightInfo, v.Random, 0, 0, core.BATTLE_TYPE_CHAMPIONZONE, 0)
			v.BattleId = ret
			battleData := &model.ArenaFightList{
				Type:    0,
				FightId: ret,
				Random:  int64(v.Random),
				Time:    model.TimeServer().Unix(),
				Attack:  attackFightInfo,
				Defend:  defenceFightInfo,
				BossId:  0}
			self.FightList.Store(battleData.FightId, battleData)
		}
	case SCOREMATCH_STAGE_END:
		//结算信息
		self.HandleScoreFightResult(scoreMatchInfo)
		//发放奖励
		self.SendBetResult(scoreMatchInfo)
		self.MakeTop()
	}
	if needSyn {
		self.Locker.Lock()
		self.Save()
		self.Locker.Unlock()
		self.SendSynMessage()
	}
}

// 发放 游戏
func (self *ChampionMgr) SendBetSave(value int) {
	for _, v := range self.ChampionInfo.MapBetInfo {
		v.NowBet += value
	}
}
func (self *ChampionMgr) SendBetResult(scoreMatchInfo *ScoreMatchInfo) {
	utils.LogDebug("SendBetResult结算下注")
	if scoreMatchInfo == nil {
		return
	}
	self.Locker.Lock()
	defer self.Locker.Unlock()

	stage := 0
	scoreTurn := self.DataScoreInfo.ScoreTurn

	teamInfo := scoreMatchInfo.GetTeam(scoreMatchInfo.BetTeamId)
	if teamInfo == nil {
		utils.LogError("SendBetResult找不到目标组")
		return
	}

	battleInfoData, ok := self.MapBattleInfo.Load(teamInfo.BattleId)
	if !ok {
		return
	}
	battleInfo := battleInfoData.(*model.BattleInfo)

	winUid := int64(0)
	if battleInfo.Result == model.ATTACK_WIN {
		winUid = teamInfo.AttackUid
	} else {
		winUid = teamInfo.DefenceUid
	}
	if winUid == 0 {
		return
	}
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return
	}
	serverRecord := make(map[int][]*ChampionBetRecord)
	for _, v := range self.ChampionInfo.MapBetInfo {
		if v.TargetUid == 0 {
			continue
		}
		betResult := model.LOGIC_FALSE
		betChange := 0
		if v.TargetUid == winUid {
			betChange = int(math.Ceil(float64(v.TargetBet*config.BetWin) / float64(100)))
			betResult = model.LOGIC_TRUE
		} else {
			betChange = int(math.Ceil(float64(v.TargetBet*config.BetLose) / float64(100)))
		}
		v.NowBet += betChange
		oldTargetUid := v.TargetUid
		oldTargetBet := v.TargetBet
		v.TargetUid = 0
		v.TargetBet = 0

		_, ok := serverRecord[v.ServerId]
		if !ok {
			serverRecord[v.ServerId] = make([]*ChampionBetRecord, 0)
		}
		//下注记录，根据比赛玩法，下注的玩家就算下线了这个时间点也一定在内存里
		betRecord := new(BetRecord)
		betRecord.TeamInfo = self.CheckMsgBattleInfo(teamInfo)
		betRecord.TargetUid = oldTargetUid
		betRecord.TargetBet = oldTargetBet
		betRecord.BetResult = betResult
		betRecord.BetChange = betChange
		betRecord.Stage = stage
		betRecord.ScoreTurn = scoreTurn
		data := new(ChampionBetRecord)
		data.Uid = v.Uid
		data.BetRecord = betRecord
		serverRecord[v.ServerId] = append(serverRecord[v.ServerId], data)
	}

	for serverid, v := range serverRecord {
		msgdata := utils.Lz4Encode(v)
		core.GetCenterApp().AddEvent(serverid, core.CHAMPION_ZONE_EVENT_BET_RECORD, 0,
			0, self.ActType, msgdata)
	}
}

func (self *ChampionMgr) SendBetResultEliminate(eliminateMatchInfo *EliminateMatchInfo) {

	utils.LogDebug("SendBetResultEliminate结算下注")
	if eliminateMatchInfo == nil {
		return
	}
	self.Locker.Lock()
	defer self.Locker.Unlock()
	stage := 0
	eliminateTurn := self.DataEliminateInfo.EliminateTurn

	teamInfo := eliminateMatchInfo.GetTeam(eliminateMatchInfo.BetTeamId)
	if teamInfo == nil {
		utils.LogError("SendBetResultEliminate找不到组")
		return
	}
	battleInfoData, ok := self.MapBattleInfo.Load(teamInfo.BattleId)
	if !ok {
		return
	}
	battleInfo := battleInfoData.(*model.BattleInfo)
	winUid := int64(0)
	if battleInfo.Result == model.ATTACK_WIN {
		winUid = teamInfo.AttackUid
	} else {
		winUid = teamInfo.DefenceUid
	}
	if winUid == 0 {
		return
	}
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return
	}
	serverRecord := make(map[int][]*ChampionBetRecord)
	for _, v := range self.ChampionInfo.MapBetInfo {
		if v.TargetUid == 0 {
			continue
		}
		betResult := model.LOGIC_FALSE
		betChange := 0
		if v.TargetUid == winUid {
			betChange = int(math.Ceil(float64(v.TargetBet * config.BetWin / 100)))
			betResult = model.LOGIC_TRUE
		} else {
			betChange = int(math.Ceil(float64(v.TargetBet * config.BetLose / 100)))
		}
		//
		v.NowBet += betChange
		oldTargetUid := v.TargetUid
		oldTargetBet := v.TargetBet
		v.TargetUid = 0
		v.TargetBet = 0

		_, ok := serverRecord[v.ServerId]
		if !ok {
			serverRecord[v.ServerId] = make([]*ChampionBetRecord, 0)
		}
		//下注记录，根据比赛玩法，下注的玩家就算下线了这个时间点也一定在内存里
		betRecord := new(BetRecord)
		betRecord.TeamInfo = self.CheckMsgBattleInfo(teamInfo)
		betRecord.TargetUid = oldTargetUid
		betRecord.TargetBet = oldTargetBet
		betRecord.BetResult = betResult
		betRecord.BetChange = betChange
		betRecord.Stage = stage
		betRecord.EliminateTurn = eliminateTurn
		data := new(ChampionBetRecord)
		data.Uid = v.Uid
		data.BetRecord = betRecord
		serverRecord[v.ServerId] = append(serverRecord[v.ServerId], data)
		//var msgRel protocol.S2C_ChampionUpdateBet
		//msgRel.Cid = CHAMPION_UPDATEBET
		//msgRel.Reason = CHAMPION_MATCH_BET_RESULT
		//msgRel.TargetUid = v.TargetUid
		//msgRel.TargetBet = v.TargetBet
		//msgRel.NowBet = v.NowBet
		//msgRel.BetResult = betResult
		//msgRel.BetChange = betChange
		//targetPlayer.SendMsg(msgRel.Cid, utils.HF_JtoB(&msgRel))
	}

	for serverid, v := range serverRecord {
		msgdata := utils.Lz4Encode(v)
		core.GetCenterApp().AddEvent(serverid, core.CHAMPION_ZONE_EVENT_BET_RECORD, 0,
			0, self.ActType, msgdata)
	}
}

func (self *ChampionMgr) GetStageParam() int {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	stage := self.ChampionInfo.Stage
	switch stage {
	case CHAMPION_STAGE_SCORE:
		return stage*100 + self.DataScoreInfo.ScoreTurn
	case CHAMPION_STAGE_ELIMINATE:
		return stage*100 + self.DataEliminateInfo.EliminateTurn
	}
	return 0
}
func (self *ChampionMgr) HandleScoreFightResult(scoreMatchInfo *ScoreMatchInfo) {
	resultBattleInfo := make(map[int64]*model.BattleInfo)
	resultScore := make(map[int64]int)
	resultScoreWinTimes := make(map[int64]int)
	stageParam := self.GetStageParam()

	//记录结果
	self.Locker.Lock()
	defer self.Locker.Unlock()

	for _, v := range self.DataScoreInfo.ScoreUser {
		resultScore[v.Uid] = v.Score
	}
	deleteList := make([]int64, 0)
	self.FightList.Range(func(key, battleData interface{}) bool {
		if battleData == nil {
			deleteList = append(deleteList, key.(int64))
			return true
		}
		v := battleData.(*model.ArenaFightList)
		FightResult := battle.GetFightMgr().GetFightResult(v.FightId)
		// 有结果,设置战斗时间
		if FightResult == nil {
			utils.LogError("冠军赛,战斗数据丢失")
			return true
		}
		isBattleServer := model.LOGIC_FALSE

		self.ScoreTimeAll[stageParam]++
		if FightResult.Result == 0 {
			utils.LogError(fmt.Sprintf("冠军赛战斗结果,服务器生成:%d", v))
			//生成统计
			FightResult.Info[model.POS_ATTACK] = model.GetFightHero(FightResult.Fight[model.POS_ATTACK])
			FightResult.Info[model.POS_DEFENCE] = model.GetFightHero(FightResult.Fight[model.POS_DEFENCE])
			// 战斗结果超时 对比战斗力
			if FightResult.Fight[0].Deffight > FightResult.Fight[1].Deffight {
				FightResult.Result = model.ATTACK_WIN
				for index := range FightResult.Info[model.POS_ATTACK] {
					FightResult.Info[model.POS_ATTACK][index].Hp = model.PER_BIT
				}
			} else {
				FightResult.Result = model.DEFENCE_WIN
				for index := range FightResult.Info[model.POS_DEFENCE] {
					FightResult.Info[model.POS_DEFENCE][index].Hp = model.PER_BIT
				}
			}
		} else {
			self.ScoreTime[stageParam]++
			utils.LogError(fmt.Sprintf("冠军赛战斗结果,战斗服生成:%d", v))
			isBattleServer = model.LOGIC_TRUE
		}
		//战报
		battleInfo := new(model.BattleInfo)
		battleInfo.Id = FightResult.Id
		battleInfo.StageParam = stageParam
		battleInfo.LevelID = 600001
		attackHeroInfo := []*model.BattleHeroInfo{}
		for i, v := range FightResult.Info[model.POS_ATTACK] {
			level, star, skin, exclusiveLv := 0, 0, 0, 0
			if i < len(FightResult.Fight[model.POS_ATTACK].Heroinfo) {
				level = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Levels
				star = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Stars
				skin = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Skin
				exclusiveLv = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].HeroExclusiveLv
			}
			attackHeroInfo = append(attackHeroInfo, &model.BattleHeroInfo{v.Heroid, level, star, skin, v.Hp, v.Energy, v.Damage, v.TakeDamage, v.Healing, nil, exclusiveLv, nil})
		}
		defendHeroInfo := []*model.BattleHeroInfo{}
		for i, v := range FightResult.Info[model.POS_DEFENCE] {
			level, star, skin, exclusiveLv := 0, 0, 0, 0
			if i < len(FightResult.Fight[model.POS_DEFENCE].Heroinfo) {
				level = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Levels
				star = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Stars
				skin = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Skin
				exclusiveLv = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].HeroExclusiveLv

			}
			defendHeroInfo = append(defendHeroInfo, &model.BattleHeroInfo{v.Heroid, level, star, skin, v.Hp, v.Energy, v.Damage, v.TakeDamage, v.Healing, nil, exclusiveLv, nil})
		}
		battleInfo.UserInfo[model.POS_ATTACK] = NewBattleUserInfo(FightResult.Fight[model.POS_ATTACK], attackHeroInfo)
		battleInfo.UserInfo[model.POS_DEFENCE] = NewBattleUserInfo(FightResult.Fight[model.POS_DEFENCE], defendHeroInfo)
		//battleInfo.Type = core.BATTLE_TYPE_PVP
		battleInfo.LevelID = 600006
		battleInfo.Time = int64(FightResult.Time)
		battleInfo.Random = int64(FightResult.Random)
		//进攻方胜利
		battleInfo.Result = FightResult.Result
		battleInfo.ResultType = isBattleServer

		resultBattleInfo[FightResult.Fight[model.POS_ATTACK].Uid] = battleInfo
		resultBattleInfo[FightResult.Fight[model.POS_DEFENCE].Uid] = battleInfo

		if battleInfo.Result == model.ATTACK_WIN {
			changeScore := self.GetChangeScore(battleInfo, battleInfo.UserInfo[0])
			targetUid := FightResult.Fight[model.POS_ATTACK].Uid
			resultScore[targetUid] += changeScore
			resultScoreWinTimes[targetUid] += 1
			battleInfo.UserInfo[model.POS_ATTACK].Param1 = resultScore[FightResult.Fight[model.POS_ATTACK].Uid]
			battleInfo.UserInfo[model.POS_ATTACK].Score = changeScore
		} else {
			changeScore := self.GetChangeScore(battleInfo, battleInfo.UserInfo[1])
			targetUid := FightResult.Fight[model.POS_DEFENCE].Uid
			resultScore[targetUid] += changeScore
			resultScoreWinTimes[targetUid] += 1
			battleInfo.UserInfo[model.POS_DEFENCE].Param1 = resultScore[FightResult.Fight[model.POS_DEFENCE].Uid]
			battleInfo.UserInfo[model.POS_DEFENCE].Score = changeScore
		}

		battleReocrd := model.BattleRecord{}
		battleReocrd.Level = 0
		battleReocrd.Side = 1
		battleReocrd.Time = model.TimeServer().Unix()
		battleReocrd.Id = battleInfo.Id
		battleReocrd.LevelID = battleInfo.LevelID
		//data2.Result = result
		battleReocrd.Type = core.BATTLE_TYPE_CHAMPIONZONE
		battleReocrd.RandNum = battleInfo.Random
		battleReocrd.FightInfo[0] = FightResult.Fight[0]
		battleReocrd.FightInfo[1] = FightResult.Fight[1]
		battleReocrd.LevelID = 600006
		db.HMSetRedisEx(BATTLE_INFO_CHAMPION, battleInfo.Id, &battleInfo, utils.DAY_SECS*4)
		db.HMSetRedisEx(BATTLE_RECORD_CHAMPION, battleReocrd.Id, &battleReocrd, utils.DAY_SECS*4)
		battle.GetFightMgr().DelResult(FightResult.Id)
		return true
	})
	for _, v := range self.DataScoreInfo.ScoreUser {
		_, ok := resultBattleInfo[v.Uid]
		if !ok {
			continue
		}
		v.BattleInfo = append(v.BattleInfo, resultBattleInfo[v.Uid].Id)
		v.Score = resultScore[v.Uid]
		v.ScoreWinTimes += resultScoreWinTimes[v.Uid]
	}
	for _, v := range resultBattleInfo {
		self.MapBattleInfo.Store(v.Id, v)
	}
	return
}

// 初始化时间
func (self *ChampionMgr) InitData() {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return
	}

	self.ChampionInfo.Period += 1
	self.DataScoreInfo = ChampionScoreInfo{}
	self.DataEliminateInfo = ChampionEliminateInfo{}
	self.ChampionInfo.MapBetInfo = make(map[int64]*ChampionBetInfo)
	self.ChampionInfo.SliceTopRank = make([]*ChampionTop, 0)

	nowTime := model.TimeServer()
	currentYear, currentMonth, currentDay := nowTime.Date()
	currentLocation := nowTime.Location()

	self.ChampionInfo.SignTime = time.Date(currentYear, currentMonth, currentDay, config.ChampionStartHour, config.ChampionStartMinute, 0, 0, currentLocation).Unix()
	self.ChampionInfo.EndTime = self.ChampionInfo.SignTime + CHAMPION_START_TIME_DIS_END
	//如果这个时间小于现在，更改计算的基准时间
	if self.ChampionInfo.EndTime < nowTime.Unix() {
		nowTime = nowTime.AddDate(0, 0, 1)
		currentYear, currentMonth, currentDay = nowTime.Date()
		currentLocation = nowTime.Location()
		self.ChampionInfo.SignTime = time.Date(currentYear, currentMonth, currentDay, config.ChampionStartHour, config.ChampionStartMinute, 0, 0, currentLocation).Unix()
	}

	judgeIndex := int64(nowTime.Weekday())
	dis := int64(7)
	for _, v := range GetServerGroupMgr().TimeResetConfigArr {
		if v.System != config.TimeReset {
			continue
		}
		for _, index := range v.Time {
			if index == 0 {
				continue
			}
			indexDis := (index - judgeIndex + 7) % 7
			if indexDis < dis {
				dis = indexDis
			}
		}
	}
	//生成新的数据
	self.ChampionInfo.SignTime += dis * utils.DAY_SECS
	self.ChampionInfo.Stage = CHAMPION_STAGE_INVALID
	self.ChampionInfo.ScoreTime = self.ChampionInfo.SignTime + config.StartTime
	self.ChampionInfo.EndTime = self.ChampionInfo.SignTime + config.EndTime
	self.InitBattleInfoSafe()

	//根据开始时间 生成预告时间

	nextJudgeTime := time.Unix(self.ChampionInfo.SignTime, 0)

	nextJudgeTime = nextJudgeTime.AddDate(0, 0, 1)
	currentYear, currentMonth, currentDay = nextJudgeTime.Date()
	currentLocation = nextJudgeTime.Location()
	self.ChampionInfo.NextTime = time.Date(currentYear, currentMonth, currentDay, config.ChampionStartHour, config.ChampionStartMinute, 0, 0, currentLocation).Unix()

	nextJudgeIndex := int64(nextJudgeTime.Weekday())
	nextDis := int64(7)
	for _, v := range GetServerGroupMgr().TimeResetConfigArr {
		if v.System != config.TimeReset {
			continue
		}
		for _, index := range v.Time {
			if index == 0 {
				continue
			}
			indexDis := (index - nextJudgeIndex + 7) % 7
			if indexDis < nextDis {
				nextDis = indexDis
			}
		}
	}
	self.ChampionInfo.NextTime += nextDis * utils.DAY_SECS
	self.ChampionInfo.NextSignTime = self.ChampionInfo.NextTime + config.StartTime
	self.ChampionInfo.NextEndTime = self.ChampionInfo.NextTime + config.EndTime
	self.ScoreTime = make(map[int]int)
	self.ScoreTimeAll = make(map[int]int)
	self.EliminateTime = make(map[int]int)
	self.EliminateAll = make(map[int]int)
	self.Save()
	return
}

func (self *ChampionMgr) MakeHistory() {
	if self.ChampionInfo.Period == 0 {
		return
	}
	self.ChampionInfo.DataHistoryInfo = new(ChampionHistoryInfo)
	self.ChampionInfo.DataHistoryInfo.Period = self.ChampionInfo.Period
	self.ChampionInfo.DataHistoryInfo.StartTime = self.ChampionInfo.ScoreTime
	self.ChampionInfo.DataHistoryInfo.EndTime = self.ChampionInfo.EndTime
	self.ChampionInfo.DataHistoryInfo.AllRank = self.ChampionInfo.SliceTopRank
	for _, v := range self.ChampionInfo.DataHistoryInfo.AllRank {
		fightInfo := self.GetFightInfoSave(v.Uid)
		if fightInfo == nil {
			continue
		}
		self.ChampionInfo.DataHistoryInfo.TopRank = append(self.ChampionInfo.DataHistoryInfo.TopRank, fightInfo)
		if len(self.ChampionInfo.DataHistoryInfo.TopRank) >= 3 {
			break
		}
	}
}

// 锁需要加再外层
func (self *ChampionMgr) Save() {
	self.ChampionInfo.Encode()
	self.ChampionInfo.Update(true, false)

	//for _, v := range self.ChampionFightInfo {
	//	v.Save()
	//	v.NeedSave = false
	//}
	var limitTimeSecs int64 = utils.MIN_SECS * 30
	now := model.TimeServer().Unix()
	self.ChampionFightInfo.Range(func(key, value interface{}) bool {
		info := value.(*FightInfoCache)
		info.Save()
		info.NeedSave = false
		if now > info.LastLiveTime && (now-info.LastLiveTime) > limitTimeSecs {
			self.ChampionFightInfo.Delete(key)
		}
		return true
	})

	//拆分存储
	self.SaveScoreInfo()
	self.SaveEliminateInfo()
}

func (self *ChampionMgr) SaveScoreInfo() {
	scoreMatchInfo, ok := self.DataScoreInfo.ScoreMatchInfo[self.DataScoreInfo.ScoreTurn]
	if !ok {
		return
	}
	self.ScoreMatchEncode(scoreMatchInfo)
	scoreMatchInfo.Update(true, false)
}

func (self *ChampionMgr) SaveEliminateInfo() {
	eliminateMatchInfo, ok := self.DataEliminateInfo.EliminateMatchInfo[self.DataEliminateInfo.EliminateTurn]
	if !ok {
		return
	}
	self.EliminateMatchEncode(eliminateMatchInfo)
	eliminateMatchInfo.Update(true, false)
}

// 切换阶段
func (self *ChampionMgr) ChangeStage(stage int) {
	self.ChampionInfo.Stage = stage
	//可能需要广播
	switch self.ChampionInfo.Stage {
	case CHAMPION_STAGE_INVALID:
	case CHAMPION_STAGE_SIGN:
		//生成数据
		self.InitChampionScore()
	case CHAMPION_STAGE_SCORE:
		//生成第一轮数据
		self.ChampionScoreNext(self.ChampionInfo.ScoreTime)
	case CHAMPION_STAGE_ELIMINATE:
		self.InitChampionEliminate()
		//生成第一轮数据
		self.ChampionEliminateNext(self.ChampionInfo.EliminateTime)
	case CHAMPION_STAGE_END:
		//生成最终排行
		self.MakeTop()
		//发放排行奖励
		self.SendRankReward()
		//发放下注奖励
		self.SendBetReward()
		//生成历史记录
		self.MakeHistory()
	case CHAMPION_STAGE_NEXT:
	default:
	}
	self.Locker.Lock()
	self.Save()
	self.Locker.Unlock()
	self.SendSynMessage()
}

func (self *ChampionMgr) getFightInfoCache(uid int64) (*FightInfoCache, bool) {
	info, ok := self.ChampionFightInfo.Load(uid)
	if !ok || info.(*FightInfoCache).FightInfoCacheDB == nil {
		if _ok, _data := self.loadUserChampionFightInfo(uid); _ok && _data != nil {
			return _data, true
		}
		return nil, false
	}
	info.(*FightInfoCache).LastLiveTime = model.TimeServer().Unix()
	return info.(*FightInfoCache), true
}

func (self *ChampionMgr) GetFightInfoSave(uid int64) *model.JS_FightInfo {
	info, ok := self.getFightInfoCache(uid)
	if !ok || info.FightInfoCacheDB == nil {
		return nil
	}
	return info.FightInfoCacheDB.GetFightInfo()
}

func (self *ChampionMgr) GetFightInfo(uid int64) *model.JS_FightInfo {
	info, ok := self.getFightInfoCache(uid)
	if !ok || info.FightInfoCacheDB == nil {
		return nil
	}
	return info.FightInfoCacheDB.GetFightInfo()
}

func (self *ChampionMgr) CanUpdateTeam() bool {
	switch self.ChampionInfo.Stage {
	case CHAMPION_STAGE_SCORE:
		scoreMatchInfo, ok := self.DataScoreInfo.ScoreMatchInfo[self.DataScoreInfo.ScoreTurn]
		if ok {
			return scoreMatchInfo.ScoreMatchStage != SCOREMATCH_STAGE_BATTLE
		}
	case CHAMPION_STAGE_ELIMINATE:
		eliminateMatchInfo, ok := self.DataEliminateInfo.EliminateMatchInfo[self.DataEliminateInfo.EliminateTurn]
		if ok {
			return eliminateMatchInfo.EliminateMatchStage != SCOREMATCH_STAGE_BATTLE
		}
	}
	return true
}

func (self *ChampionMgr) UpdateFightInfoCache(fightInfo *model.JS_FightInfo) {
	if !self.CanUpdateTeam() {
		return
	}
	self.Locker.Lock()
	defer self.Locker.Unlock()
	if fightInfo == nil {
		return
	}
	info, ok := self.getFightInfoCache(fightInfo.Uid)
	if !ok {
		data := new(FightInfoCache)
		data.NeedSave = false
		data.LastLiveTime = model.TimeServer().Unix()
		data.FightInfoCacheDB = new(FightInfoCacheDB)
		data.FightInfoCacheDB.Uid = fightInfo.Uid
		data.FightInfoCacheDB.ActType = self.ActType
		data.FightInfoCacheDB.SetFightInfo(fightInfo)
		data.FightInfoCacheDB.Encode()
		db.InsertTable(TABLE_NAME_CHAMPION_FIGHTINFO, data.FightInfoCacheDB, 0, false)
		data.FightInfoCacheDB.Init(TABLE_NAME_CHAMPION_FIGHTINFO, data.FightInfoCacheDB, false)
		self.ChampionFightInfo.Store(fightInfo.Uid, data)
		return
	}
	info.FightInfoCacheDB.SetFightInfo(fightInfo)
	info.NeedSave = true

	for _, v := range self.DataScoreInfo.ScoreUser {
		if v.Uid == fightInfo.Uid {
			self.NeedSyn = append(self.NeedSyn, fightInfo.Uid)
		}
	}
	return
}

// 初始化积分赛数据
func (self *ChampionMgr) InitChampionScore() {
	// todo  参赛人员
	//playerList := GetArenaMgr().GetChampionSign()
	playerList := make([]*model.JS_FightInfo, 0)
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return
	}
	if config.Type == CHAMPION_ZONE_TYPE_1 {
		playerList = GetCrossServerLevelArenaMgr().GetChampionSign(config.FirstPlayerMax, self.ZoneId)
	}
	//不足的部分补充机器人
	needAdd := config.FirstPlayerMax - len(playerList)
	startUid := int64(10000)
	for i := 0; i < needAdd; i++ {
		robotFightInfo := GetCrossServerLevelArenaMgr().GetRobotFightInfo(1)
		if robotFightInfo == nil {
			continue
		}
		startUid--
		robotFightInfo.Uid = startUid
		robotFightInfo.Param = int64(config.FirstPlayerMax) //机器人默认128名，排玩家后面
		playerList = append(playerList, robotFightInfo)
	}
	//保存阵容
	self.NeedSyn = make([]int64, 0)
	for index, v := range playerList {
		//这里直接拿冠军赛阵容,如果拿不到则覆盖竞技场阵容到冠军赛
		championFightInfo := self.GetFightInfo(v.Uid)
		//老号不会上传
		if self.ChampionInfo.Period == 1 || championFightInfo == nil {
			championFightInfo = v
			self.UpdateFightInfoCache(championFightInfo)
		}
		championFightInfo.Param = int64(index + 1)
		playerList[index] = championFightInfo
		self.NeedSyn = append(self.NeedSyn, v.Uid)
	}
	self.SetScoreInfo(playerList)
	return
}

// 设置初始阵容
func (self *ChampionMgr) SetScoreInfo(playerList []*model.JS_FightInfo) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	self.DataScoreInfo = ChampionScoreInfo{}
	self.DataScoreInfo.ScoreMatchInfo = make(map[int]*ScoreMatchInfo)
	allFightInfo := int64(0)
	for _, v := range playerList {
		scoreTurnUser := NewScoreTurnUser(v)
		scoreTurnUser.ArenaRank = int(v.Param)
		allFightInfo += v.Deffight
		self.DataScoreInfo.ScoreUser = append(self.DataScoreInfo.ScoreUser, scoreTurnUser)
	}
	if len(self.DataScoreInfo.ScoreUser) > 0 {
		self.DataScoreInfo.UserFight = allFightInfo / int64(len(self.DataScoreInfo.ScoreUser))
	}
}

func (self *ChampionMgr) StageScoreEnd() bool {
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return false
	}
	if self.DataScoreInfo.ScoreTurn <= config.FirstTurn {
		return false
	}
	return true
}

func (self *ChampionMgr) StageEliminateEnd() bool {
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return false
	}
	if self.DataEliminateInfo.EliminateTurn <= config.SecondTurn {
		return false
	}
	return true
}

func (self *ChampionMgr) ChampionScoreNext(startTime int64) bool {
	self.DataScoreInfo.ScoreTurn++ //进入到下个阶段
	if self.StageScoreEnd() {
		return false
	}
	self.Locker.Lock()
	defer self.Locker.Unlock()
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return false
	}
	scoreTurnInfo := self.MakeScoreTurnInfo(self.DataScoreInfo.ScoreTurn, startTime, config)
	self.DataScoreInfo.ScoreMatchInfo[self.DataScoreInfo.ScoreTurn] = scoreTurnInfo
	self.SendBetSave(config.BetSend)
	return true
}

func (self *ChampionMgr) MakeScoreTurnInfo(turnId int, startTime int64, config *ChampionSimpleConfig) *ScoreMatchInfo {
	scoreMatchInfo := new(ScoreMatchInfo)
	scoreMatchInfo.ScoreTurnId = turnId
	scoreMatchInfo.Period = self.ChampionInfo.Period
	scoreMatchInfo.ActType = self.ActType
	scoreMatchInfo.ZoneId = self.ZoneId
	scoreMatchInfo.ScoreMatchStage = SCOREMATCH_STAGE_BET
	scoreMatchInfo.BetEnd = startTime + config.BetTime
	scoreMatchInfo.BattleEnd = scoreMatchInfo.BetEnd + config.BattleTime

	scoreMatchInfo.ScoreMatchTeam = make([]*ScoreMatchTeam, 0)
	teamId := 0
	betTeamId := 0
	fightMin := int64(0)
	fightAll := int64(0)

	temp := self.DataScoreInfo.ScoreUser
	sort.Slice(temp, func(i, j int) bool {
		if temp[i].Score == temp[j].Score {
			return temp[i].ArenaRank < temp[j].ArenaRank
		}
		return temp[i].Score >= temp[j].Score
	})
	//把人分为4档
	playerList := make(map[int]map[int64]int, 0)
	for index, v := range temp {
		groupId := (index / 32) + 1
		_, GroupOk := playerList[groupId]
		if !GroupOk {
			playerList[groupId] = make(map[int64]int, 0)
		}
		playerList[groupId][v.Uid] = model.LOGIC_TRUE
	}
	//从第4轮开始，连胜或连负的人调整档次
	if turnId >= 4 {
		for index, v := range temp {
			//连胜
			if v.ScoreWinTimes == turnId-1 {
				groupId := (index / 32) + 1
				targetGroupId := groupId - 1
				_, GroupOk := playerList[targetGroupId]
				if !GroupOk {
					continue
				}
				delete(playerList[groupId], v.Uid)
				playerList[targetGroupId][v.Uid] = model.LOGIC_TRUE
			} else if v.ScoreWinTimes == 0 {
				groupId := (index / 32) + 1
				targetGroupId := groupId + 1
				_, GroupOk := playerList[targetGroupId]
				if !GroupOk {
					continue
				}
				delete(playerList[groupId], v.Uid)
				playerList[targetGroupId][v.Uid] = model.LOGIC_TRUE
			}
		}
	}
	//开始匹配 从上到下
	//从第一档开始匹配
	nowGroup := 1
	for {
		_, ok := playerList[nowGroup]
		if !ok {
			break
		}
		//拿出数据
		attackList := make([]int64, 0)
		defenceList := make([]int64, 0)
		defenceMap := make(map[int64]int, 0)
		deleteList := make([]int64, 0)

		for groupId, players := range playerList {
			if groupId == nowGroup {
				for uid := range players {
					attackList = append(attackList, uid)
				}
			} else {
				for uid := range players {
					defenceMap[uid] = model.LOGIC_TRUE
				}
			}
		}
		for uid := range defenceMap {
			defenceList = append(defenceList, uid)
		}
		//这种情况直接进行调整,匹配完也就结束了
		attackSize := len(attackList)
		defenceSize := len(defenceList)
		if attackSize > defenceSize {
			correstSize := (attackSize - defenceSize) / 2
			defenceList = append(defenceList, attackList[attackSize-correstSize:]...)
			attackList = attackList[:attackSize-correstSize]
		}
		for i := 0; i < len(attackList); i++ {
			if i >= len(defenceList) {
				break
			}
			teamId++
			scoreMatchTeam := new(ScoreMatchTeam)
			scoreMatchTeam.TeamId = teamId
			scoreMatchTeam.AttackUid = attackList[i]
			scoreMatchTeam.DefenceUid = defenceList[i]
			scoreMatchTeam.Random = utils.HF_GetRandom(int(model.TimeServer().Unix()))
			scoreMatchInfo.ScoreMatchTeam = append(scoreMatchInfo.ScoreMatchTeam, scoreMatchTeam)

			attackFightInfo, _ := self.getFightInfoCache(scoreMatchTeam.AttackUid)
			defenceFightInfo, _ := self.getFightInfoCache(scoreMatchTeam.DefenceUid)
			if attackFightInfo != nil &&
				attackFightInfo.FightInfoCacheDB != nil &&
				attackFightInfo.FightInfoCacheDB.GetFightInfo() != nil &&
				defenceFightInfo != nil &&
				defenceFightInfo.FightInfoCacheDB != nil &&
				defenceFightInfo.FightInfoCacheDB.GetFightInfo() != nil {
				dis := int64(0)
				attackFight := attackFightInfo.FightInfoCacheDB.GetFightInfo().Deffight
				defenceFight := defenceFightInfo.FightInfoCacheDB.GetFightInfo().Deffight
				if attackFight > defenceFight {
					dis = attackFight - defenceFight
				} else {
					dis = defenceFight - attackFight
				}
				if betTeamId == 0 {
					betTeamId = scoreMatchTeam.TeamId
					fightMin = dis
				} else if dis < fightMin {
					betTeamId = scoreMatchTeam.TeamId
					fightMin = dis
				}
				fightAll += attackFight
				fightAll += defenceFight
			}
			deleteList = append(deleteList, scoreMatchTeam.AttackUid)
			deleteList = append(deleteList, scoreMatchTeam.DefenceUid)
		}
		//删除已经被选中的人
		for _, v := range deleteList {
			for _, players := range playerList {
				delete(players, v)
			}
		}
		nowGroup++
	}
	scoreMatchInfo.BetTeamId = betTeamId
	if len(playerList) > 0 {
		scoreMatchInfo.UserFight = fightAll / int64(len(playerList))
	}
	//utils.LogDebug("分组测试开始:%s", utils.HF_JtoA(self.DataScoreInfo.ScoreUser))
	//utils.LogDebug("分组测试结束:%s", utils.HF_JtoA(scoreMatchInfo))
	self.ScoreMatchEncode(scoreMatchInfo)
	dbId := db.InsertTable(TABLE_NAME_CHAMPION_SCORE_INFO, scoreMatchInfo, 0, false)
	scoreMatchInfo.Id = int(dbId)
	scoreMatchInfo.Init(TABLE_NAME_CHAMPION_SCORE_INFO, scoreMatchInfo, false)
	return scoreMatchInfo
}

func (self *ChampionMgr) ScoreMatchEncode(scoreMatchInfo *ScoreMatchInfo) {
	if scoreMatchInfo == nil {
		return
	}
	scoreMatchInfo.ScoreUser = self.DataScoreInfo.ScoreUser
	scoreMatchInfo.ScoreMatchTeamStr = utils.HF_JtoA(&scoreMatchInfo.ScoreMatchTeam)
	scoreMatchInfo.ScoreUserStr = utils.HF_JtoA(&scoreMatchInfo.ScoreUser)
	return
}

func (self *ChampionMgr) EliminateMatchEncode(eliminateMatchInfo *EliminateMatchInfo) {
	if eliminateMatchInfo == nil {
		return
	}
	eliminateMatchInfo.EliminateUser = self.DataEliminateInfo.EliminateUser
	eliminateMatchInfo.EliminateMatchTeamStr = utils.HF_JtoA(&eliminateMatchInfo.EliminateMatchTeam)
	eliminateMatchInfo.EliminateUserStr = utils.HF_JtoA(&eliminateMatchInfo.EliminateUser)
	return
}

func (self *ChampionMgr) GetScoreWinList() []*ScoreTurnUser {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	return self.DataScoreInfo.ScoreUser
}
func (self *ChampionMgr) InitChampionEliminate() {
	scoreList := self.GetScoreWinList()
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return
	}
	if len(scoreList) < config.SecondPlayerMax {
		utils.LogError("InitChampionEliminate：冠军赛淘汰赛，晋级选手数量出现异常")
		return
	}
	//根据积分排序 选出目标
	sort.Slice(scoreList, func(i, j int) bool {
		if scoreList[i].Score == scoreList[j].Score {
			return scoreList[i].ArenaRank < scoreList[j].ArenaRank
		}
		return scoreList[i].Score >= scoreList[j].Score
	})
	eliminateList := scoreList[:config.SecondPlayerMax]
	//utils.LogDebug(eliminateList)
	//淘汰赛分组
	self.SetEliminateInfo(eliminateList)
	return
}

func (self *ChampionMgr) SetEliminateInfo(scoreWin []*ScoreTurnUser) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	self.DataEliminateInfo = ChampionEliminateInfo{}
	self.DataEliminateInfo.EliminateMatchInfo = make(map[int]*EliminateMatchInfo)
	for _, v := range scoreWin {
		eliminateUser := NewEliminateUser(v)
		self.DataEliminateInfo.EliminateUser = append(self.DataEliminateInfo.EliminateUser, eliminateUser)
	}
	//根据积分赛结果排序
	sort.Slice(self.DataEliminateInfo.EliminateUser, func(i, j int) bool {
		if self.DataEliminateInfo.EliminateUser[i].WinTimes == self.DataEliminateInfo.EliminateUser[j].WinTimes {
			if self.DataEliminateInfo.EliminateUser[i].Score == self.DataEliminateInfo.EliminateUser[j].Score {
				return self.DataEliminateInfo.EliminateUser[i].ArenaRank >= self.DataEliminateInfo.EliminateUser[j].ArenaRank
			}
			return self.DataEliminateInfo.EliminateUser[i].Score >= self.DataEliminateInfo.EliminateUser[j].Score
		}
		return self.DataEliminateInfo.EliminateUser[i].WinTimes >= self.DataEliminateInfo.EliminateUser[j].WinTimes
	})
	for index, v := range self.DataEliminateInfo.EliminateUser {
		v.KeyId = conf.MapChampionConfig[index+1].Location
	}
	//根据作为排序
	sort.Slice(self.DataEliminateInfo.EliminateUser, func(i, j int) bool {
		return self.DataEliminateInfo.EliminateUser[i].KeyId < self.DataEliminateInfo.EliminateUser[j].KeyId
	})
}

func (self *ChampionMgr) ChampionEliminateNext(startTime int64) bool {
	self.DataEliminateInfo.EliminateTurn++ //进入到下个阶段
	if self.StageEliminateEnd() {
		return false
	}
	self.Locker.Lock()
	defer self.Locker.Unlock()
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return false
	}
	eliminateTurnInfo := self.MakeEliminateInfo(self.DataEliminateInfo.EliminateTurn, startTime, config)
	self.DataEliminateInfo.EliminateMatchInfo[self.DataEliminateInfo.EliminateTurn] = eliminateTurnInfo
	self.SendBetSave(config.BetSend)
	return true
}

func (self *ChampionMgr) MakeEliminateInfo(turnId int, startTime int64, config *ChampionSimpleConfig) *EliminateMatchInfo {
	eliminateMatchInfo := new(EliminateMatchInfo)
	eliminateMatchInfo.EliminateTurnId = turnId
	eliminateMatchInfo.Period = self.ChampionInfo.Period
	eliminateMatchInfo.ZoneId = self.ZoneId
	eliminateMatchInfo.ActType = self.ActType
	eliminateMatchInfo.EliminateMatchStage = SCOREMATCH_STAGE_BET
	eliminateMatchInfo.BetEnd = startTime + config.BetTime
	eliminateMatchInfo.BattleEnd = eliminateMatchInfo.BetEnd + config.BattleTime

	eliminateMatchInfo.EliminateMatchTeam = make([]*ScoreMatchTeam, 0)
	already := make(map[int64]int)
	judgeWin := turnId - 1

	teamId := 0
	betTeamId := 0
	fightMin := int64(0)
	for i := 0; i < len(self.DataEliminateInfo.EliminateUser); i++ {
		eliminateUserAttack := self.DataEliminateInfo.EliminateUser[i]
		_, ok := already[eliminateUserAttack.Uid]
		if ok {
			continue
		}
		//被淘汰的选手
		if eliminateUserAttack.WinTimes < judgeWin {
			already[eliminateUserAttack.Uid] = model.LOGIC_TRUE
			continue
		}
		//寻找对手
		for j := i + 1; j < len(self.DataEliminateInfo.EliminateUser); j++ {
			eliminateUserDefence := self.DataEliminateInfo.EliminateUser[j]
			_, ok := already[eliminateUserDefence.Uid]
			if ok {
				continue
			}
			//被淘汰的选手
			if eliminateUserDefence.WinTimes < judgeWin {
				already[eliminateUserDefence.Uid] = model.LOGIC_TRUE
				continue
			}
			//生成分组
			teamId++
			eliminateMatchTeam := new(ScoreMatchTeam)
			eliminateMatchTeam.TeamId = teamId
			eliminateMatchTeam.AttackUid = eliminateUserAttack.Uid
			eliminateMatchTeam.DefenceUid = eliminateUserDefence.Uid
			eliminateMatchTeam.Random = utils.HF_GetRandom(int(model.TimeServer().Unix()))
			eliminateMatchInfo.EliminateMatchTeam = append(eliminateMatchInfo.EliminateMatchTeam, eliminateMatchTeam)

			already[eliminateUserAttack.Uid] = model.LOGIC_TRUE
			already[eliminateUserDefence.Uid] = model.LOGIC_TRUE

			//计算押注组
			attackFightInfo, _ := self.getFightInfoCache(eliminateMatchTeam.AttackUid)
			defenceFightInfo, _ := self.getFightInfoCache(eliminateMatchTeam.DefenceUid)
			if attackFightInfo != nil &&
				attackFightInfo.FightInfoCacheDB != nil &&
				attackFightInfo.FightInfoCacheDB.GetFightInfo() != nil &&
				defenceFightInfo != nil &&
				defenceFightInfo.FightInfoCacheDB != nil &&
				defenceFightInfo.FightInfoCacheDB.GetFightInfo() != nil {
				dis := int64(0)
				attackFight := attackFightInfo.FightInfoCacheDB.GetFightInfo().Deffight
				defenceFight := defenceFightInfo.FightInfoCacheDB.GetFightInfo().Deffight
				if attackFight > defenceFight {
					dis = attackFight - defenceFight
				} else {
					dis = defenceFight - attackFight
				}
				if betTeamId == 0 {
					betTeamId = eliminateMatchTeam.TeamId
					fightMin = dis
				} else if dis < fightMin {
					betTeamId = eliminateMatchTeam.TeamId
					fightMin = dis
				}
			}
			break
		}
	}
	eliminateMatchInfo.BetTeamId = betTeamId

	self.EliminateMatchEncode(eliminateMatchInfo)
	dbId := db.InsertTable(TABLE_NAME_CHAMPION_ELIMINATE_INFO, eliminateMatchInfo, 0, false)
	eliminateMatchInfo.Id = int(dbId)
	eliminateMatchInfo.Init(TABLE_NAME_CHAMPION_ELIMINATE_INFO, eliminateMatchInfo, false)
	return eliminateMatchInfo
}

func (self *ChampionMgr) ChampionEliminateOnTimer() {
	championEliminateInfo := self.GetNowChampionEliminateInfo()
	if championEliminateInfo == nil {
		utils.LogDebug("ChampionEliminateOnTimer:当前赛场数据错误")
		return
	}
	nowTime := model.TimeServer().Unix()
	switch championEliminateInfo.EliminateMatchStage {
	case SCOREMATCH_STAGE_BET:
		if nowTime < championEliminateInfo.BetEnd {
			return
		}
		self.ChangeEliminateMatchStage(championEliminateInfo, SCOREMATCH_STAGE_BATTLE)
	case SCOREMATCH_STAGE_BATTLE:
		if nowTime < championEliminateInfo.BattleEnd {
			return
		}
		self.ChangeEliminateMatchStage(championEliminateInfo, SCOREMATCH_STAGE_END)
	case SCOREMATCH_STAGE_END:
		self.ChangeEliminateMatchStage(championEliminateInfo, SCOREMATCH_STAGE_BET)
	}
	return
}

func (self *ChampionMgr) GetNowChampionEliminateInfo() *EliminateMatchInfo {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	eliminateInfo, ok := self.DataEliminateInfo.EliminateMatchInfo[self.DataEliminateInfo.EliminateTurn]
	if !ok {
		return nil
	}
	return eliminateInfo
}

// 返回是否有下一轮
func (self *ChampionMgr) ChangeEliminateMatchStage(eliminateMatchInfo *EliminateMatchInfo, stage int) {
	if eliminateMatchInfo == nil {
		return
	}
	eliminateMatchInfo.EliminateMatchStage = stage
	needSyn := true
	//delay := int64(60 * (core.GetZoneApp().GetConfig().ServerId % 3))
	//delay := int64(0)
	switch eliminateMatchInfo.EliminateMatchStage {
	case SCOREMATCH_STAGE_BET:
		needSyn = self.ChampionEliminateNext(eliminateMatchInfo.BattleEnd)
	case SCOREMATCH_STAGE_BATTLE:
		//战斗加入战斗服
		self.FightList = new(sync.Map)
		for _, v := range eliminateMatchInfo.EliminateMatchTeam {
			attackFightInfo := self.GetFightInfo(v.AttackUid)
			defenceFightInfo := self.GetFightInfo(v.DefenceUid)
			//_attack, _defence := models.ReBuildJSFightInfoByCondition(attackFightInfo, defenceFightInfo)
			//attackFightInfo = _attack
			//defenceFightInfo = _defence
			if attackFightInfo == nil || defenceFightInfo == nil {
				utils.LogDebug("数据异常:ChangeScoreMatchStage 分组时，战斗结构找不到")
				continue
			}
			ret := battle.GetFightMgr().AddFightID(attackFightInfo, defenceFightInfo, v.Random, 0, 0, core.BATTLE_TYPE_CHAMPIONZONE, 0)
			v.BattleId = ret
			battleData := &model.ArenaFightList{
				Type:    0,
				FightId: ret,
				Random:  int64(v.Random),
				Time:    model.TimeServer().Unix(),
				Attack:  attackFightInfo,
				Defend:  defenceFightInfo,
				BossId:  0}
			self.FightList.Store(battleData.FightId, battleData)
		}
	case SCOREMATCH_STAGE_END:
		//结算超时信息
		self.HandleEliminateFightResult(eliminateMatchInfo)
		self.SendBetResultEliminate(eliminateMatchInfo)
		self.MakeTop()
	}
	if needSyn {
		self.Locker.Lock()
		self.Save()
		self.Locker.Unlock()
		self.SendSynMessage()
	}
}

func (self *ChampionMgr) HandleEliminateFightResult(eliminateMatchInfo *EliminateMatchInfo) {
	resultBattleInfo := make(map[int64]*model.BattleInfo)
	resultWin := make(map[int64]int)
	stageParam := self.GetStageParam()
	deleteList := make([]int64, 0)

	if self.FightList == nil {
		return
	}

	self.FightList.Range(func(key, battleData interface{}) bool {
		if battleData == nil {
			deleteList = append(deleteList, key.(int64))
			return true
		}
		v := battleData.(*model.ArenaFightList)
		FightResult := battle.GetFightMgr().GetFightResult(v.FightId)
		// 有结果,设置战斗时间
		if FightResult == nil {
			utils.LogError("冠军赛,战斗数据丢失")
			return true
		}
		isBattleServer := model.LOGIC_FALSE
		self.EliminateAll[stageParam]++
		if FightResult.Result == 0 {
			utils.LogError(fmt.Sprintf("冠军赛战斗结果,服务器生成:%d", v))
			//生成统计
			FightResult.Info[model.POS_ATTACK] = model.GetFightHero(FightResult.Fight[model.POS_ATTACK])
			FightResult.Info[model.POS_DEFENCE] = model.GetFightHero(FightResult.Fight[model.POS_DEFENCE])
			// 战斗结果超时 对比战斗力
			if FightResult.Fight[0].Deffight > FightResult.Fight[1].Deffight {
				FightResult.Result = model.ATTACK_WIN
				for index := range FightResult.Info[model.POS_ATTACK] {
					FightResult.Info[model.POS_ATTACK][index].Hp = model.PER_BIT
				}
			} else {
				FightResult.Result = model.DEFENCE_WIN
				for index := range FightResult.Info[model.POS_DEFENCE] {
					FightResult.Info[model.POS_DEFENCE][index].Hp = model.PER_BIT
				}
			}
		} else {
			self.EliminateTime[stageParam]++
			utils.LogError(fmt.Sprintf("冠军赛战斗结果,战斗服生成:%d", v))
			isBattleServer = model.LOGIC_TRUE
		}
		//战报
		battleInfo := new(model.BattleInfo)
		battleInfo.Id = FightResult.Id
		battleInfo.StageParam = stageParam
		battleInfo.LevelID = 600001
		attackHeroInfo := []*model.BattleHeroInfo{}
		for i, v := range FightResult.Info[model.POS_ATTACK] {
			level, star, skin, exclusiveLv := 0, 0, 0, 0
			if i < len(FightResult.Fight[model.POS_ATTACK].Heroinfo) {
				level = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Levels
				star = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Stars
				skin = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Skin
				exclusiveLv = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].HeroExclusiveLv
			}
			attackHeroInfo = append(attackHeroInfo, &model.BattleHeroInfo{v.Heroid, level, star, skin, v.Hp, v.Energy, v.Damage, v.TakeDamage, v.Healing, nil, exclusiveLv, nil})
		}
		defendHeroInfo := []*model.BattleHeroInfo{}
		for i, v := range FightResult.Info[model.POS_DEFENCE] {
			level, star, skin, exclusiveLv := 0, 0, 0, 0
			if i < len(FightResult.Fight[model.POS_DEFENCE].Heroinfo) {
				level = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Levels
				star = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Stars
				skin = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Skin
				exclusiveLv = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].HeroExclusiveLv

			}
			defendHeroInfo = append(defendHeroInfo, &model.BattleHeroInfo{v.Heroid, level, star, skin, v.Hp, v.Energy, v.Damage, v.TakeDamage, v.Healing, nil, exclusiveLv, nil})
		}
		battleInfo.UserInfo[model.POS_ATTACK] = NewBattleUserInfo(FightResult.Fight[model.POS_ATTACK], attackHeroInfo)
		battleInfo.UserInfo[model.POS_DEFENCE] = NewBattleUserInfo(FightResult.Fight[model.POS_DEFENCE], defendHeroInfo)
		//battleInfo.Type = core.BATTLE_TYPE_PVP
		battleInfo.Time = int64(FightResult.Time)
		battleInfo.Random = int64(FightResult.Random)
		//进攻方胜利
		battleInfo.Result = FightResult.Result
		battleInfo.ResultType = isBattleServer

		resultBattleInfo[FightResult.Fight[model.POS_ATTACK].Uid] = battleInfo
		resultBattleInfo[FightResult.Fight[model.POS_DEFENCE].Uid] = battleInfo

		if battleInfo.Result == model.ATTACK_WIN {
			resultWin[FightResult.Fight[model.POS_ATTACK].Uid] += 1
		} else {
			resultWin[FightResult.Fight[model.POS_DEFENCE].Uid] += 1
		}

		battleReocrd := model.BattleRecord{}
		battleReocrd.Level = 0
		battleReocrd.Side = 1
		battleReocrd.Time = model.TimeServer().Unix()
		battleReocrd.Id = battleInfo.Id
		battleReocrd.LevelID = battleInfo.LevelID
		//data2.Result = result
		battleReocrd.Type = core.BATTLE_TYPE_CHAMPIONZONE
		battleReocrd.RandNum = battleInfo.Random
		battleReocrd.FightInfo[0] = FightResult.Fight[0]
		battleReocrd.FightInfo[1] = FightResult.Fight[1]
		db.HMSetRedisEx(BATTLE_INFO_CHAMPION, battleInfo.Id, &battleInfo, utils.DAY_SECS*3)
		db.HMSetRedisEx(BATTLE_RECORD_CHAMPION, battleReocrd.Id, &battleReocrd, utils.DAY_SECS*3)
		battle.GetFightMgr().DelResult(FightResult.Id)
		return true
	})
	//记录结果
	self.Locker.Lock()
	defer self.Locker.Unlock()
	for _, v := range self.DataEliminateInfo.EliminateUser {
		_, ok := resultBattleInfo[v.Uid]
		if !ok {
			continue
		}
		v.BattleInfo = append(v.BattleInfo, resultBattleInfo[v.Uid].Id)
		v.WinTimes += resultWin[v.Uid]
	}
	for _, v := range resultBattleInfo {
		self.MapBattleInfo.Store(v.Id, v)
	}
	return
}

func (self *ChampionMgr) MakeTop() {
	self.Locker.Lock()
	defer self.Locker.Unlock()

	rankMap := make(map[int64]*ChampionTop)
	//加入积分赛的选手
	for _, v := range self.DataScoreInfo.ScoreUser {
		data := new(ChampionTop)
		data.Uid = v.Uid
		data.ServerId = v.ServerId
		data.Score = v.Score
		data.ArenaRank = v.ArenaRank
		rankMap[data.Uid] = data
	}
	//更新淘汰赛的选手记录
	for _, v := range self.DataEliminateInfo.EliminateUser {
		data, ok := rankMap[v.Uid]
		if !ok {
			continue
		}
		//data.BattleInfo = append(data.BattleInfo, v.BattleInfo...) //战报是否合并，由需求决定
		data.WinTimes = v.WinTimes
	}
	self.ChampionInfo.SliceTopRank = make([]*ChampionTop, 0)
	for _, v := range rankMap {
		self.ChampionInfo.SliceTopRank = append(self.ChampionInfo.SliceTopRank, v)
	}
	//淘汰赛开始后 只考虑淘汰赛成绩和竞技场排行
	sort.Slice(self.ChampionInfo.SliceTopRank, func(i, j int) bool {
		if self.ChampionInfo.SliceTopRank[i].WinTimes == self.ChampionInfo.SliceTopRank[j].WinTimes {
			return self.ChampionInfo.SliceTopRank[i].ArenaRank <= self.ChampionInfo.SliceTopRank[j].ArenaRank
		}
		return self.ChampionInfo.SliceTopRank[i].WinTimes >= self.ChampionInfo.SliceTopRank[j].WinTimes
	})
	for index, v := range self.ChampionInfo.SliceTopRank {
		v.RankPos = index + 1
	}
	return
}
func (self *ChampionMgr) SendRankReward() {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	topInfo := make(map[int][]*ChampionTop)

	for _, v := range self.ChampionInfo.SliceTopRank {
		if v.IsRobot() {
			continue
		}
		_, ok := topInfo[v.ServerId]
		if !ok {
			topInfo[v.ServerId] = make([]*ChampionTop, 0)
		}
		topInfo[v.ServerId] = append(topInfo[v.ServerId], v)
	}
	for serverid, v := range topInfo {
		msgdata := utils.HF_JtoA(v)
		core.GetCenterApp().AddEvent(serverid, core.CHAMPION_ZONE_EVENT_RANK_REWARD, 0,
			0, self.ActType, msgdata)
	}
	return
}

func (self *ChampionMgr) SendBetReward() {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	betInfo := make(map[int][]*ChampionBetInfo)
	for _, v := range self.ChampionInfo.MapBetInfo {
		_, ok := betInfo[v.ServerId]
		if !ok {
			betInfo[v.ServerId] = make([]*ChampionBetInfo, 0)
		}
		betInfo[v.ServerId] = append(betInfo[v.ServerId], v)
	}
	for serverid, v := range betInfo {
		msgdata := utils.HF_JtoA(v)
		core.GetCenterApp().AddEvent(serverid, core.CHAMPION_ZONE_EVENT_BET_REWARD, 0,
			0, self.ActType, msgdata)
	}
	for _, v := range self.ChampionInfo.MapBetInfo {
		v.NowBet = 0
	}

	return
}
func (self *ChampionMgr) GetChampionInfo() *ChampionInfo {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	return &self.ChampionInfo
}

func (self *ChampionMgr) GetChampionBetInfo(uid int64) *ChampionBetInfo {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	betInfo, ok := self.ChampionInfo.MapBetInfo[uid]
	if !ok {
		betInfo = new(ChampionBetInfo)
		betInfo.Uid = uid
		self.ChampionInfo.MapBetInfo[uid] = betInfo
	}
	return self.ChampionInfo.MapBetInfo[uid]
}

func (self *ChampionMgr) NewChampionBetInfo(player core.IPlayer) *ChampionBetInfo {
	stageParam := self.GetStageParam()

	self.Locker.Lock()
	defer self.Locker.Unlock()

	info, ok := self.ChampionInfo.MapBetInfo[player.GetUid()]
	if ok {
		return info
	}

	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return nil
	}
	betInfo := new(ChampionBetInfo)
	betInfo.Uid = player.GetUid()
	betInfo.ServerId = player.GetServerId()
	if stageParam > 0 {
		betInfo.NowBet = config.BetSend
	}
	self.ChampionInfo.MapBetInfo[betInfo.Uid] = betInfo
	return betInfo
}

func (self *ChampionMgr) GetFightInfoList() []*model.JS_FightInfo {
	//self.Locker.RLock()
	//defer self.Locker.RUnlock()
	fightInfoList := make([]*model.JS_FightInfo, 0)
	for _, v := range self.DataScoreInfo.ScoreUser {
		info, ok := self.getFightInfoCache(v.Uid)
		if !ok || info.FightInfoCacheDB == nil || info.FightInfoCacheDB.GetFightInfo() == nil {
			continue
		}
		fightInfoList = append(fightInfoList, info.FightInfoCacheDB.GetFightInfo())
	}

	return fightInfoList
}

func (self *ChampionMgr) GetSimpleInfo() []*JS_SimpleInfo {
	fightInfoList := make([]*JS_SimpleInfo, 0)
	for _, v := range self.DataScoreInfo.ScoreUser {
		info, ok := self.getFightInfoCache(v.Uid)
		if !ok || info.FightInfoCacheDB == nil || info.FightInfoCacheDB.GetFightInfo() == nil {
			continue
		}
		simpleInfo := new(JS_SimpleInfo)
		simpleInfo.Rankid = info.FightInfoCacheDB.GetFightInfo().Rankid
		simpleInfo.Uid = info.FightInfoCacheDB.GetFightInfo().Uid
		simpleInfo.Uname = info.FightInfoCacheDB.GetFightInfo().Uname
		simpleInfo.UnionName = info.FightInfoCacheDB.GetFightInfo().UnionName
		simpleInfo.UnionId = info.FightInfoCacheDB.GetFightInfo().UnionId
		simpleInfo.Iconid = info.FightInfoCacheDB.GetFightInfo().Iconid
		simpleInfo.Camp = info.FightInfoCacheDB.GetFightInfo().Camp
		simpleInfo.Level = info.FightInfoCacheDB.GetFightInfo().Level
		simpleInfo.Vip = info.FightInfoCacheDB.GetFightInfo().Vip
		simpleInfo.Deffight = info.FightInfoCacheDB.GetFightInfo().Deffight
		simpleInfo.FightTeam = info.FightInfoCacheDB.GetFightInfo().FightTeam
		simpleInfo.Portrait = info.FightInfoCacheDB.GetFightInfo().Portrait
		simpleInfo.Title = info.FightInfoCacheDB.GetFightInfo().Title
		simpleInfo.Server = info.FightInfoCacheDB.GetFightInfo().Server
		//simpleInfo.Score = info.FightInfoCacheDB.fightInfo.Score
		simpleInfo.Param = info.FightInfoCacheDB.GetFightInfo().Param
		fightInfoList = append(fightInfoList, simpleInfo)
	}

	return fightInfoList
}

func (self *ChampionMgr) GetFightInfoSyn() []*JS_SimpleInfo {
	//self.Locker.RLock()
	fightInfoList := make([]*JS_SimpleInfo, 0)
	for _, v := range self.NeedSyn {
		info, ok := self.getFightInfoCache(v)
		if !ok || info.FightInfoCacheDB == nil || info.FightInfoCacheDB.GetFightInfo() == nil {
			continue
		}
		simpleInfo := new(JS_SimpleInfo)
		simpleInfo.Rankid = info.FightInfoCacheDB.GetFightInfo().Rankid
		simpleInfo.Uid = info.FightInfoCacheDB.GetFightInfo().Uid
		simpleInfo.Uname = info.FightInfoCacheDB.GetFightInfo().Uname
		simpleInfo.UnionName = info.FightInfoCacheDB.GetFightInfo().UnionName
		simpleInfo.UnionId = info.FightInfoCacheDB.GetFightInfo().UnionId
		simpleInfo.Camp = info.FightInfoCacheDB.GetFightInfo().Camp
		simpleInfo.Level = info.FightInfoCacheDB.GetFightInfo().Level
		simpleInfo.Vip = info.FightInfoCacheDB.GetFightInfo().Vip
		simpleInfo.Deffight = info.FightInfoCacheDB.GetFightInfo().Deffight
		simpleInfo.FightTeam = info.FightInfoCacheDB.GetFightInfo().FightTeam
		simpleInfo.Iconid = info.FightInfoCacheDB.GetFightInfo().Iconid
		simpleInfo.Portrait = info.FightInfoCacheDB.GetFightInfo().Portrait
		simpleInfo.Title = info.FightInfoCacheDB.GetFightInfo().Title
		simpleInfo.Server = info.FightInfoCacheDB.GetFightInfo().Server
		//simpleInfo.Score = info.FightInfoCacheDB.fightInfo.Score
		simpleInfo.Param = info.FightInfoCacheDB.GetFightInfo().Param
		fightInfoList = append(fightInfoList, simpleInfo)
	}
	//self.Locker.RUnlock()

	return fightInfoList
}

func (self *ChampionMgr) DoLike(targetUid int64) bool {
	self.Locker.Lock()
	defer self.Locker.Unlock()

	if self.ChampionInfo.DataHistoryInfo != nil {
		for _, v := range self.ChampionInfo.DataHistoryInfo.TopRank {
			if v.Uid == targetUid {
				v.Score++
				return true
			}
		}
	}

	//player.SendErr(fmt.Sprintf(csvs.GetText("STR_ARR_DO_LIKE", player.GetDataInt(core.PLAYER_DATA_LANGUAGE)), csvs.MOD_CHAMPION_DO_LIKE_ERROR_CODE))
	return false
}

func (self *ChampionMgr) GetBetInfo(uid int64) *ChampionBetInfo {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	betInfo, ok := self.ChampionInfo.MapBetInfo[uid]
	if !ok {
		betInfo = new(ChampionBetInfo)
		betInfo.Uid = uid
		self.ChampionInfo.MapBetInfo[uid] = betInfo
	}
	return betInfo
}

func (self *ChampionMgr) StageCanBetSave() bool {
	switch self.ChampionInfo.Stage {
	case CHAMPION_STAGE_SCORE:
		nowTurn := self.DataScoreInfo.ScoreTurn
		info, ok := self.DataScoreInfo.ScoreMatchInfo[nowTurn]
		if ok {
			return info.ScoreMatchStage == SCOREMATCH_STAGE_BET
		}
	case CHAMPION_STAGE_ELIMINATE:
		nowTurn := self.DataEliminateInfo.EliminateTurn
		info, ok := self.DataEliminateInfo.EliminateMatchInfo[nowTurn]
		if ok {
			return info.EliminateMatchStage == SCOREMATCH_STAGE_BET
		}
	}
	return false
}
func (self *ChampionMgr) GetBetTargetIdSave() *ScoreMatchTeam {
	switch self.ChampionInfo.Stage {
	case CHAMPION_STAGE_SCORE:
		nowTurn := self.DataScoreInfo.ScoreTurn
		info, ok := self.DataScoreInfo.ScoreMatchInfo[nowTurn]
		if ok {
			return info.GetTeam(info.BetTeamId)
		}
	case CHAMPION_STAGE_ELIMINATE:
		nowTurn := self.DataEliminateInfo.EliminateTurn
		info, ok := self.DataEliminateInfo.EliminateMatchInfo[nowTurn]
		if ok {
			return info.GetTeam(info.BetTeamId)
		}
	}
	return nil
}

// 阶段同步
func (self *ChampionMgr) SendSynMessage() {
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return
	}
	nowChampionInfo := self.GetChampionInfo()
	fightInfos := self.GetFightInfoSyn()

	var msg S2C_ChampionZoneSyn
	msg.Cid = CHAMPIONZONE_SYN
	msg.Period = nowChampionInfo.Period
	msg.ActType = nowChampionInfo.ActType
	msg.Stage = nowChampionInfo.Stage
	msg.SignTime = nowChampionInfo.SignTime
	msg.ScoreTime = nowChampionInfo.ScoreTime
	msg.EliminateTime = nowChampionInfo.EliminateTime
	msg.EndTime = nowChampionInfo.EndTime
	msg.NextTime = nowChampionInfo.NextTime
	msg.NextSignTime = nowChampionInfo.NextSignTime
	msg.NextEndTime = nowChampionInfo.NextEndTime
	msg.ScoreSyn = self.GetSynChampionScoreInfo()
	msg.EliminateSyn = self.GetSynChampionEliminateInfo()
	msg.ScoreUserSyn = self.GetChampionScoreUser()
	msg.EliminateUserSyn = self.GetChampionEliminateUser()
	msg.FightInfoList = fightInfos
	msg.TopRank = nowChampionInfo.SliceTopRank
	msg.SendTime = model.TimeServer().Unix()

	msgdata := utils.HF_JtoA(msg)
	if config.GroupType == CHAMPION_GROUP_TYPE_ZONE {
		serverGroupMap := GetServerGroupMgr().GetServerZoneMap()
		for serverid, serverGroup := range serverGroupMap {
			if serverGroup == self.ZoneId {
				core.GetCenterApp().AddEvent(serverid, core.CHAMPION_ZONE_EVENT_SYN, 0,
					0, 0, msgdata)
			}
		}
	} else if config.GroupType == CHAMPION_GROUP_TYPE_GROUP {
		serverGroupMap := GetServerGroupMgr().GetServerGroupMap()
		for serverid, serverGroup := range serverGroupMap {
			if serverGroup == self.ZoneId {
				core.GetCenterApp().AddEvent(serverid, core.CHAMPION_ZONE_EVENT_SYN, 0,
					0, 0, msgdata)
			}
		}
	}

	//core.GetPlayerMgr().BroadcastMsg(msg.Cid, utils.HF_JtoB(&msg))

	self.ClearNeedSyn()
}

func (self *ChampionMgr) ClearNeedSyn() {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	self.NeedSyn = make([]int64, 0)
}

// 这个结构需要过滤 内容过多
func (self *ChampionMgr) GetChampionInfoMsg() *ChampionInfo {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return nil
	}
	rel := new(ChampionInfo)
	rel.Id = self.ChampionInfo.Id
	rel.Period = self.ChampionInfo.Period
	rel.Stage = self.ChampionInfo.Stage
	rel.SignTime = self.ChampionInfo.SignTime
	rel.ScoreTime = self.ChampionInfo.ScoreTime
	rel.EliminateTime = self.ChampionInfo.EliminateTime
	rel.EndTime = self.ChampionInfo.EndTime
	rel.NextTime = self.ChampionInfo.NextTime
	rel.NextSignTime = self.ChampionInfo.NextSignTime
	rel.NextEndTime = self.ChampionInfo.NextEndTime

	//积分赛信息
	rel.DataScoreInfo = new(JS_ChampionScoreInfo)
	rel.DataScoreInfo.ScoreTurn = self.DataScoreInfo.ScoreTurn
	for _, v := range self.DataScoreInfo.ScoreUser {
		scoreUser := new(ScoreTurnUser)
		scoreUser.BattleInfo = make([]int64, 0)
		scoreUser.Uid = v.Uid
		scoreUser.ServerId = v.ServerId
		scoreUser.KeyId = v.KeyId
		scoreUser.WinTimes = v.WinTimes
		scoreUser.Score = v.Score
		scoreUser.ArenaRank = v.ArenaRank
		rel.DataScoreInfo.ScoreUser = append(rel.DataScoreInfo.ScoreUser, scoreUser)
	}
	rel.DataScoreInfo.ScoreMatchInfo = make(map[int]*JS_ScoreMatchInfo)
	for i := 1; i <= config.FirstTurn; i++ {
		info, ok := self.DataScoreInfo.ScoreMatchInfo[i]
		if !ok {
			continue
		}
		msgInfo := self.NewJSScoreMatchInfo(info)
		msgTeamList := make([]*ScoreMatchTeam, 0)
		for _, teamInfo := range msgInfo.ScoreMatchTeam {
			msgTeam := self.CheckMsgBattleInfo(teamInfo)
			if msgTeam != nil {
				msgTeamList = append(msgTeamList, msgTeam)
			}
		}
		msgInfo.ScoreMatchTeam = msgTeamList
		rel.DataScoreInfo.ScoreMatchInfo[info.ScoreTurnId] = msgInfo
	}
	//淘汰赛信息
	rel.DataEliminateInfo = new(JS_ChampionEliminateInfo)
	rel.DataEliminateInfo.EliminateTurn = self.DataEliminateInfo.EliminateTurn
	for _, v := range self.DataEliminateInfo.EliminateUser {
		eliminateUser := new(EliminateUser)
		eliminateUser.Uid = v.Uid
		eliminateUser.KeyId = v.KeyId
		eliminateUser.WinTimes = v.WinTimes
		eliminateUser.Score = v.Score
		eliminateUser.ArenaRank = v.ArenaRank
		rel.DataEliminateInfo.EliminateUser = append(rel.DataEliminateInfo.EliminateUser, eliminateUser)
	}
	rel.DataEliminateInfo.EliminateMatchInfo = make(map[int]*JS_EliminateMatchInfo)
	for i := 1; i <= config.SecondTurn; i++ {
		info, ok := self.DataEliminateInfo.EliminateMatchInfo[i]
		if !ok {
			continue
		}

		msgInfo := self.NewJSEliminateMatchInfo(info)
		msgTeamList := make([]*ScoreMatchTeam, 0)
		for _, teamInfo := range msgInfo.EliminateMatchTeam {
			msgTeam := self.CheckMsgBattleInfo(teamInfo)
			if msgTeam != nil {
				msgTeamList = append(msgTeamList, msgTeam)
			}
		}
		msgInfo.EliminateMatchTeam = msgTeamList
		rel.DataEliminateInfo.EliminateMatchInfo[info.EliminateTurnId] = msgInfo
	}
	rel.SliceTopRank = self.ChampionInfo.SliceTopRank
	rel.DataHistoryInfo = self.ChampionInfo.DataHistoryInfo

	rel.MapScoreTime = make(map[int]int)
	rel.MapScoreTimeAll = make(map[int]int)
	rel.MapEliminateTime = make(map[int]int)
	rel.MapEliminateAll = make(map[int]int)
	for k, v := range self.ScoreTime {
		rel.MapScoreTime[k] = v
	}
	for k, v := range self.ScoreTimeAll {
		rel.MapScoreTimeAll[k] = v
	}
	for k, v := range self.EliminateTime {
		rel.MapEliminateTime[k] = v
	}
	for k, v := range self.EliminateAll {
		rel.MapEliminateAll[k] = v
	}
	return rel
}

func (self *ChampionMgr) GetChampionPeriod() int {
	return self.ChampionInfo.Period
}

func (self *ChampionMgr) NewJSScoreMatchInfo(info *ScoreMatchInfo) *JS_ScoreMatchInfo {
	msgInfo := new(JS_ScoreMatchInfo)
	msgInfo.Period = info.Period
	msgInfo.ScoreTurnId = info.ScoreTurnId
	msgInfo.ScoreMatchStage = info.ScoreMatchStage
	msgInfo.BetTeamId = info.BetTeamId
	msgInfo.BetEnd = info.BetEnd
	msgInfo.BattleEnd = info.BattleEnd
	msgInfo.ScoreUser = info.ScoreUser
	msgInfo.ScoreMatchTeam = info.ScoreMatchTeam
	return msgInfo
}
func (self *ChampionMgr) NewJSEliminateMatchInfo(info *EliminateMatchInfo) *JS_EliminateMatchInfo {
	msgInfo := new(JS_EliminateMatchInfo)
	msgInfo.Period = info.Period
	msgInfo.EliminateTurnId = info.EliminateTurnId
	msgInfo.EliminateMatchStage = info.EliminateMatchStage
	msgInfo.BetTeamId = info.BetTeamId
	msgInfo.BetEnd = info.BetEnd
	msgInfo.BattleEnd = info.BattleEnd
	msgInfo.EliminateUser = info.EliminateUser
	msgInfo.EliminateMatchTeam = info.EliminateMatchTeam
	return msgInfo
}
func (self *ChampionMgr) GetNowTeamId(teamId int) *ScoreMatchTeam {
	self.Locker.RLock()
	defer self.Locker.RUnlock()

	switch self.ChampionInfo.Stage {
	case CHAMPION_STAGE_SCORE:
		matchInfo, ok := self.DataScoreInfo.ScoreMatchInfo[self.DataScoreInfo.ScoreTurn]
		if !ok {
			return nil
		}
		return matchInfo.GetTeam(teamId)
	case CHAMPION_STAGE_ELIMINATE:
		matchInfo, ok := self.DataEliminateInfo.EliminateMatchInfo[self.DataEliminateInfo.EliminateTurn]
		if !ok {
			return nil
		}
		return matchInfo.GetTeam(teamId)
	}
	return nil
}

func (self *ChampionMgr) GetBattleInfoUid(uid int64) []*model.BattleInfo {
	self.Locker.RLock()
	rel := make([]int64, 0)
	relBattleInfo := make([]*model.BattleInfo, 0)
	for _, v := range self.DataScoreInfo.ScoreUser {
		if v.Uid == uid {
			rel = append(rel, v.BattleInfo...)
		}
	}
	for _, v := range self.DataEliminateInfo.EliminateUser {
		if v.Uid == uid {
			rel = append(rel, v.BattleInfo...)
		}
	}
	self.Locker.RUnlock()

	//拿战报
	for _, v := range rel {
		battleNow := self.GetBattleInfoId(v)
		if battleNow == nil {
			continue
		}
		relBattleInfo = append(relBattleInfo, battleNow)
	}

	return relBattleInfo

}

func (self *ChampionMgr) GetBattleInfoId(id int64) *model.BattleInfo {
	battleInfoData, ok := self.MapBattleInfo.Load(id)
	if !ok {
		battleNow := GetBattleInfoRedis(id)
		if battleNow == nil {
			return nil
		} else {
			self.MapBattleInfo.Store(id, battleNow)
			return battleNow
		}
	}
	return battleInfoData.(*model.BattleInfo)
}

func (self *ChampionMgr) GetNowTop() []*ChampionTop {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	return self.ChampionInfo.SliceTopRank
}

func (self *ChampionMgr) UpdateBestRank() {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	return
}

func (self *ChampionMgr) SetEliminateTime() {
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return
	}
	self.ChampionInfo.EliminateTime = model.TimeServer().Unix()
	info, ok := self.DataScoreInfo.ScoreMatchInfo[config.FirstTurn]
	if ok {
		self.ChampionInfo.EliminateTime = info.BattleEnd
	}
}

func (self *ChampionMgr) GetChangeScore(battleInfo *model.BattleInfo, userInfo *model.BattleUserInfo) int {
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return 0
	}

	score := config.ScoreCalStandard
	//战力修正
	fightScoreAdd := int64(0)
	fightDis := userInfo.Fight - self.DataScoreInfo.UserFight
	if self.DataScoreInfo.UserFight > 0 {
		tempFight := self.DataScoreInfo.UserFight * int64(config.ScoreCalStandardFight) / model.PER_BIT
		if tempFight > 0 {
			fightScoreAdd = fightDis / tempFight
		}
		fightLimit := int64(-10)
		if fightScoreAdd < fightLimit {
			fightScoreAdd = fightLimit
		}
		score += int(fightScoreAdd)
	}
	//满血修正
	if config.ScoreCalStandardHp > 0 {
		superAdd := config.ScoreCalStandardSuperWin
		nowHp := 0
		allHp := 0
		for _, v := range userInfo.HeroInfo {
			if v.HeroID > 0 {
				nowHp += v.Hp
				allHp += model.PER_BIT
				break
			}
		}
		superAddCorrect := (allHp - nowHp) / config.ScoreCalStandardHp
		superAdd -= superAddCorrect
		if superAdd < 0 {
			superAdd = 0
		}
		score += superAdd
	}
	//时间修正
	if config.ScoreWinTimeSec > 0 {
		//基准10分
		winTimeAdd := 10
		disTime := int(battleInfo.Time - 10000) //10秒以外
		if disTime < 0 {
			disTime = 0
		}

		timeAddCorrect := disTime / (config.ScoreWinTimeSec * 1000)
		winTimeAdd -= timeAddCorrect
		if winTimeAdd < 0 {
			winTimeAdd = 0
		}
		score += winTimeAdd
	}
	return score
}

func (self *ChampionMgr) CheckMsgBattleInfo(matchTeam *ScoreMatchTeam) *ScoreMatchTeam {
	rel := new(ScoreMatchTeam)
	if matchTeam == nil {
		return rel
	}
	rel.TeamId = matchTeam.TeamId
	rel.AttackUid = matchTeam.AttackUid
	rel.DefenceUid = matchTeam.DefenceUid
	rel.AttackBet = matchTeam.AttackBet
	rel.DefenceBet = matchTeam.DefenceBet
	rel.Random = matchTeam.Random
	rel.BattleId = matchTeam.BattleId
	if rel.BattleId == 0 {
		rel.BattleInfo = make([]*model.BattleInfo, 0)
		return rel
	}
	battleInfo := self.GetBattleInfoId(rel.BattleId)
	if battleInfo == nil {
		rel.BattleInfo = make([]*model.BattleInfo, 0)
		return rel
	}
	rel.BattleInfo = append(rel.BattleInfo, battleInfo)
	return rel
}

func (self *ChampionMgr) GetSynChampionScoreInfo() *JS_ScoreMatchInfo {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	scoreInfo, ok := self.DataScoreInfo.ScoreMatchInfo[self.DataScoreInfo.ScoreTurn]
	if !ok {
		return &JS_ScoreMatchInfo{}
	}
	msgInfo := self.NewJSScoreMatchInfo(scoreInfo)
	msgTeamList := make([]*ScoreMatchTeam, 0)
	for _, teamInfo := range msgInfo.ScoreMatchTeam {
		msgTeam := self.CheckMsgBattleInfo(teamInfo)
		if msgTeam != nil {
			msgTeamList = append(msgTeamList, msgTeam)
		}
	}
	msgInfo.ScoreMatchTeam = msgTeamList
	return msgInfo
}

func (self *ChampionMgr) GetSynChampionEliminateInfo() *JS_EliminateMatchInfo {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	eliminateInfo, ok := self.DataEliminateInfo.EliminateMatchInfo[self.DataEliminateInfo.EliminateTurn]
	if !ok {
		return &JS_EliminateMatchInfo{}
	}
	msgInfo := self.NewJSEliminateMatchInfo(eliminateInfo)
	msgTeamList := make([]*ScoreMatchTeam, 0)
	for _, teamInfo := range msgInfo.EliminateMatchTeam {
		msgTeam := self.CheckMsgBattleInfo(teamInfo)
		if msgTeam != nil {
			msgTeamList = append(msgTeamList, msgTeam)
		}
	}
	msgInfo.EliminateMatchTeam = msgTeamList
	return msgInfo
}

func (self *ChampionAllMgr) GM_GetNowInfo(w http.ResponseWriter, r *http.Request) {
	zoneid := utils.HF_Atoi(r.FormValue("zoneid"))
	actType := utils.HF_Atoi(r.FormValue("acttype"))

	_, ok := self.ChampionTypeInfoMap[actType]
	if ok {
		_, ok1 := self.ChampionTypeInfoMap[actType].ChampionAllMgr[zoneid]
		if ok1 {
			self.ChampionTypeInfoMap[actType].ChampionAllMgr[zoneid].GM_GetNowInfo(w)
		}
	}
}

// GM_GetNowInfo ! 获取当前冠军赛数据 ***************************************************************************************
func (self *ChampionMgr) GM_GetNowInfo(w http.ResponseWriter) {

	nowInfo := self.GetChampionInfoMsg()
	w.Write([]byte(fmt.Sprintf("战斗统计信息: \n")))
	for i := 201; i <= 206; i++ {
		if nowInfo.DataScoreInfo == nil {
			break
		}
		w.Write([]byte(fmt.Sprintf("积分赛第%d轮: \n", i%100)))
		w.Write([]byte(fmt.Sprintf("战斗服生效[%d]/[%d]场: \n", nowInfo.MapScoreTime[i], nowInfo.MapScoreTimeAll[i])))
	}
	w.Write([]byte(fmt.Sprintf("\n")))
	for i := 301; i <= 306; i++ {
		if nowInfo.DataEliminateInfo == nil {
			break
		}
		w.Write([]byte(fmt.Sprintf("淘汰赛第%d轮: \n", i%100)))
		w.Write([]byte(fmt.Sprintf("战斗服生效[%d]/[%d]场: \n", nowInfo.MapEliminateTime[i], nowInfo.MapEliminateAll[i])))
	}
	w.Write([]byte(fmt.Sprintf("\n当前排名: \n")))
	for _, v := range nowInfo.SliceTopRank {
		w.Write([]byte(fmt.Sprintf("排名:%d,玩家:%d,淘汰赛胜场:%d,积分赛积分:%d \n", v.RankPos, v.Uid, v.WinTimes, v.Score)))
	}
}

func (self *ChampionAllMgr) GMInitDataNow(w http.ResponseWriter, r *http.Request) {
	zoneid := utils.HF_Atoi(r.FormValue("zoneid"))
	actType := utils.HF_Atoi(r.FormValue("acttype"))

	_, ok := self.ChampionTypeInfoMap[actType]
	if ok {
		_, ok1 := self.ChampionTypeInfoMap[actType].ChampionAllMgr[zoneid]
		if ok1 {
			self.ChampionTypeInfoMap[actType].ChampionAllMgr[zoneid].GMInitDataNow(w)
		}
	}
}

// GMInitDataNow 立即开始一场冠军赛
func (self *ChampionMgr) GMInitDataNow(w http.ResponseWriter) {
	self.Locker.Lock()
	defer self.Locker.Unlock()
	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return
	}
	self.ChampionInfo.Period += 1
	self.DataScoreInfo = ChampionScoreInfo{}
	self.DataEliminateInfo = ChampionEliminateInfo{}
	self.ChampionInfo.MapBetInfo = make(map[int64]*ChampionBetInfo)
	self.ChampionInfo.SliceTopRank = make([]*ChampionTop, 0)

	nowTime := model.TimeServer()
	currentYear, currentMonth, currentDay := nowTime.Date()
	currentLocation := nowTime.Location()

	self.ChampionInfo.SignTime = nowTime.Unix()
	self.ChampionInfo.Stage = CHAMPION_STAGE_INVALID
	self.ChampionInfo.ScoreTime = self.ChampionInfo.SignTime + config.StartTime
	self.ChampionInfo.EndTime = self.ChampionInfo.SignTime + config.EndTime
	self.InitBattleInfoSafe()

	//根据开始时间 生成预告时间

	nextJudgeTime := time.Unix(self.ChampionInfo.SignTime, 0)

	nextJudgeTime = nextJudgeTime.AddDate(0, 0, 1)
	currentYear, currentMonth, currentDay = nextJudgeTime.Date()
	currentLocation = nextJudgeTime.Location()
	self.ChampionInfo.NextTime = time.Date(currentYear, currentMonth, currentDay, 19, 55, 0, 0, currentLocation).Unix()

	nextJudgeIndex := int64(nextJudgeTime.Weekday())
	nextDis := int64(7)
	for _, v := range GetServerGroupMgr().TimeResetConfigArr {
		if v.System != config.TimeReset {
			continue
		}
		for _, index := range v.Time {
			if index == 0 {
				continue
			}
			indexDis := (index - nextJudgeIndex + 7) % 7
			if indexDis < nextDis {
				nextDis = indexDis
			}
		}
	}
	self.ChampionInfo.NextTime += nextDis * utils.DAY_SECS
	self.ChampionInfo.NextSignTime = self.ChampionInfo.NextTime + config.StartTime
	self.ChampionInfo.NextEndTime = self.ChampionInfo.NextTime + config.EndTime
	self.ScoreTime = make(map[int]int)
	self.ScoreTimeAll = make(map[int]int)
	self.EliminateTime = make(map[int]int)
	self.EliminateAll = make(map[int]int)
	self.Save()

	w.Write([]byte(fmt.Sprintf("冠军赛即将开始: \n")))
	w.Write([]byte(fmt.Sprintf("生成时间:%d \n", self.ChampionInfo.SignTime)))
	w.Write([]byte(fmt.Sprintf("积分赛开始时间:%d \n", self.ChampionInfo.ScoreTime)))
	w.Write([]byte(fmt.Sprintf("结束时间:%d \n", self.ChampionInfo.EndTime)))
	return
}

func (self *ChampionMgr) GetFightUpdate(battleId int64) *model.BattleInfo {

	_, ok := self.FightList.Load(battleId)
	if !ok {
		return nil
	}

	//championFightInfo := battleData.(*core.ArenaFightList)
	FightResult := battle.GetFightMgr().GetResult(int64(battleId))
	if FightResult == nil || FightResult.Result == 0 {
		return nil
	}

	//同步战报
	battleInfo := new(model.BattleInfo)
	battleInfo.Id = FightResult.Id
	battleInfo.LevelID = 600001
	attackHeroInfo := []*model.BattleHeroInfo{}
	for i, v := range FightResult.Info[model.POS_ATTACK] {
		level, star, skin, exclusiveLv := 0, 0, 0, 0
		if i < len(FightResult.Fight[model.POS_ATTACK].Heroinfo) {
			level = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Levels
			star = FightResult.Fight[model.POS_ATTACK].Heroinfo[i].Stars
		}
		attackHeroInfo = append(attackHeroInfo, &model.BattleHeroInfo{v.Heroid, level, star, skin, v.Hp, v.Energy, v.Damage, v.TakeDamage, v.Healing, nil, exclusiveLv, nil})
	}
	defendHeroInfo := []*model.BattleHeroInfo{}
	for i, v := range FightResult.Info[model.POS_DEFENCE] {
		level, star, skin, exclusiveLv := 0, 0, 0, 0
		if i < len(FightResult.Fight[model.POS_DEFENCE].Heroinfo) {
			level = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Levels
			star = FightResult.Fight[model.POS_DEFENCE].Heroinfo[i].Stars
		}
		defendHeroInfo = append(defendHeroInfo, &model.BattleHeroInfo{v.Heroid, level, star, skin, v.Hp, v.Energy, v.Damage, v.TakeDamage, v.Healing, nil, exclusiveLv, nil})
	}
	battleInfo.UserInfo[model.POS_ATTACK] = NewBattleUserInfo(FightResult.Fight[model.POS_ATTACK], attackHeroInfo)
	battleInfo.UserInfo[model.POS_DEFENCE] = NewBattleUserInfo(FightResult.Fight[model.POS_DEFENCE], defendHeroInfo)
	battleInfo.Type = core.BATTLE_TYPE_CHAMPIONZONE
	battleInfo.Time = int64(FightResult.Time)
	battleInfo.Random = int64(FightResult.Random)
	//进攻方胜利
	battleInfo.Result = FightResult.Result
	return battleInfo
}

func (self *ChampionAllMgr) GetInfo(actType int, serverid int) (int, string) {
	champion := self.GetChampion(actType, serverid)
	if champion == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	info := champion.GetChampionInfoMsg()
	return 0, utils.HF_JtoA(info)
}
func (self *ChampionAllMgr) GetBetInfo(uid int64, actType int, serverid int) (int, string) {
	champion := self.GetChampion(actType, serverid)
	if champion == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	info := champion.GetChampionBetInfo(uid)
	return 0, utils.HF_JtoA(info)
}
func (self *ChampionAllMgr) DoLike(actType int, serverid int, targetUid int64) int {
	champion := self.GetChampion(actType, serverid)
	if champion == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR
	}
	if !champion.DoLike(targetUid) {
		return RETCODE_DATA_CROSS_PARAM_ERROR
	}
	return 0
}
func (self *ChampionAllMgr) Bet(uid int64, actType int, serverid int, targetUid int64, value int) (int, string) {
	champion := self.GetChampion(actType, serverid)
	if champion == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	info := champion.Bet(uid, targetUid, value, serverid)
	if info == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	return 0, utils.HF_JtoA(info)
}
func (self *ChampionMgr) Bet(uid int64, target int64, value int, serverId int) *ChampionBetInfo {
	self.Locker.RLock()
	defer self.Locker.RUnlock()
	//当前阶段是否可以下注
	if !self.StageCanBetSave() {
		return nil
	}

	config := GetChampionAllMgr().GetChampionSimpleConfig(self.ActType)
	if config == nil {
		return nil
	}

	if value <= 0 || value > config.BetMax {
		return nil
	}

	betInfo, ok := self.ChampionInfo.MapBetInfo[uid]
	if !ok {
		betInfo = new(ChampionBetInfo)
		betInfo.Uid = uid
		self.ChampionInfo.MapBetInfo[uid] = betInfo
	}
	//筹码不足
	if betInfo.NowBet < value {
		return nil
	}
	//已下注
	if betInfo.TargetUid > 0 {
		return nil
	}
	team := self.GetBetTargetIdSave()
	if team == nil {
		return nil
	}
	if team.AttackUid != target && team.DefenceUid != target {
		return nil
	}
	betInfo.TargetUid = target
	betInfo.TargetBet = value
	betInfo.NowBet -= value
	betInfo.ServerId = serverId

	if team.AttackUid == target {
		team.AttackBet += 1
	} else {
		team.DefenceBet += 1
	}
	return betInfo
}
func (self *ChampionAllMgr) GetTeamInfo(uid int64, actType int, serverid int, value int) (int, string, string, string) {
	champion := self.GetChampion(actType, serverid)
	if champion == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", "", ""
	}
	teaminfo, attackFightInfo, defenceFightInfo := champion.GetTeamInfo(value)
	if teaminfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, "", "", ""
	}
	return 0, utils.HF_JtoA(teaminfo), utils.HF_JtoA(attackFightInfo), utils.HF_JtoA(defenceFightInfo)
}
func (self *ChampionMgr) GetTeamInfo(teamId int) (*ScoreMatchTeam, *model.JS_FightInfo, *model.JS_FightInfo) {

	teamInfo := self.GetNowTeamId(teamId)
	if teamInfo == nil {
		return nil, nil, nil
	}
	teamInfo = self.CheckMsgBattleInfo(teamInfo)
	attackFightInfo := self.GetFightInfo(teamInfo.AttackUid)
	if attackFightInfo == nil {
		attackFightInfo = new(model.JS_FightInfo)
		attackFightInfo.Init()
	}
	defenceFightInfo := self.GetFightInfo(teamInfo.DefenceUid)
	if defenceFightInfo == nil {
		defenceFightInfo = new(model.JS_FightInfo)
		defenceFightInfo.Init()
	}
	return teamInfo, attackFightInfo, defenceFightInfo
}

func (self *ChampionAllMgr) GetNowTeamId(actType int, serverid int, value int) (int, string) {
	champion := self.GetChampion(actType, serverid)
	if champion == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	teaminfo := champion.GetNowTeamId(value)
	if teaminfo == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	return 0, utils.HF_JtoA(teaminfo)
}

func (self *ChampionAllMgr) GetBattleInfoUid(uid int64, actType int, serverid int) (int, string) {
	champion := self.GetChampion(actType, serverid)
	if champion == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	battleInfo := champion.GetBattleInfoUid(uid)
	return 0, utils.HF_JtoA(battleInfo)
}

func (self *ChampionAllMgr) GetBattleRecord(actType int, serverid int, fightId int64) (int, string) {
	champion := self.GetChampion(actType, serverid)
	if champion == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	_, battleRecord := self.GetRecord(fightId)
	return 0, battleRecord
}

func (self *ChampionAllMgr) GetRecord(id int64) (int, string) {
	var battleRecord model.BattleRecord
	value, flag, err := db.HGetRedisEx(BATTLE_RECORD_CHAMPION, id, fmt.Sprintf("%d", id))
	if err != nil || !flag {
		return 0, ""
	}
	err1 := json.Unmarshal([]byte(value), &battleRecord)
	if err1 != nil {
		return 0, ""
	}
	if battleRecord.Id == 0 {
		return 0, ""
	}
	return 0, utils.HF_JtoA(battleRecord)
}

func (self *ChampionAllMgr) GetPeriod(actType int, serverid int) (int, string) {
	champion := self.GetChampion(actType, serverid)
	if champion == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	period := champion.GetChampionPeriod()
	return 0, utils.HF_JtoA(period)
}
func (self *ChampionAllMgr) GetNowTop(actType int, serverid int) (int, string) {
	champion := self.GetChampion(actType, serverid)
	if champion == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	nowTop := champion.GetNowTop()
	return 0, utils.HF_JtoA(nowTop)
}
func (self *ChampionAllMgr) GetFightInfo(actType int, serverid int, targetUid int64) (int, string) {
	champion := self.GetChampion(actType, serverid)
	if champion == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	fightInfo := champion.GetFightInfo(targetUid)
	return 0, utils.HF_JtoA(fightInfo)
}
func (self *ChampionAllMgr) GetFightUpdate(actType, serverid int, battleId int64) (int, string) {
	champion := self.GetChampion(actType, serverid)
	if champion == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	fightInfo := champion.GetFightUpdate(battleId)
	return 0, utils.HF_JtoA(fightInfo)
}
func (self *ChampionAllMgr) GetSimpleInfo(actType int, serverid int) (int, string) {
	champion := self.GetChampion(actType, serverid)
	if champion == nil {
		return RETCODE_DATA_CROSS_PARAM_ERROR, ""
	}
	fightInfo := champion.GetSimpleInfo()
	return 0, utils.HF_JtoA(fightInfo)
}
