// Code generated by protoc-gen-go.
// source: chat.proto
// DO NOT EDIT!

/*
Package protobuf is a generated protocol buffer package.

It is generated from these files:
	chat.proto
	login.proto
	msgbase.proto

It has these top-level messages:
	Chat
	Login
	MsgBase
*/
package network

import "github.com/golang/protobuf/proto"
import "fmt"
import "math"

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
const _ = proto.ProtoPackageIsVersion1

type Chat struct {
	Channel          *int32  `protobuf:"varint,1,req,name=channel" json:"channel,omitempty"`
	Content          []byte  `protobuf:"bytes,2,opt,name=content" json:"content,omitempty"`
	Name             *string `protobuf:"bytes,3,opt,name=name" json:"name,omitempty"`
	Medianame        *string `protobuf:"bytes,4,opt,name=medianame" json:"medianame,omitempty"`
	XXX_unrecognized []byte  `json:"-"`
}

func (m *Chat) Reset()                    { *m = Chat{} }
func (m *Chat) String() string            { return proto.CompactTextString(m) }
func (*Chat) ProtoMessage()               {}
func (*Chat) Descriptor() ([]byte, []int) { return fileDescriptor0, []int{0} }

func (m *Chat) GetChannel() int32 {
	if m != nil && m.Channel != nil {
		return *m.Channel
	}
	return 0
}

func (m *Chat) GetContent() []byte {
	if m != nil {
		return m.Content
	}
	return nil
}

func (m *Chat) GetName() string {
	if m != nil && m.Name != nil {
		return *m.Name
	}
	return ""
}

func (m *Chat) GetMedianame() string {
	if m != nil && m.Medianame != nil {
		return *m.Medianame
	}
	return ""
}

func init() {
	proto.RegisterType((*Chat)(nil), "protobuf.Chat")
}

var fileDescriptor0 = []byte{
	// 106 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x09, 0x6e, 0x88, 0x02, 0xff, 0xe2, 0xe2, 0x4a, 0xce, 0x48, 0x2c,
	0xd1, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0xe2, 0x00, 0x53, 0x49, 0xa5, 0x69, 0x4a, 0x9e, 0x5c,
	0x2c, 0xce, 0x40, 0x71, 0x21, 0x7e, 0x2e, 0x76, 0xa0, 0x7c, 0x5e, 0x5e, 0x6a, 0x8e, 0x04, 0xa3,
	0x02, 0x93, 0x06, 0x2b, 0x58, 0x20, 0x3f, 0xaf, 0x24, 0x35, 0xaf, 0x44, 0x82, 0x49, 0x81, 0x51,
	0x83, 0x47, 0x88, 0x87, 0x8b, 0x25, 0x2f, 0x31, 0x37, 0x55, 0x82, 0x19, 0xc8, 0xe3, 0x14, 0x12,
	0xe4, 0xe2, 0xcc, 0x4d, 0x4d, 0xc9, 0x4c, 0x04, 0x0b, 0xb1, 0x80, 0x84, 0x00, 0x01, 0x00, 0x00,
	0xff, 0xff, 0x64, 0xcb, 0x1e, 0x63, 0x61, 0x00, 0x00, 0x00,
}
